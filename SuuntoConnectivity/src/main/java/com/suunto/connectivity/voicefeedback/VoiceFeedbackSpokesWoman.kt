package com.suunto.connectivity.voicefeedback

import com.stt.android.utils.isPositive
import com.suunto.algorithms.data.HeartRate.Companion.hz
import javax.inject.Inject

/**
 *  voice feedback build text logic and uses tts engine to speech
 */
class VoiceFeedbackSpokesWoman @Inject constructor() {
    @Inject
    lateinit var voiceFeedback: IVoiceFeedback

    fun start() {
        voiceFeedback.start()
    }

    fun stop() {
        voiceFeedback.stop()
    }

    /**
     * say heart rate data when the heart rate changed
     * @param heartRateMdsResponse HeartRateMdsResponse
     */
    fun sayHeartRateChanged(heartRateMdsResponse: HeartRateMdsResponse) {
        if (heartRateMdsResponse.heartRate.isPositive()) {
            voiceFeedback.sayCurrentHeartRate(heartRateMdsResponse.heartRate.hz)
            if (heartRateMdsResponse.zone.isPositive()) {
                voiceFeedback.sayHeartRateZone(heartRateMdsResponse.zone)
            }
        }
    }

    /**
     * say autoLap data when the autoLaps is triggered
     * @param autoLapMdsResponse AutoLapMdsResponse
     *  lap mode == 1 lap distance speech
     *  lap mode == 2 lap time speech
     *  running: lap number — lap distance/lap time — average heart rate — lap pace
     swimming: lap number — lap distance/lap time — total distance — average pace
     Sailing: lap number — lap distance/lap time — total distance — lap pace
     other indoor activities: lap number — lap distance/lap time — average heart rate — max heart rate
     other activities: lap number — lap distance/lap time — total distance — lap pace
     */
    fun sayLap(autoLapMdsResponse: AutoLapMdsResponse) {
        sayLapTop(autoLapMdsResponse)
        sayLapCenter(autoLapMdsResponse)
        sayLapBottom(autoLapMdsResponse)
    }

    private fun sayLapTop(autoLapMdsResponse: AutoLapMdsResponse) {
        when (autoLapMdsResponse.lapMode) {
            LAP_MODE_DISTANCE -> {
                // Distance lap, for example this could be configured to trigger every 1km
                if (autoLapMdsResponse.lapNumber.isPositive()) {
                    voiceFeedback.sayLapNumber(autoLapMdsResponse.lapNumber)
                }
                if (autoLapMdsResponse.splitTime.isPositive()) {
                    voiceFeedback.sayLapTime(autoLapMdsResponse.splitTime)
                }
            }
            LAP_MODE_DURATION -> {
                // Duration lap, for example this could be configured to trigger every 5min
                if (autoLapMdsResponse.lapNumber.isPositive()) {
                    voiceFeedback.sayLapNumber(autoLapMdsResponse.lapNumber)
                }
                if (autoLapMdsResponse.isIndoorActivities() &&
                    !shouldSayLapDistanceForActivityType(autoLapMdsResponse.lapActivityID) &&
                    autoLapMdsResponse.splitTime.isPositive()
                ) {
                    voiceFeedback.sayLapTime(autoLapMdsResponse.splitTime)
                } else if (autoLapMdsResponse.splitDistance.isPositive()) {
                    voiceFeedback.saySplitDistance(
                        isSwimmingSeries = voiceFeedback.isSwimmingSeries(autoLapMdsResponse.lapActivityID),
                        isSailing = voiceFeedback.isSailingSeries(autoLapMdsResponse.lapActivityID),
                        distance = autoLapMdsResponse.splitDistance,
                        unit = autoLapMdsResponse.measurementUnit ?: 0
                    )
                }
            }
        }
    }

    private fun shouldSayLapDistanceForActivityType(lapActivityID: Int?): Boolean {
        return voiceFeedback.isSwimmingSeries(lapActivityID) ||
            voiceFeedback.isSailingSeries(lapActivityID) ||
            voiceFeedback.isTrailRunning(lapActivityID) ||
            voiceFeedback.isTreadmill(lapActivityID)
    }

    private fun sayLapCenter(autoLapMdsResponse: AutoLapMdsResponse) {
        when {
            autoLapMdsResponse.lapPODPowerSW == true -> {
                if (autoLapMdsResponse.splitPower.isPositive()) {
                    voiceFeedback.sayPower(autoLapMdsResponse.splitPower)
                }
            }
            voiceFeedback.isRunningSeries(autoLapMdsResponse.lapActivityID) -> {
                if (autoLapMdsResponse.lapPODHeartRateSW == true) {
                    if (autoLapMdsResponse.splitAverageHeartRate.isPositive()) {
                        voiceFeedback.sayLapHeartRate(autoLapMdsResponse.splitAverageHeartRate.hz)
                    }
                }
                if (autoLapMdsResponse.lapPODHeartRateSW == false) {
                    if (autoLapMdsResponse.splitCadence.isPositive()) {
                        voiceFeedback.sayAvgCadence(autoLapMdsResponse.splitCadence)
                    }
                }
            }

            voiceFeedback.isSwimmingSeries(autoLapMdsResponse.lapActivityID) -> {
                if (autoLapMdsResponse.splitAverageSpeed.isPositive()) {
                    voiceFeedback.sayLapPace(
                        isSwimmingSeries = true,
                        lapSpeed = autoLapMdsResponse.splitAverageSpeed,
                        unit = autoLapMdsResponse.measurementUnit ?: 0
                    )
                }
            }

            voiceFeedback.isSailingSeries(autoLapMdsResponse.lapActivityID) -> {
                if (autoLapMdsResponse.splitAverageSpeed.isPositive()) {
                    voiceFeedback.sayLapSpeed(autoLapMdsResponse.splitAverageSpeed, autoLapMdsResponse.measurementUnit ?: 0)
                }
            }

            autoLapMdsResponse.isIndoorActivities() -> {
                if (autoLapMdsResponse.splitAverageHeartRate.isPositive()) {
                    voiceFeedback.sayLapHeartRate(autoLapMdsResponse.splitAverageHeartRate.hz)
                }
            }

            else -> {
                if (autoLapMdsResponse.splitAverageSpeed.isPositive()) {
                    voiceFeedback.sayLapSpeed(autoLapMdsResponse.splitAverageSpeed, autoLapMdsResponse.measurementUnit ?: 0)
                }
            }
        }
    }

    private fun sayLapBottom(autoLapMdsResponse: AutoLapMdsResponse) {
        when {
            voiceFeedback.isVerticalSports(autoLapMdsResponse.lapActivityID) && autoLapMdsResponse.totalAscent.isPositive() && autoLapMdsResponse.totalAscent > 50 -> {
                if (autoLapMdsResponse.splitAscent.isPositive() && autoLapMdsResponse.splitDescent.isPositive()) {
                    if (autoLapMdsResponse.splitAscent + 5 < autoLapMdsResponse.splitDescent) {
                        voiceFeedback.sayDescent(autoLapMdsResponse.splitDescent, autoLapMdsResponse.measurementUnit ?: 0)
                    } else {
                        voiceFeedback.sayAscent(autoLapMdsResponse.splitAscent, autoLapMdsResponse.measurementUnit ?: 0)
                    }
                }
            }

            autoLapMdsResponse.lapMode == LAP_MODE_DISTANCE -> {
                if (autoLapMdsResponse.duration.isPositive()) {
                    voiceFeedback.sayTotalTime(autoLapMdsResponse.duration)
                }
            }

            autoLapMdsResponse.lapMode == LAP_MODE_DURATION -> {
                when {
                    voiceFeedback.isRunningSeries(autoLapMdsResponse.lapActivityID) -> {
                        if (autoLapMdsResponse.splitAverageSpeed.isPositive()) {
                            voiceFeedback.sayLapPace(
                                isSwimmingSeries = false,
                                lapSpeed = autoLapMdsResponse.splitAverageSpeed,
                                unit = autoLapMdsResponse.measurementUnit ?: 0
                            )
                        }
                    }
                    autoLapMdsResponse.lapPODHeartRateSW == true && autoLapMdsResponse.lapGpsIntervalSW == 0 -> {
                        if (autoLapMdsResponse.splitMaxHeartRate.isPositive()) {
                            voiceFeedback.sayMaxHeartRate(autoLapMdsResponse.splitMaxHeartRate.hz)
                        }
                    }
                    else -> {
                        if (autoLapMdsResponse.duration.isPositive()) {
                            voiceFeedback.sayTotalTime(autoLapMdsResponse.duration)
                        }
                    }
                }
            }
        }
    }

    fun sayStart() {
        voiceFeedback.sayStarted()
    }
}
