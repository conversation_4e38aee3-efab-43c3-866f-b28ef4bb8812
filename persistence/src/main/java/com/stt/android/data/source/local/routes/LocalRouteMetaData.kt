package com.stt.android.data.source.local.routes

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class LocalRouteMetaData(
    @Json(name = "locationMetas") val locationMetas: Map<Int, LocalRouteActivityMetadata?>
)

@JsonClass(generateAdapter = true)
data class LocalRouteActivityMetadata(
    @Json(name = "totalDistance") val totalDistance: Double,
    @J<PERSON>(name = "ascent") val ascent: Double,
    @Json(name = "descent") val descent: Double,
    @<PERSON><PERSON>(name = "duration") val duration: Long,
    @<PERSON><PERSON>(name = "popularity") val popularity: Double,
    @J<PERSON>(name = "averageSpeed") val averageSpeed: Double,
    @J<PERSON>(name = "routeImages") val routeImages: List<String>?,
    @Json(name = "locales") val locales: Map<String, LocalRouteLocalization>?
)

@JsonClass(generateAdapter = true)
data class LocalRouteLocalization(
    @Json(name = "name") val name: String,
    @Json(name = "startAddress") val startAddress: String,
    @<PERSON><PERSON>(name = "endAddress") val endAddress: String
)
