<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <item
        android:id="@+id/aboutDevice"
        android:title="@string/about_device" />
    <item
        android:id="@+id/infoOutline"
        android:icon="@drawable/ic_info_outline"
        android:title="@string/dual_device_connection_learn_how_text"
        app:showAsAction="always" />
    <item
        android:id ="@+id/find_headphone"
        android:icon = "@drawable/icon_find_headphone"
        app:showAsAction="ifRoom"
        android:title=""
        />
</menu>
