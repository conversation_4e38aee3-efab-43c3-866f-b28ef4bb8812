<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/fragment_redirect">

    <fragment
        android:id="@+id/fragment_redirect"
        android:name="com.suunto.headset.ui.RedirectFragment" />

    <fragment
        android:id="@+id/fragment_device_pairing"
        android:name="com.suunto.headset.ui.DevicePairingFragment"
        tools:layout="@layout/fragment_headset_pairing" />

    <fragment
        android:id="@+id/fragment_device_pairing_failed"
        android:name="com.suunto.headset.ui.DevicePairingFailedFragment"
        tools:layout="@layout/fragment_headset_pairing_failed" />

    <fragment
        android:id="@+id/fragment_body_sensing"
        android:name="com.suunto.headset.ui.BodySensingFragment"
        tools:layout="@layout/fragment_body_sensing" />
    <fragment
        android:id="@+id/fragment_dual_device"
        android:name="com.suunto.headset.ui.DualDeviceFragment"
        tools:layout="@layout/fragment_headset_dual_connect" />
    <fragment
        android:id="@+id/fragment_guide"
        android:name="com.suunto.headset.ui.GuideFragment"
        tools:layout="@layout/fragment_guide" />
    <fragment
        android:id="@+id/fragment_indicator"
        android:name="com.suunto.headset.ui.IndicatorFragment"
        tools:layout="@layout/fragment_indicator" />
    <fragment
        android:id="@+id/fragment_instruction"
        android:name="com.suunto.headset.ui.InstructionFragment"
        tools:layout="@layout/fragment_instuction" />
    <fragment
        android:id="@+id/fragment_jump_bluetooth_setting"
        android:name="com.suunto.headset.ui.JumpBluetoothSettingFragment"
        tools:layout="@layout/fragment_jump_bluetooth_setting" />
    <fragment
        android:id="@+id/fragment_headset_setting"
        android:name="com.suunto.headset.ui.HeadsetSettingFragment"
        tools:layout="@layout/fragment_headset_settings" />
    <fragment
        android:id="@+id/fragment_device_pair_list"
        android:name="com.suunto.headset.ui.DevicePairListFragment"
        tools:layout="@layout/fragment_headset_pair_list" />
    <fragment
        android:id="@+id/fragment_sound_mode"
        android:name="com.suunto.headset.ui.SoundModeFragment"
        tools:layout="@layout/fragment_sound_mode" />
    <fragment
        android:id="@+id/fragment_device_info"
        android:name="com.suunto.headset.ui.DeviceInfoFragment"
        tools:layout="@layout/fragment_headset_info" />
    <fragment
        android:id="@+id/fragment_dual_device_guide"
        android:name="com.suunto.headset.ui.DualDeviceGuideFragment"
        tools:layout="@layout/fragment_dual_device_connect_guide" />
    <fragment
        android:id="@+id/fragment_ota_version_available"
        android:name="com.suunto.headset.ui.OTAVersionAvailableFragment"
        tools:layout="@layout/fragment_ota_version_available" />
    <fragment
        android:id="@+id/fragment_ota_version_latest"
        android:name="com.suunto.headset.ui.OTALatestFragment"
        tools:layout="@layout/fragment_ota_version_latest" />
    <fragment
        android:id="@+id/fragment_ota_version_check"
        android:name="com.suunto.headset.ui.OTACheckingFragment"
        tools:layout="@layout/fragment_ota_version_check" />
    <fragment
        android:id="@+id/fragment_customize_button"
        android:name="com.suunto.headset.ui.ButtonCustomizationFragment"
        tools:layout="@layout/fragment_customize_button" />
    <fragment
        android:id="@+id/neckMobilityAssessment"
        android:name="com.suunto.headset.ui.NeckMobilityAssessmentFragment"
        android:label="NeckMobilityAssessment" />
    <fragment
        android:id="@+id/neckMovementMonitoring"
        android:name="com.suunto.headset.ui.NeckMovementMonitoringFragment"
        android:label="NeckMovementMonitoring" />
    <fragment
        android:id="@+id/jump_assessment"
        android:name="com.suunto.headset.ui.JumpAssessmentFragment"
        android:label="JumpAssessment" />
    <fragment
        android:id="@+id/sport_switch"
        android:name="com.suunto.headset.ui.SportsSwitchFragment"
        android:label="SportSwitch" />
    <fragment
        android:id="@+id/su07_dual_device_guide"
        android:name="com.suunto.headset.ui.Su07DualDeviceGuideFragment"
        android:label="Su07DualDeviceGuide" />
    <fragment
        android:id="@+id/metronome_setting"
        android:name="com.suunto.headset.ui.ows.MetronomeFragment"
        android:label="MetronomeSetting"/>
    <fragment
        android:id="@+id/fragment_headset_setting_v2"
        android:name="com.suunto.headset.settingv2.HeadsetSettingFragment"
        tools:layout="@layout/fragment_headset_settings_v2" />
    <fragment
        android:id="@+id/find_headphone"
        android:name="com.suunto.headset.ui.ows.FindHeadphoneFragment"
        android:label="FindHeadphone" />
</navigation>
