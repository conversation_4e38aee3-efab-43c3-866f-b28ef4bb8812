<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?suuntoItemBackgroundColor">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="0dp"
        app:elevation="0dp"
        app:layout_anchor="@+id/device_setting_scrollview"
        app:layout_behavior="com.stt.android.watch.VerticalFadeBehavior">

        <include
            android:id="@+id/my_headset"
            layout="@layout/view_my_headset"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/device_setting_scrollview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="270dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/layout_headset_device_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintTop_toTopOf="parent">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:id="@+id/headset_connection_state"
                            style="@style/Body.Larger.Bold"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:padding="@dimen/size_spacing_smaller"
                            android:textAllCaps="true"
                            android:textColor="@color/settings_item_color"
                            android:visibility="gone"
                            tools:text="@string/connect"
                            tools:visibility="visible" />

                        <include
                            android:id="@+id/layout_power"
                            layout="@layout/layout_headphone_power"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:visibility="gone"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:visibility="visible" />
                    </FrameLayout>


                    <LinearLayout
                        android:id="@+id/workout_tracking_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/headset_workout_tracking"
                            style="@style/Body.Larger.Bold"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/size_spacing_smaller"
                            android:layout_marginEnd="@dimen/size_spacing_smaller"
                            android:gravity="center"
                            android:lineSpacingMultiplier="1.3"
                            android:paddingBottom="@dimen/size_spacing_medium"
                            android:text="@string/tracking_workout"
                            android:textAllCaps="true"
                            android:textColor="@color/settings_item_color" />

                        <TextView
                            android:id="@+id/headset_workout_tracking_tips"
                            style="@style/Body.Medium"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/size_spacing_smaller"
                            android:layout_marginEnd="@dimen/size_spacing_smaller"
                            android:gravity="center"
                            android:lineSpacingMultiplier="1.3"
                            android:paddingBottom="@dimen/size_spacing_medium"
                            android:text="@string/tracking_workout_tips"
                            android:textColor="@color/settings_item_color" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/headset_reconnect"
                        style="@style/Body.Medium.Bold"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:paddingBottom="@dimen/size_spacing_smaller"
                        android:text="@string/reconnect"
                        android:textAllCaps="true"
                        android:textColor="@color/change_email_button_text_disabled"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <LinearLayout
                        android:id="@+id/sync_head_data"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/white"
                        android:orientation="vertical"
                        android:visibility="gone"
                        app:layout_constraintTop_toTopOf="@id/layout_power"
                        tools:visibility="visible">

                        <TextView
                            style="@style/Body.Larger.Bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginStart="@dimen/size_spacing_smaller"
                            android:layout_marginEnd="@dimen/size_spacing_smaller"
                            android:lineSpacingMultiplier="1.3"
                            android:paddingBottom="@dimen/size_spacing_medium"
                            android:text="@string/data_sync"
                            android:textAllCaps="true"
                            android:textColor="@color/settings_item_color" />
                    </LinearLayout>
                </LinearLayout>

                <include
                    android:id="@+id/layout_sport_type"
                    layout="@layout/item_sports_switch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toBottomOf="@+id/layout_headset_device_info" />

                <TextView
                    android:id="@+id/headset_setting_body_sensing_label"
                    style="@style/Body.Larger.Bold"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingTop="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    android:text="@string/head_movement_control_title"
                    android:textColor="@color/color_body_text_disabled"
                    app:layout_constraintEnd_toStartOf="@id/headset_setting_body_sensing"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layout_sport_type" />

                <TextView
                    android:id="@+id/headset_setting_body_sensing_info"
                    style="@style/Body.Medium"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    android:paddingBottom="@dimen/size_spacing_medium"
                    android:text="@string/body_sensing_summary"
                    android:textColor="@color/color_body_text_disabled"
                    app:layout_constraintEnd_toStartOf="@id/headset_setting_body_sensing"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_body_sensing_label" />

                <TextView
                    android:id="@+id/headset_setting_body_sensing"
                    style="@style/Body.Small.Bold.AllCaps"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:gravity="center"
                    android:padding="@dimen/size_spacing_medium"
                    android:textColor="@color/change_email_button_text_disabled"
                    android:textSize="@dimen/text_size_medium"
                    app:layout_constraintBottom_toBottomOf="@id/headset_setting_body_sensing_info"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/headset_setting_body_sensing_label" />

                <View
                    android:id="@+id/divider"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_body_sensing_info" />

                <TextView
                    android:id="@+id/headset_setting_dual_device_connection_label"
                    style="@style/Body.Larger.Bold"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingTop="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    android:text="@string/dual_device_connection_title"
                    android:textColor="@color/color_body_text_disabled"
                    app:layout_constraintEnd_toStartOf="@id/headset_setting_dual_device_connection"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider" />

                <TextView
                    android:id="@+id/headset_setting_dual_device_connection_info"
                    style="@style/Body.Medium"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    android:paddingBottom="@dimen/size_spacing_medium"
                    android:text="@string/dual_device_connection_summary"
                    android:textColor="@color/color_body_text_disabled"
                    app:layout_constraintEnd_toStartOf="@id/headset_setting_dual_device_connection"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_dual_device_connection_label" />

                <TextView
                    android:id="@+id/headset_setting_dual_device_connection"
                    style="@style/Body.Small.Bold.AllCaps"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:gravity="center"
                    android:padding="@dimen/size_spacing_medium"
                    android:textColor="@color/change_email_button_text_disabled"
                    android:textSize="@dimen/text_size_medium"
                    app:layout_constraintBottom_toBottomOf="@id/headset_setting_dual_device_connection_info"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/headset_setting_dual_device_connection_label" />

                <View
                    android:id="@+id/divider2"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_dual_device_connection_info" />

                <TextView
                    android:id="@+id/headset_setting_sound_mode_label"
                    style="@style/Body.Larger.Bold"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingTop="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    android:text="@string/sound_mode_title"
                    android:textColor="@color/color_body_text_disabled"
                    app:layout_constraintEnd_toStartOf="@id/headset_setting_sound_mode"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider2" />

                <TextView
                    android:id="@+id/headset_setting_sound_mode_info"
                    style="@style/Body.Medium"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    android:paddingBottom="@dimen/size_spacing_medium"
                    android:text="@string/sound_mode_summary"
                    android:textColor="@color/color_body_text_disabled"
                    app:layout_constraintEnd_toStartOf="@id/headset_setting_sound_mode"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_sound_mode_label" />

                <TextView
                    android:id="@+id/headset_setting_sound_mode"
                    style="@style/Body.Small.Bold.AllCaps"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:gravity="center"
                    android:padding="@dimen/size_spacing_medium"
                    android:textColor="@color/change_email_button_text_disabled"
                    android:textSize="@dimen/text_size_medium"
                    app:layout_constraintBottom_toBottomOf="@id/headset_setting_sound_mode_info"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/headset_setting_sound_mode_label" />

                <View
                    android:id="@+id/divider_sound_mode"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_sound_mode_info" />

                <TextView
                    android:id="@+id/headset_setting_music_mode_label"
                    style="@style/Body.Larger.Bold"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingTop="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    android:text="@string/music_mode"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider_sound_mode" />

                <TextView
                    android:id="@+id/headset_setting_music_mode_info"
                    style="@style/Body.Medium"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    android:paddingBottom="@dimen/size_spacing_medium"
                    android:text="@string/music_mode_introduction"
                    android:textColor="@color/color_body_text_disabled"
                    app:layout_constraintEnd_toStartOf="@+id/headset_setting_music_mode"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_music_mode_label" />

                <TextView
                    android:id="@+id/headset_setting_music_mode"
                    style="@style/Body.Small.Bold.AllCaps"
                    android:layout_marginStart="@dimen/size_spacing_xsmall"
                    android:layout_marginEnd="@dimen/size_spacing_smaller"
                    android:textColor="@color/change_email_button_text_disabled"
                    android:textSize="@dimen/text_size_medium"
                    app:layout_constraintBottom_toBottomOf="@+id/headset_setting_music_mode_info"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/headset_setting_music_mode_info"
                    app:layout_constraintTop_toTopOf="@+id/headset_setting_music_mode_label"
                    tools:text="@string/offline_music_title" />

                <View
                    android:id="@+id/divider_music_mode"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_music_mode_info" />

                <TextView
                    android:id="@+id/headset_setting_button_customization"
                    style="@style/Body.Larger.Bold"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingTop="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    android:text="@string/setting_button_customization_title"
                    android:textColor="@color/color_body_text_disabled"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider_music_mode" />

                <TextView
                    android:id="@+id/headset_setting_button_customization_info"
                    style="@style/Body.Medium"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    android:paddingBottom="@dimen/size_spacing_medium"
                    android:text="@string/setting_button_customization_summary"
                    android:textColor="@color/color_body_text_disabled"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_button_customization" />

                <View
                    android:id="@+id/divider_button_customization"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_button_customization_info" />

                <TextView
                    android:id="@+id/headset_setting_indicator_label"
                    style="@style/Body.Larger.Bold"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingTop="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    android:text="@string/indicator_title"
                    android:textColor="@color/color_body_text_disabled"
                    app:layout_constraintEnd_toStartOf="@id/headset_setting_indicator"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider_button_customization" />

                <TextView
                    android:id="@+id/headset_setting_indicator_info"
                    style="@style/Body.Medium"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_spacing_medium"
                    android:paddingEnd="@dimen/size_spacing_medium"
                    android:paddingBottom="@dimen/size_spacing_medium"
                    android:text="@string/indicator_summary"
                    android:textColor="@color/color_body_text_disabled"
                    app:layout_constraintEnd_toStartOf="@id/headset_setting_indicator"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_indicator_label" />

                <TextView
                    android:id="@+id/headset_setting_indicator"
                    style="@style/Body.Small.Bold.AllCaps"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:gravity="center"
                    android:padding="@dimen/size_spacing_medium"
                    android:textColor="@color/change_email_button_text_disabled"
                    android:textSize="@dimen/text_size_medium"
                    app:layout_constraintBottom_toBottomOf="@id/headset_setting_indicator_info"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/headset_setting_indicator_label" />


                <View
                    android:id="@+id/divider3"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_indicator_info" />

                <include
                    android:id="@+id/layout_neck_move_monitoring"
                    layout="@layout/item_neck_move_monitoring"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toBottomOf="@+id/divider3" />

                <include
                    android:id="@+id/layout_neck_mobility_assessment"
                    layout="@layout/item_neck_mobility_assessment"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toBottomOf="@+id/layout_neck_move_monitoring" />

                <include
                    android:id="@+id/layout_jump_assessment"
                    layout="@layout/item_jump_assessment"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toBottomOf="@+id/layout_neck_mobility_assessment" />

                <TextView
                    android:id="@+id/headset_setting_low_latency_label"
                    style="@style/Body.Larger.Bold"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical"
                    android:padding="@dimen/size_spacing_medium"
                    android:text="@string/low_latency_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/layout_jump_assessment"
                    tools:ignore="UnknownIdInLayout" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/headset_setting_sc_low_latency"
                    style="@style/RadioCheckSwitchStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    app:layout_constraintBottom_toBottomOf="@id/headset_setting_low_latency_label"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/headset_setting_low_latency_label" />

                <View
                    android:id="@+id/sync_data_layer"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:alpha="0.5"
                    android:background="@color/white"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@+id/headset_setting_low_latency_label"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/layout_sport_type" />

                <View
                    android:id="@+id/divider5"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_low_latency_label" />

                <TextView
                    android:id="@+id/headset_setting_function_introduction_label"
                    style="@style/Body.Larger.Bold"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:focusable="true"
                    android:padding="@dimen/size_spacing_medium"
                    android:text="@string/function_introduction_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider5" />

                <View
                    android:id="@+id/divider6"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_function_introduction_label" />

                <TextView
                    android:id="@+id/headset_setting_user_guide_label"
                    style="@style/Body.Larger.Bold"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:focusable="true"
                    android:padding="@dimen/size_spacing_medium"
                    android:text="@string/user_guide_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider6" />

                <View
                    android:id="@+id/divider7"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_user_guide_label" />

                <TextView
                    android:id="@+id/headset_setting_pair_another_device_label"
                    style="@style/Body.Larger.Bold"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:focusable="true"
                    android:padding="@dimen/size_spacing_medium"
                    android:text="@string/pair_another_device_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider7" />


                <ImageView
                    android:id="@+id/iv_connect_another_device"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_margin="@dimen/padding"
                    android:padding="@dimen/size_spacing_xsmall"
                    android:src="@drawable/ic_plus_toolbar"
                    app:layout_constraintBottom_toTopOf="@id/divider8"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider7" />

                <View
                    android:id="@+id/divider8"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/size_divider"
                    android:background="?suuntoDividerColor"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/headset_setting_pair_another_device_label" />

                <Space
                    android:layout_width="0dp"
                    android:layout_height="@dimen/my_headset_height_half"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/divider8" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </FrameLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
