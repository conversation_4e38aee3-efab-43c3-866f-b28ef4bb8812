<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="headphone_activity_anim_progress" translatable="false">watch_activity_progress.json</string>
    <string name="headphone_activity_anim_error" translatable="false">watch_activity_error.json</string>
    <string name="headphone_activity_anim_pairing" translatable="false">watch_activity_pairing.json</string>
    <string name="all_anim_done_green_tick" translatable="false">all_done_green_tick.json</string>
    <string name="headphone_SU03_light_flashing" translatable="false">headset_SU03_light_blinking.json</string>
    <string name="headphone_SU03_light_constant" translatable="false">headset_SU03_light_constant.json</string>
    <string name="headphone_SU03_light_sos" translatable="false">headset_SU03_light_sos.json</string>
    <string name="headphone_SU03_light_close" translatable="false">headset_SU03_light_close.json</string>
    <string name="headphone_SU07_syncing" translatable="false">file_syncing_icon.json</string>
    <string name="headphone_play_sound" translatable="false">headphone_play_sound.json</string>
    <string name="onboarding_page_photo_dimen_ratio" translatable="false">H, 9:5</string>
    <string name="user_guide_url_su03" translatable="false">https://www.suunto.com/suuntowing/userguide</string>
    <string name="user_guide_url_su05" translatable="false">https://www.suunto.com/suuntosonic/userguide</string>
    <string name="user_guide_url_su07" translatable="false">https://www.suunto.com/suuntoaqua/userguide</string>
    <string name="user_guide_url_su08" translatable="false">http://www.suunto.com/suuntoaqualight/userguide</string>
    <string name="more_information_url_su03" translatable="false">https://www.suunto.com/suuntowing/userguide/softwareupdates</string>
    <string name="more_information_url_su05" translatable="false">https://www.suunto.com/suuntosonic/userguide/softwareupdates</string>
    <string name="more_information_url_su07" translatable="false">https://www.suunto.com/suuntoaqua/userguide/softwareupdates</string>
    <string name="more_information_url_su08" translatable="false">http://www.suunto.com/suuntoaqualight/userguide/softwareupdates</string>
    <string name="head_movement_control">Head movement control</string>
    <string name="hello">Hello!</string>
    <string name="headphone_guide_summary">Suunto app supports bone conduction headphones. Personalize your experience by connecting your headphones. Let\'s get started!</string>
    <string name="body_sensing_summary">When on, you can control incoming calls and music switching through head movements.</string>
    <string name="sound_mode_title">Sound mode</string>
    <string name="sound_mode_summary">Choose the sound mode that is suitable for the headphones usage scenario.</string>
    <string name="indicator_title">LED lights</string>
    <string name="indicator_summary">Switching LED lights</string>
    <string name="dual_device_connection_title">Dual device connection</string>
    <string name="dual_device_connection_summary">When on, the headphones can connect with two Bluetooth playback devices simultaneously.</string>
    <string name="function_introduction_title">Function introduction</string>
    <string name="user_guide_title">User guide</string>
    <string name="pair_another_device_title">Pair another device</string>
    <string name="power_summary">POWER: %s</string>
    <string name="headphone_ui_no_signal_guidance">Ensure your headphones are close by and awake by pressing the middle button.</string>
    <string name="headphone_ui_scan_footer">Don\'t see your device?</string>
    <string name="headphone_ui_status_scan_info">1. Long press the \"+\" button on the headphones until the lights start flashing to enter pairing mode.\n2. Open \"Phone - Settings - Bluetooth\" and select \"Suunto xx\". The devices are successfully connected when the headphones play a prompt tone.</string>
    <string name="headphone_ui_status_scan_info_bold_1">\"Phone - Settings - Bluetooth\"</string>
    <string name="headphone_ui_status_scan_info_bold_2">\"Suunto xx\"</string>
    <string name="paired_device">paired device</string>
    <string name="ble_need_location_service">Scanning for Bluetooth devices requires that your phone\'s location services are on.</string>
    <string name="bluetooth_is_not_supported">Bluetooth is not supported.</string>
    <string name="ble_location_permission_denied">Bluetooth scanning not allowed. Please check your permissions settings.</string>
    <string name="headphone_ui_scan_footer_summary">Connect Device From Phone - Settings - Bluetooth</string>
    <string name="ble_need_location_permission">Scanning for Bluetooth devices requires access to location services. Please grant permission next.</string>
    <string name="bluetooth_not_enabled">Bluetooth not enabled. Please turn it on and try to connect again.</string>
    <string name="headphone_ui_status_failed_to_pair_message">Something went wrong with the pairing process, please try again. </string>
    <string name="need_help">Need help?</string>
    <string name="headphone_ui_status_pairing_message">Please wait.\nThis may take a while.</string>
    <string name="headphone_ui_status_pairing">Pairing</string>
    <string name="headphone_ui_pair">Pair</string>
    <string name="headphone_ui_connected">Connected</string>
    <string name="headphone_ui_status_found_device">Found a device</string>
    <string name="headphone_ui_status_looking_for_devices">Looking for devices</string>
    <string name="headphone_ui_status_failed_to_pair">Pairing failed for some reason</string>
    <!-- Indicator UI -->
    <string name="option_indicator_title">LED lights</string>
    <string name="option_indicator_description">When enabled, the headphones will display the selected LED lights. This function accelerates battery consumption.</string>
    <string name="option_sport_mode">Sport mode</string>
    <string name="option_custom_mode">Custom mode</string>
    <string name="option_custom_mode_description">Customize personalized lighting effects</string>
    <string name="custom_mode_constant_light">Constant light</string>
    <string name="custom_mode_flashing">Flashing</string>
    <string name="custom_mode_sos">SOS</string>
    <string name="custom_mode_running" translatable="false">Running</string>
    <string name="custom_mode_running_description" translatable="false">The light follows your step frequency.</string>
    <string name="custom_mode_cycling" translatable="false">Cycling</string>
    <string name="custom_mode_cycling_description" translatable="false">The light illuminates unilaterally in the direction of head rotation and simultaneously on both sides when braking.</string>

    <string name="close">Close</string>

    <string name="onboarding_sound_mode_title">Sound mode</string>
    <string name="onboarding_sound_mode_detail">Enhance your experience in various environments by switching between normal and outdoor mode.</string>
    <string name="onboarding_colleagues_title">Connect with two devices</string>
    <string name="onboarding_colleagues_detail">Your headphones can connect with two Bluetooth devices at the same time.</string>
    <string name="onboarding_headphone_welcome_title_su05">Welcome to SUUNTO Sonic</string>
    <string name="onboarding_headphone_welcome_title">Welcome to Suunto Wing</string>
    <string name="onboarding_headphone_welcome_detail">If you are a first time user, we will guide you on how to use the headphones functions.</string>
    <string name="onboarding_headphone_somatosensory_title">Head movement control</string>
    <string name="onboarding_headphone_somatosensory_detail">Answer calls or switch through songs by shaking and nodding your head.</string>
    <string name="onboarding_indicator_setting_title">LED lights</string>
    <string name="onboarding_indicator_setting_detail">You can switch between a variety of lights according to your needs.</string>
    <string name="turn_on_bluetooth_title">permissions needed to connect suunto device</string>
    <string name="turn_on_bluetooth_summary">To search for new Bluetooth devices, location access must be enabled.</string>
    <string name="headphone_update">Headphones update</string>
    <string name="serial_number">Serial number</string>
    <string name="version_information">Version</string>
    <string name="software_up_to_date">Your software is up to date.</string>
    <string name="software_version_check">Checking for updates…</string>
    <string name="update_available_label">Update available</string>
    <string name="ota_summary">1. Please keep the headphones connected with the phone during the update process. You can use your phone during the update, but keep Suunto app running in the background.\n\n2. After the upgrade is complete, the headphone will automatically restart.</string>
    <string name="body_sensing">Head Movement Control</string>
    <string name="connected_device_label">Connected devices</string>
    <string name="connect_two_device_label">Connect up to two devices</string>
    <string name="mode_summary_normal">Balanced frequency band, suitable for various daily scenarios.</string>
    <string name="mode_summary_outdoor">Higher volume, suitable for outdoor and noisy environments\nNote: Noise may occur at higher volume levels.</string>

    <string name="mode_normal">Normal</string>
    <string name="mode_outdoor">Outdoor</string>
    <string name="about_device">About device</string>
    <string name="dual_device_connection_learn_how_text">Learn how to dual device connection</string>
    <string name="outdoor">Outdoor</string>
    <string name="download_progress_text">Downloading %s</string>
    <string name="transfer_firmware_preparing">Verifying</string>
    <string name="transfer_firmware_progress_text">Updating %s</string>
    <string name="update_now">Update now</string>
    <string name="headphone_not_connected">The headphones are not connected</string>
    <string name="update_succeeded">Update succeeded</string>
    <string name="ota_transferring_failed">OTA transferring failed</string>
    <string name="ota_transferring_battery_failed">The headphones cannot be updated when the battery level is lower than 20%.</string>
    <string name="ota_file_check_failed">OTA file check failed</string>
    <string name="ota_file_download_failed">OTA file download failed</string>
    <string name="ota_phone_calling">The headphones cannot be updated when answering a call</string>
    <string name="ota_offline_music_playing">The headphones cannot be updated when playing music</string>
    <string name="update_unavailable_battery_low">Headphones power is too low, please charge before update</string>
    <string name="ota_update_fail">Update failed</string>
    <string name="head_movement_control_summary">When on, you can control incoming calls and music switching through head movements.</string>
    <string name="head_movement_control_title">Head movement control</string>
    <string name="head_movement_control_music_play_label">Switching songs:</string>
    <string name="head_movement_control_music_play_summary">Shake your head twice to switch to the next song.</string>
    <string name="try_again">Try again</string>
    <string name="restart_pairing">Restart pairing</string>
    <string name="not_allowed_exit">Not allowed to exit</string>
    <string name="low_latency_title">Low latency</string>
    <string name="update_headphone">Updating headphone</string>
    <string name="not_connected">not connected</string>
    <string name="reconnect">Reconnect</string>
    <string name="cancel">Cancel</string>
    <string name="dialog_disconnected_title">The headphones are disconnected, please restart pairing</string>
    <string name="dialog_ota_upgrade_title">A new software version is available for %s</string>
    <string name="headphone_setting_ui_enabled">Enabled</string>
    <string name="closed">Closed</string>
    <string name="normal">Normal</string>
    <string name="reboot_wait">The file transfer is successful. After the \n headphones are verified and restarted, they \n will reconnect with the app. \n Please wait patiently.</string>
    <string name="device_information">Device information</string>
    <string name="dual_device_text_top">How do I connect the second device?</string>
    <string name="tv_text_top">1. Enable dual device connection\nEnable dual device connection in Suunto app or by keeping the multifunction button and the \"+\" button pressed simultaneously for 3 seconds. If you enable the function with the device buttons, enter pairing mode first.</string>
    <string name="dual_device_center">2. Enter pairing mode\nKeep the \"+\" and \"-\" buttons pressed simultaneously for 3 seconds to enter pairing mode.\n\n3. Connect the second device\nConnect the headphones with the second device under the Bluetooth settings of the device.\n\n4. Reconnect the first device\nWhen you connect the second device, the first device temporarily disconnects from the headphones. Open the Bluetooth settings of the first device and reconnect the device. The headphones are now successfully connected with the two devices.\n\n5. Check the connected devices\nIf one of the two devices is connected with Suunto app on your phone, you can see the two connected devices listed on the headphones\' page, in the dual device connection details.</string>
    <string name="dual_device_bottom">Note: When you use the headphones with two paired devices at the same time, the headphones determine their own priority based on the order of the played content and the tasks. Normally, the first played audio takes precedence over the second, and phone calls take precedence over audio playback. However, the actual operation of different brands and models of the compatible devices may vary.</string>

    <string name="dual_device_guide_1">When the headphones are turned off:\n1. Keep the [+] button pressed for 5 seconds to switch the headphones on and enter pairing mode. </string>
    <string name="dual_device_guide_2">2. While the light are blinking in white and red, keep the [MFB] button and the [+] button pressed simultaneously for 3 seconds to enable dual device connection. The headphones will play a sound when the function is enabled.</string>
    <string name="dual_device_guide_3">3. Under the Bluetooth settings of the first compatible device, open the list of nearby devices.</string>
    <string name="dual_device_guide_4">4. Find Suunto Aqua in the list and pair the headphones with the first device.</string>
    <string name="dual_device_guide_5">5.Turn off the headphones.</string>
    <string name="dual_device_guide_6">6.Keep the [+] button pressed for 5 seconds to switch the headphones on and enter pairing mode.</string>
    <string name="dual_device_guide_7">7. Under the Bluetooth settings of the second compatible device, open the list of nearby devices.</string>
    <string name="dual_device_guide_8">8. Find Suunto Aqua on the list and pair the headphones with the second device.</string>
    <string name="dual_device_guide_9">9. When you connect the second device, the first device temporarily disconnects from the headphones. To reconnect the first device, go to the Bluetooth settings of the device or simply restart the headphones.</string>
    <string name="dual_device_guide_note">Note: When you use the headphones with two paired devices at the same time, the headphones determine their own priority based on the order of the played content and the tasks. Normally, the first played audio takes precedence over the second, and phone calls take precedence over audio playback. However, the actual operation of different brands and models of the compatible devices may vary.</string>
    <string name="new_version">new version</string>
    <string name="no_network_connection">No network connection</string>
    <string name="connecting">connecting</string>
    <string name="more_information">More Information</string>

    <string name="headset_setting_on">ON</string>
    <string name="headset_setting_off">OFF</string>

    <string name="dual_device_connect_title">Second device connection</string>
    <string name="dual_device_connect_guide">How do I connect the second device?</string>
    <string name="dual_device_connect_count">%1d/%2d</string>

    <string name="setting_button_customization_title">Button customization</string>
    <string name="setting_button_customization_summary">Customize buttons</string>

    <string name="sound_mode_normal_summary">Balanced frequency band, suitable for various daily scenarios.</string>
    <string name="sound_mode_outdoor_summary">Higher volume, suitable for outdoor and noisy environments. Note: Noise may occur at higher volume levels.</string>
    <string name="sound_mode_underwater">Underwater</string>
    <string name="sound_mode_underwater_summary">Designed for water sports like swimming, not suitable for Bluetooth music listening.</string>

    <string name="button_type_any_circumstances">ANY CIRCUMSTANCES</string>
    <string name="button_type_while_playing_music">WHILE PLAYING MUSIC</string>
    <string name="button_type_non_customizable">NON-CUSTOMIZABLE BUTTONS</string>

    <string name="button_function_power_on_off">Power on/off</string>
    <string name="button_function_volume_adjustment">Volume adjustment</string>
    <string name="button_function_dual_device_connect">Dual device connection</string>
    <string name="button_function_voice_assistant">Activate voice assistant</string>
    <string name="button_function_head_movement_control">Head movement control</string>
    <string name="button_function_exercise_trace">Enable swimming tracking</string>
    <string name="button_function_jump_rope_trace">Enable jump rope tracking</string>
    <string name="button_function_neck_fatigue_alert">Neck fatigue alert on/off</string>
    <string name="button_function_music_play_pause">Music play/pause</string>
    <string name="button_function_switch_playback_order">Switch playback order</string>
    <string name="button_function_next_song">Next song</string>
    <string name="button_function_last_song">Last song</string>
    <string name="button_function_switch_sound_mode">Switch sound mode</string>
    <string name="button_function_change_playlist">Change internal playlist</string>
    <string name="button_function_change_music_mode">Music mode switch</string>
    <string name="button_function_phone_call_control">Answer/Hang up phone calls</string>
    <string name="button_function_reject_call">Reject call</string>
    <string name="button_function_reset_setting">Reset to default setting</string>
    <string name="button_function_start_bluetooth_pairing">Start Bluetooth pairing</string>
    <string name="button_function_start_end_workout">Start/end workout</string>
    <string name="button_function_led_switch" translatable="false">LED lights</string>

    <string name="button_shortcut_key_0">Double press “MFB”</string>
    <string name="button_shortcut_key_1">Triple press “MFB”</string>
    <string name="button_shortcut_key_2">Hold down “MFB” for 3 seconds</string>
    <string name="button_shortcut_key_3">Hold down “-” for 3 seconds</string>
    <string name="button_shortcut_key_4">Hold down “+” and “-” for 3 seconds</string>
    <string name="button_shortcut_key_5">Hold down “MFB” and “+” for 3 seconds</string>
    <string name="button_shortcut_key_6">Hold down “MFB” and “-” for 3 seconds</string>
    <string name="button_shortcut_key_7">Hold down “+” for 3 seconds</string>
    <string name="button_shortcut_key_8">Press “+”/ “-”</string>
    <string name="button_shortcut_key_9">Press “MFB” once</string>
    <string name="button_shortcut_key_10">Press and hold “MFB”</string>
    <string name="button_shortcut_key_11">Hold down “MFB” for 5 seconds</string>
    <string name="button_null">None</string>

    <string name="button_setting_dialog_title">Confirm your selection</string>
    <string name="button_setting_dialog_content">Assigning this action to \'%1s\' will automatically reset the \'%2s\' action to \'None\'.</string>
    <string name="headset_battery_low">Headphones power is too low, please charge before update</string>

    <string name="neck_movement_monitoring">Neck fatigue alert</string>
    <string name="neck_movement_monitoring_introduce">Get reminders to move your neck if it\'s still too long to prevent stress and injury.</string>
    <string name="neck_movement_monitoring_function">Get reminders to move your neck if it\'s still too long to prevent stress and injury. It is recommended for cycling.</string>
    <string name="interval_reminder_time">Interval timer</string>
    <string name="interval_reminder_time_value">%d min</string>
    <string name="neck_mobility_assessment">Neck mobility assessment</string>
    <string name="neck_mobility_assessment_introduce">Assessment of your cervical spine flexibility.</string>
    <string name="other_age_setting_title">Assessor\'s information required</string>
    <string name="self_age_setting_title">Personal information required</string>
    <string name="age_setting_purpose">Your information is required for accurate assessment.</string>
    <string name="assessor_name">Assessor\'s name</string>
    <string name="assessor_name_hint">Name</string>
    <string name="age_setting_tips">This information will only be used for this assessment and will not be saved, nor will the result be saved.</string>
    <string name="self_age_setting_tips">You can change the information in the settings later.</string>
    <string name="save_and_start">Save &amp; start </string>
    <string name="start_assessment">Start Assessment</string>
    <string name="assessment_usage_title">How to do this assessment?</string>
    <string name="neck_assessment_usage_content">Get to know it in a few steps.</string>
    <string name="assessment_guide_step_title">Step %d</string>
    <string name="neck_assessment_guide_step_1">Wear your headphones and keep your upper body straight.</string>
    <string name="neck_assessment_guide_step_2">Look straight ahead and follow the audio cues to complete the visual calibration.</string>
    <string name="neck_assessment_guide_step_3">Rotate your head in each direction indicated by the audio hint.</string>
    <string name="neck_assessment_guide_step_4">When you reach the limit of your movement, hold the position until you hear a beep, then return your head to the neutral position.</string>
    <string name="neck_assessment_guide_step_5">After completing the assessment in all six directions, you will receive a cervical mobility report.</string>
    <string name="calibration">Calibration</string>
    <string name="calibration_guide">Look straight ahead then hold still</string>
    <string name="calibration_hold_still">Please follow the voice prompts to complete the visual calibration.</string>
    <string name="calibrating">Calibrating...</string>
    <string name="calibration_complete">Calibration completed</string>
    <string name="calibration_complete_content">The cervical spine assessment will start automatically, if the page does not jump automatically, please tap the button to start the assessment.</string>
    <string name="calibration_failed">Calibration failed</string>
    <string name="calibration_failed_content">Something went wrong, please tap the button to restart calibration.</string>
    <string name="recalibrate">Recalibrate</string>
    <string name="neck_assessment_guide_instruction">Use the cervical mobility assessment to quickly check your neck health by wearing the headset and following the prompts.</string>
    <string name="assessment_note_title">Please note</string>
    <string name="assessment_recommend_ages_note_title">Recommended for ages 8+</string>
    <string name="assessment_note_content">If you have a medical condition that requires you to limit the movement of your cervical spine as recommended by your doctor, do not perform the assessment; if you do not have such a condition and experience dizziness or your vision turns black while completing the assessment, stop the assessment immediately and seek medical attention if necessary.</string>
    <string name="calibration_quit_title">Are you sure you want to quit?</string>
    <string name="calibration_quit_content">If you exit now, you need to restart the visual calibration. Are you sure you want to continue?</string>
    <string name="left_rotation_title">Turn your head to the left</string>
    <string name="right_rotation_title">Turn your head to the right</string>
    <string name="flexion_title">Bend your head forward</string>
    <string name="extension_title">Tilt your head back</string>
    <string name="left_lateral_flexion_title">Tilt your head to the left</string>
    <string name="right_lateral_flexion_title">Tilt your head to the right</string>
    <string name="assessment_step_instruction">Move till the point you think you cannot move any further, and hold for a few seconds.</string>
    <string name="reassess">Reassess</string>
    <string name="generate_report">Generate report</string>
    <string name="generating_report">report generating...</string>
    <string name="assessing">Assessing...</string>
    <string name="ready_for_next">Get ready for the next step...</string>
    <string name="left_rotation">Left rotation</string>
    <string name="right_rotation">Right rotation</string>
    <string name="flexion">Flexion</string>
    <string name="extension">Extension</string>
    <string name="left_lateral_flexion">Left lateral flexion</string>
    <string name="right_lateral_flexion">Right lateral flexion</string>
    <string name="left_rotation_complete">Left rotation completed</string>
    <string name="left_rotation_not_complete">Left rotation failed</string>
    <string name="right_rotation_complete">Right rotation completed</string>
    <string name="right_rotation_not_complete">Right rotation failed</string>
    <string name="flexion_complete">Flexion completed</string>
    <string name="flexion__not_complete">Flexion failed</string>
    <string name="extension_complete">Extension completed</string>
    <string name="extension__not_complete">Extension failed</string>
    <string name="left_lateral_flexion_complete">Left lateral flexion completed</string>
    <string name="left_lateral_flexion_not_complete">Left lateral flexion failed</string>
    <string name="right_lateral_flexion_complete">Right lateral flexion completed</string>
    <string name="right_lateral_flexion_not_complete">Right lateral flexion failed</string>
    <string name="complete_hint">Next step will start automatically in 1 second.</string>
    <string name="reassess_hint">You have completed this step. Tap the button to reassess if needed.</string>
    <string name="assessment_failed_hint">Please tap the button to reassess.</string>
    <string name="skip">Skip</string>
    <string name="assessment_exit_hint">If you exit the assessment, your data will not be saved. Are you sure you want to continue?</string>
    <string name="assessment_completed">Assessment completed</string>
    <string name="assessment_completed_hint">You can now generate assessment report.</string>
    <string name="assessment_not_completed">Assessment failed</string>
    <string name="assessment_not_completed_hint">A comprehensive assessment requires all movements in six directions. Tap reassess to start over.</string>
    <string name="assessment_all_failed_hint">No data recorded. Please complete at least one assessment.</string>
    <string name="steps_skipped_title">Some steps were skipped</string>
    <string name="steps_skipped_hint">This may result in an incomplete report.</string>
    <string name="return_to_assessment">Return to assessment</string>
    <string name="save_data_title">Save data?</string>
    <string name="save_data_hint">Do you want to save the data before exiting?</string>
    <string name="do_not_save">Don\'t save</string>
    <string name="confirm">Confirm</string>
    <string name="generate_report_hint">Generating your report. The page will automatically proceed when ready. If not, please tap the button.</string>
    <string name="view_report">View report</string>
    <string name="history_report">Assessment logs</string>
    <string name="assessment_other">Guest assessment</string>
    <string name="introduction">Instructions</string>
    <string name="history_report_hint">Here you can find the logged assessments from the last 3 months.</string>
    <string name="delete_assessment_report_title">Delete this record?</string>
    <string name="delete_assessment_report_content">This record will no longer be displayed after deletion</string>
    <string name="assessment_result">Assessment Result</string>
    <string name="hi">Hi,</string>
    <string name="assessment_report_detail_tip">Assessment results are for reference only. If you feel unwell, please consult a medical professional.</string>
    <string name="neck_health_attention_title">Attention required</string>
    <string name="insufficient_cervical_mobility">Insufficient cervical mobility.</string>
    <string name="neck_health_fair_title">Fair</string>
    <string name="neck_health_below_standard">Neck mobility in multiple directions is below standard</string>
    <string name="neck_health_not_bad_title">Not bad</string>
    <string name="neck_health_not_bad_content">Neck mobility in most directions is above standard.</string>
    <string name="neck_health_all_good_title">All good</string>
    <string name="neck_health_all_good_content">You have good neck mobility!</string>
    <string name="rotation">Rotation</string>
    <string name="lateral_flexion">Lateral flexion</string>
    <string name="head_tilts">Head tilts</string>
    <string name="slight_rotation_asymmetry">slight rotation asymmetry.</string>
    <string name="slight_lateral_flexion_asymmetry">slight lateral flexion asymmetry.</string>
    <string name="slight_insufficient_mobility">Slight insufficient mobility</string>
    <string name="rotation_symmetry">Rotation symmetry</string>
    <string name="lateral_flexion_symmetry">Lateral flexion symmetry</string>
    <string name="asymmetry">Asymmetry</string>
    <string name="good_condition">Good condition</string>
    <string name="pay_attention">Pay attention</string>
    <string name="birthday_tips">You will enter your birthday before the first assessment. Your information is required for accurate assessment. </string>
    <string name="age_not_suited_info">This test is best suited for individuals aged 8 and above. We recommend waiting until your child reaches the appropriate age to ensure accurate results.\nYou can change the age in the settings.</string>
    <string name="trend">Trend</string>
    <string name="trend_period">over the last 3 months</string>
    <string name="history_title">%d/%d assessments met the standard </string>
    <string name="skip_instructions">Skip instructions</string>

    <!--    jump assessment-->
    <string name="jump_assessment">Jump assessment</string>
    <string name="jump_assessment_introduction">Assess your neuromuscular fatigue.</string>
    <string name="jump_assessment_title">Neuromuscular fatigue assessment</string>
    <string name="jump_assessment_baseline_instruction">Jump assessment, also known as Counter Movement Jump (CMJ) testing, is an effective way to measure your neuromuscular fatigue in both exercise or daily activities.\n\nWe need to build your jump baseline first to get the initial data.</string>
    <string name="jump_assessment_guide_usage_content">Get to know it in a few steps.\n\nIt is recommended that you build your jump baseline when you are in good physical condition.</string>
    <string name="jump_assessment_guide_step_1">Wear your headphones and find a comfortable stance width before jumping.</string>
    <string name="jump_assessment_guide_step_2">Keep your hands on your hips during the whole test.</string>
    <string name="jump_assessment_guide_step_3">Rapidly squat and jump to your maximum height with both legs extended in the air. Land in the same spot where you took off.</string>
    <string name="jump_assessment_guide_step_4">Repeat STEP 3 three consecutive times. Then you will receive your neuromuscular fatigue report.</string>
    <string name="build_baseline">Build baseline</string>
    <string name="fatigued_reminder_title">Feeling Fatigued?</string>
    <string name="fatigued_reminder_content">Your current condition might not be ideal for building jump baseline values. We recommend proceeding when you feel well-rested. Would you like to continue?</string>
    <string name="first_jump">First jump</string>
    <string name="second_jump">Second jump</string>
    <string name="last_jump">Last jump</string>
    <string name="jump_reminder">When you hear a beep, follow the video instruction to jump once.</string>
    <string name="jump_height_value">Jump height %d</string>
    <string name="jump_height">Jump height</string>
    <string name="re_record">Re-record</string>
    <string name="next">Next</string>
    <string name="complete">Complete</string>
    <string name="first_jump_assessment_failed">Failed to save first jump</string>
    <string name="second_jump_assessment_failed">Failed to save second jump</string>
    <string name="last_jump_assessment_failed">Failed to save last jump</string>
    <string name="jump_assessment_failed_hint">Something went wrong. Please try again.</string>
    <string name="jump_assessment_exit_confirmation_title">Are you sure you want to quit?</string>
    <string name="jump_assessment_exit_confirmation_content">Data will not be saved if you exit. Are you sure you want to continue?</string>
    <string name="measurements">Measurements</string>
    <string name="tap_re_record">Tap to re-record any jump if needed.</string>
    <string name="your_baseline">Your baseline</string>
    <string name="your_jump_data">Your jump data</string>
    <string name="result_average">The result is the average of three jumps.</string>
    <string name="average_jump_height">Average jump height</string>
    <string name="flight_time">Flight time</string>
    <string name="takeoff_v">Takeoff velocity</string>
    <string name="save_baseline">Save baseline</string>
    <string name="recommendation_assessment">It is recommended that you do this assessment both before and after training.</string>
    <string name="add_measurement">Add measurement</string>
    <string name="re_record_baseline">Re-record baseline</string>
    <string name="neuromuscular_fatigue">Neuromuscular fatigue</string>
    <string name="fatigue_index">Fatigue index</string>
    <string name="jump_assessment_result">Jump Assessment result</string>
    <string name="jump_assessment_result_hint">Here is your result</string>
    <string name="no_neuromuscular_fatigue">︎Slight</string>
    <string name="some_neuromuscular_fatigue">︎Mild</string>
    <string name="moderate_neuromuscular_fatigue">︎Moderate</string>
    <string name="higher_neuromuscular_fatigue">︎Severe</string>
    <string name="extreme_neuromuscular_fatigue">︎Extreme</string>
    <string name="suunto_coach">Suunto Coach</string>
    <string name="least_neuromuscular_fatigue_advice">To optimize your fitness for competition, consider increasing your total training volume by 5–10%% if you\'re still in the training cycle. This can help further improve your performance.</string>
    <string name="less_neuromuscular_fatigue_advice">Feel confident about your training or upcoming race! If a race is approaching, it\'s best to maintain your current training volume. If you\'re still in the training cycle, consider increasing your total training volume by 5–10%% to continue improving your fitness.</string>
    <string name="moderate_neuromuscular_fatigue_advice">It is recommended that 10–15 minutes of slow walking to relax before the end of each exercise session will help reduce neuromuscular fatigue.</string>
    <string name="higher_neuromuscular_fatigue_advice">Your body is still recovering. It is recommended that you assess your recovery weekly to monitor progress. A 10–15 minute slow walk at the end of each workout can help reduce neuromuscular fatigue and aid relaxation.</string>
    <string name="extreme_neuromuscular_fatigue_advice">It is advised that you rest for the next 1–2 days and engage in light aerobic activity for 20–30 minutes at heart rate zones 1–2 to actively recover from neuromuscular fatigue. Additionally, consider taking 20-minute ice baths for your lower extremities to promote circulation and your metabolism.</string>
    <string name="measured_now">Last jump</string>
    <string name="comparison_data">Comparison with average data</string>
    <string name="fatigue_high">High</string>
    <string name="fatigue_free">Low</string>
    <string name="generate_jump_profile">Generate report</string>

    <string name="function_conflict_title">Function Conflict</string>
    <string name="function_conflict_content">The assessment cannot be started while the sports mode is active. Would you like to turn off the sports mode and start the assessment?</string>
    <string name="function_conflict_confirm">Turn off and start</string>
    <string name="reassess_confirm_dialog_title">Are you sure you want to reassess?</string>
    <string name="reassess_confirm_dialog_content">You can choose whether to save your current data before reassessing.</string>
    <string name="save_reassess">Save and reassess</string>
    <string name="not_save_reassess">Reassess without saving</string>
    <string name="assessment_normal">Normal</string>
    <string name="assessment_underperforming">Underperforming</string>
    <string name="standard_line_name">Health reference line</string>
    <string name="warn_line_name">Attention line</string>
    <string name="health_guide_flexion_title">Sternocleidomastoid muscle stretch</string>
    <string name="health_guide_flexion_summary">To improve extension mobility and asymmetrical rotation.</string>
    <string name="health_guide_flexion_advice_1">Bend your head to one side at a 45-degree angle.</string>
    <string name="health_guide_flexion_advice_2">Turn your head and look up toward the ceiling on the other side.</string>
    <string name="health_guide_flexion_advice_3">Apply gentle pressure behind your head with your hands to feel the stretch in the muscles on the front side of your neck.</string>
    <string name="health_guide_flexion_advice_4">Hold for 20 seconds, rest briefly, and repeat.</string>
    <string name="health_guide_rotation_title">Rotational stretch</string>
    <string name="health_guide_rotation_summary">To improve cervical spine rotation mobility.</string>
    <string name="health_guide_rotation_advice_1">Place one hand on the back of your head and the other hand on your cheek.</string>
    <string name="health_guide_rotation_advice_2">Rotate your head to one side until you feel a stretch in the muscles on the side of your neck.</string>
    <string name="health_guide_rotation_advice_3">Hold for 20 seconds after feeling the stretch, then rest briefly and repeat.</string>
    <string name="health_guide_advice_last">Do this 3 times on each side, for a total of 6 times.</string>
    <string name="health_guide_lateral_flexion_title">Upper Trapizius muscle stretch</string>
    <string name="health_guide_lateral_flexion_summary">To improve cervical spine lateral flexion mobility and asymmetrical lateral flexion.</string>
    <string name="health_guide_lateral_flexion_advice_1">Stretch one hand under your leg and place the other hand on the side of your head, above the ear. </string>
    <string name="health_guide_lateral_flexion_advice_2">Relax your neck as you gently pull your head towards the shoulder on the opposite side, feeling the stretch in the side of your neck. </string>
    <string name="health_guide_lateral_flexion_advice_3">Hold for 20 seconds after feeling the stretch, then rest briefly and repeat.</string>

    <string name="sports_switch_tittle">Sports mode</string>
    <string name="sports_switch_instruction">Change the sport mode for your headphones</string>
    <string name="sports_switch_operation">Hold down the ◁ and ○▷ buttons for 3 seconds to start sport mode. Select your desired sport from the list.</string>
    <string name="su07_onboarding_welcome_title">Hello, %s! Welcome to your Suunto Aqua</string>
    <string name="su07_onboarding_welcome_content">Get to know your headphones in just a few steps.</string>
    <string name="su07_onboarding_swimming_title">Record your swim</string>
    <string name="su07_onboarding_swimming_content">Press and hold both the \"+\" and \"-\" buttons for 3 seconds to activate sports mode. Review your stats in the app after your workout.</string>
    <string name="su07_onboarding_sound_mode_content">Enhance your listening experience by switching between normal, outdoor, and underwater modes based on your surroundings.</string>
    <string name="su07_onboarding_offline_music_content">Enjoy built-in music without needing your phone.</string>
    <string name="su07_onboarding_neck_fatigue_content">Receive reminders to adjust your neck position after being inactive for too long, preventing discomfort and injury—especially during cycling.</string>
    <string name="su07_onboarding_neck_assessment_content">An initial check of your cervical spine health</string>
    <string name="su07_onboarding_jump_assessment_content">Measure neuromuscular fatigue effectively during exercise or daily activities.</string>
    <string name="su07_onboarding_head_movement_control_content">Answer calls or switch soundtracks hands-free by simply shaking or nodding your head.</string>
    <string name="su07_onboarding_last_title">You are good to go</string>
    <string name="su07_onboarding_last_content">If you have any questions, check the help and feedback section in the app for answers.</string>
    <string name="assessment_miss_data">No data</string>
    <string name="tracking_workout">Tracking workout</string>
    <string name="tracking_workout_tips">Some features are unavailable during workout tracking</string>
    <string name="left">Left</string>
    <string name="right">Right</string>
    <string name="empty_records_title">No records yet</string>
    <string name="empty_records_content">You haven\'t assessed your neck mobility in the past three months. Future assessment results will be saved here.</string>
    <string name="head_control_instructions_call">Incoming calls</string>
    <string name="head_control_item_answer_call_title">Answer a call</string>
    <string name="head_control_item_answer_call_content">Nod twice</string>
    <string name="head_control_item_answer_reject_title">Reject a call</string>
    <string name="head_control_item_shake_head">Shake your head twice</string>
    <string name="head_control_instructions_media">Media control</string>
    <string name="head_control_item_skip_song_title">Skip to next song</string>
    <string name="neck_health_guide">Neck health guide</string>
    <string name="neck_health_guide_top_content">Follow the videos do the exercise. Please do not force yourself to move, and seek medical advice if you feel unwell.</string>
    <string name="headphone_disconnect">Headphones disconnected</string>
    <string name="headphone_disconnect_tips">To reconnect your headphones, quit the assessment first. Once reconnected, you can restart the assessment.</string>
    <string name="quit_assessment">Quit assessment</string>

    <string name="sport_switch_modify">Modify</string>
    <string name="su08_onboarding_welcome_title">Hello, %s! Welcome to your Suunto Aqua Light</string>
    <string name="pool_length">Pool length</string>
    <string name="data_sync">Data syncing</string>
</resources>
