package com.suunto.headset.settingv2

import androidx.annotation.DrawableRes
import com.suunto.headset.contract.HeadsetSettingContract
import com.suunto.soa.data.SportStatus
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

data class HeadphoneTopInfo(
    @DrawableRes val headIconRes: Int,
    val connectionState: HeadsetSettingContract.ConnectState = HeadsetSettingContract.ConnectState.Disconnected,
    val sportStatus: SportStatus? = null,
    val batteryPower: ImmutableList<BatteryInfo> = persistentListOf(),
    val syncData: Boolean = false,
    val otaInProgress: Boolean = false,
)

data class BatteryInfo(
    val batteryType: BatteryType = BatteryType.DEFAULT,
    val value: Int? = null,
    @DrawableRes val iconRes: Int? = null
)

enum class BatteryType {
    LEFT, CHARGING_BOX, RIGHT, DEFAULT
}
