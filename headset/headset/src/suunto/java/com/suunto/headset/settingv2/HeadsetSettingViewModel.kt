package com.suunto.headset.settingv2

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import androidx.annotation.DrawableRes
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.eventtracking.EventTracker
import com.stt.android.utils.BatteryLevelUtil
import com.suunto.headset.R
import com.suunto.headset.api.HeadsetApi
import com.suunto.headset.capability.BaseHeadsetFeature
import com.suunto.headset.capability.SupportedHeadsetDevices
import com.suunto.headset.contract.HeadsetSettingContract
import com.suunto.headset.model.ConnectionPage
import com.suunto.headset.model.DeviceBindInfoRequest
import com.suunto.headset.model.HeadsetConfig
import com.suunto.headset.model.MultipleBattery
import com.suunto.headset.model.SportSupports
import com.suunto.headset.repository.CurrentlyPairedHeadsetConfigUseCase
import com.suunto.headset.repository.SetUserHeadsetConfigDataSource
import com.suunto.headset.syncdata.HeadphoneSyncDataUseCase
import com.suunto.headset.ui.CURRENT_DEVICE
import com.suunto.headset.viewmodel.AnalyticsUtils
import com.suunto.headset.viewmodel.BaseHeadsetSettingViewModel
import com.suunto.headset.viewmodel.ConnectHelper
import com.suunto.headset.viewmodel.FetchOTAInfoUseCase
import com.suunto.headset.viewmodel.OTAUploadLogHelper
import com.suunto.soa.ble.client.ConnectStates
import com.suunto.soa.ble.client.DeviceInfoFeatures
import com.suunto.soa.ble.control.attr.EqMode
import com.suunto.soa.ble.control.attr.LedStatus
import com.suunto.soa.ble.control.attr.LowLatencyMode
import com.suunto.soa.ble.control.attr.MusicMode
import com.suunto.soa.ble.control.attr.SportsMode
import com.suunto.soa.ble.exception.DisconnectionException
import com.suunto.soa.ble.scanner.validName
import com.suunto.soa.bluetooth.ota.service.OtaState
import com.suunto.soa.data.SportStatus
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableMap
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.collections.immutable.toImmutableMap
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.withLock
import timber.log.Timber
import javax.inject.Inject
import kotlin.reflect.KClass
import com.suunto.music.R as MR

@HiltViewModel
class HeadsetSettingViewModel @Inject constructor(
    private val connectHelper: ConnectHelper,
    private val connectStates: ConnectStates,
    private val headsetConfigUseCase: CurrentlyPairedHeadsetConfigUseCase,
    private val setUserHeadsetConfigDataSource: SetUserHeadsetConfigDataSource,
    private val headsetApi: HeadsetApi,
    private val savedStateHandle: SavedStateHandle,
    private val baseHeadsetFeature: BaseHeadsetFeature,
    private val deviceInfoFeatures: DeviceInfoFeatures,
    private val otaUploadLogHelper: OTAUploadLogHelper,
    private val fetchOTAInfoUseCase: FetchOTAInfoUseCase,
    private val headphoneSyncDataUseCase: HeadphoneSyncDataUseCase,
    private val supportedHeadsetDevices: SupportedHeadsetDevices,
    eventTracker: EventTracker,
    analyticsUtils: AnalyticsUtils
) : BaseHeadsetSettingViewModel(
    setUserHeadsetConfigDataSource,
    baseHeadsetFeature,
    analyticsUtils,
    eventTracker
) {

    private var shouldSyncHeadphoneData = false
    private var syncHeadsetDataJob: Job? = null

    override val stateClass: KClass<HeadsetSettingContract.UIState>
        get() = HeadsetSettingContract.UIState::class

    private val _headphoneTopInfo = MutableStateFlow(getHeadphoneTopInfo())
    val headphoneTopInfo = _headphoneTopInfo.asStateFlow()

    private val _settingItems = MutableStateFlow(getSettingItems())
    val settingItems = _settingItems.asStateFlow()

    private var otaInProgress = false
    private var calling = false
    private var exercising = false

    init {
        viewModelScope.launch {
            otaUploadLogHelper.uploadAllOTALogs(headsetApi)
            savedStateHandle.get<BluetoothDevice>(CURRENT_DEVICE)?.let {
                updateState {
                    copy(
                        currentDeviceAndConnectIsFailed = it to false
                    )
                }
                handleGetLatestVersion()
            } ?: kotlin.run {
                handleConnectDevice(
                    setUserHeadsetConfigDataSource.getLastDeviceMac(),
                    setUserHeadsetConfigDataSource.getHeadsetConfig().productId,
                )
            }
        }
        connectHelper.currentConnectionPage = ConnectionPage.HEADPHONE_MAIN
        initSettingItemState()
        shouldSyncHeadphoneData = true
    }

    private fun getHeadphoneTopInfo(): HeadphoneTopInfo {
        return HeadphoneTopInfo(
            headIconRes = baseHeadsetFeature.getHeadsetIconRes(),
            connectionState = HeadsetSettingContract.ConnectState.Connecting,
        )
    }

    fun updateSyncingData(syncingData: Boolean) {
        _headphoneTopInfo.value = _headphoneTopInfo.value.copy(
            syncData = syncingData
        )
        disabledAllSettingItems(syncingData)
    }

    fun updateOtaInProgress(otaInProgress: Boolean) {
        _headphoneTopInfo.value = _headphoneTopInfo.value.copy(
            otaInProgress = otaInProgress
        )
    }

    fun updateHeadphoneIcon(@DrawableRes iconRes: Int) {
        _headphoneTopInfo.value = _headphoneTopInfo.value.copy(
            headIconRes = iconRes
        )
    }

    fun updateConnectionState(
        connectionState: HeadsetSettingContract.ConnectState
    ) {
        _headphoneTopInfo.value = _headphoneTopInfo.value.copy(
            connectionState = connectionState
        )
        val disabled =
            connectionState != HeadsetSettingContract.ConnectState.Connected || otaInProgress || calling
        disabledAllSettingItems(disabled)
    }

    private fun disabledAllSettingItems(isDisabled: Boolean) {
        _settingItems.update { items ->
            items.mapValues { values ->
                values.value.map { item ->
                    when (item) {
                        is SettingItem.StateItem -> item.copy(isDisabled = isDisabled)
                        is SettingItem.ActionItem -> item.copy(isDisabled = isDisabled)
                    }
                }.toImmutableList()
            }.toImmutableMap()
        }
    }

    fun updateSportState(sportStatus: SportStatus?) {
        _headphoneTopInfo.value = _headphoneTopInfo.value.copy(
            sportStatus = sportStatus
        )
        handleExercising(sportStatus?.isSporting() ?: false)
    }

    private fun updateBatteryPower(power: Int?) {
        _headphoneTopInfo.value = _headphoneTopInfo.value.copy(
            batteryPower = persistentListOf(
                BatteryInfo(
                    BatteryType.DEFAULT,
                    power,
                    power?.let { BatteryLevelUtil.getBatteryLevelIconRes(it) }
                )
            )
        )
    }

    private fun updateMultipleBatteryPower(multipleBattery: MultipleBattery?) {
        _headphoneTopInfo.value = _headphoneTopInfo.value.copy(
            batteryPower = persistentListOf(
                BatteryInfo(
                    BatteryType.LEFT,
                    multipleBattery?.firstValue,
                    multipleBattery?.firstValue?.let { BatteryLevelUtil.getBatteryLevelIconRes(it) }
                ),
                BatteryInfo(
                    BatteryType.CHARGING_BOX,
                    multipleBattery?.secondValue,
                    multipleBattery?.secondValue?.let { BatteryLevelUtil.getBatteryLevelIconRes(it) }
                ),
                BatteryInfo(
                    BatteryType.RIGHT,
                    multipleBattery?.thirdValue,
                    multipleBattery?.thirdValue?.let { BatteryLevelUtil.getBatteryLevelIconRes(it) }
                )
            )
        )
    }

    fun handleOTAState(otaInProgress: Boolean) {
        this.otaInProgress = otaInProgress
        if (otaInProgress) {
            updateConnectionState(HeadsetSettingContract.ConnectState.Connected)
        } else {
            launch {
                updateConnectionState(connectStates.getState().toConnectState())
            }
        }
    }

    fun handleCalling(calling: Boolean) {
        this.calling = calling
        listOf(
            SettingItemType.MUSIC_MODE,
            SettingItemType.JUMP_ASSESSMENT,
            SettingItemType.NECK_MOBILITY_ASSESSMENT,
            SettingItemType.SPORT_MODE,
        ).forEach {
            disabledSettingItem(it, calling)
        }
    }

    private fun handleExercising(exercising: Boolean) {
        this.exercising = exercising
        listOf(
            SettingItemType.MUSIC_MODE,
            SettingItemType.JUMP_ASSESSMENT,
            SettingItemType.NECK_MOBILITY_ASSESSMENT,
            SettingItemType.SPORT_MODE,
        ).forEach {
            disabledSettingItem(it, exercising)
        }
    }

    private fun getSettingItems(): ImmutableMap<Int, ImmutableList<SettingItem>> {
        return buildMap {
            put(1, buildList<SettingItem> {
                if (supportedHeadsetDevices.supportSportsSwitchFeature(baseHeadsetFeature)) {
                    add(
                        SettingItem.StateItem(
                            titleResId = R.string.sports_switch_tittle,
                            settingItemType = SettingItemType.SPORT_MODE,
                            descriptionResId = R.string.sports_switch_instruction,
                        )
                    )
                }
            }.toImmutableList())
            put(2, buildList {
                if (supportedHeadsetDevices.supportBodySense(baseHeadsetFeature)) {
                    add(
                        SettingItem.StateItem(
                            titleResId = R.string.head_movement_control_title,
                            settingItemType = SettingItemType.BODY_SENSE,
                            descriptionResId = R.string.body_sensing_summary
                        )
                    )
                }
                if (supportedHeadsetDevices.supportDualDeviceConnect(baseHeadsetFeature)) {
                    add(
                        SettingItem.ActionItem(
                            titleResId = R.string.dual_device_connection_title,
                            settingItemType = SettingItemType.DUAL_DEVICE_CONNECTION,
                            descriptionResId = R.string.dual_device_connection_summary
                        )
                    )
                }
                if (supportedHeadsetDevices.supportEqMode(baseHeadsetFeature)) {
                    add(
                        SettingItem.StateItem(
                            titleResId = R.string.sound_mode_title,
                            settingItemType = SettingItemType.SOUND_MODE,
                            descriptionResId = R.string.sound_mode_summary
                        )
                    )
                }
                if (supportedHeadsetDevices.supportOfflineMusic(baseHeadsetFeature)) {
                    add(
                        SettingItem.StateItem(
                            titleResId = MR.string.music_mode,
                            settingItemType = SettingItemType.MUSIC_MODE,
                            descriptionResId = MR.string.music_mode_introduction
                        )
                    )
                }
                if (supportedHeadsetDevices.supportButtonCustomization(baseHeadsetFeature)) {
                    add(
                        SettingItem.ActionItem(
                            titleResId = R.string.setting_button_customization_title,
                            settingItemType = SettingItemType.KEY_CUSTOMIZATION
                        )
                    )
                }
                if (supportedHeadsetDevices.supportLEDLightMode(baseHeadsetFeature)) {
                    add(
                        SettingItem.StateItem(
                            titleResId = R.string.indicator_title,
                            settingItemType = SettingItemType.INDICATOR,
                            descriptionResId = R.string.indicator_summary
                        )
                    )
                }

            }.toImmutableList())
            put(3, buildList {
                if (supportedHeadsetDevices.supportNeckMovementAssessment(baseHeadsetFeature)) {
                    add(
                        SettingItem.StateItem(
                            titleResId = R.string.neck_movement_monitoring,
                            settingItemType = SettingItemType.NECK_MOVE_MONITORING,
                            descriptionResId = R.string.neck_movement_monitoring_introduce
                        )
                    )
                    add(
                        SettingItem.ActionItem(
                            titleResId = R.string.neck_mobility_assessment,
                            settingItemType = SettingItemType.NECK_MOBILITY_ASSESSMENT,
                            descriptionResId = R.string.neck_mobility_assessment_introduce
                        )
                    )
                }
                if (supportedHeadsetDevices.supportJumpAssessmentFeature(baseHeadsetFeature)) {
                    add(
                        SettingItem.ActionItem(
                            titleResId = R.string.jump_assessment,
                            settingItemType = SettingItemType.JUMP_ASSESSMENT,
                            descriptionResId = R.string.jump_assessment_introduction
                        )
                    )
                }
            }.toImmutableList())
            put(4, buildList {
                add(
                    SettingItem.ActionItem(
                        titleResId = R.string.function_introduction_title,
                        settingItemType = SettingItemType.FUNCTION_INTRODUCTION,
                    )
                )
                if (supportedHeadsetDevices.getUserGuideUrl(baseHeadsetFeature) != 0) {
                    add(
                        SettingItem.ActionItem(
                            titleResId = R.string.user_guide_title,
                            settingItemType = SettingItemType.USER_GUIDE,
                        )
                    )
                }
                add(
                    SettingItem.ActionItem(
                        titleResId = R.string.pair_another_device_title,
                        settingItemType = SettingItemType.PAIR_ANOTHER_DEVICE,
                        rightIconRes = R.drawable.ic_plus_toolbar
                    )
                )
            }.toImmutableList())
        }.toImmutableMap()
    }

    fun disabledSettingItem(
        settingItemType: SettingItemType,
        isDisabled: Boolean
    ) {
        _settingItems.update {
            it.mapValues { values ->
                values.value.map { item ->
                    if (item.settingItemType == settingItemType) {
                        when (item) {
                            is SettingItem.StateItem -> item.copy(isDisabled = isDisabled)
                            is SettingItem.ActionItem -> item.copy(isDisabled = isDisabled)
                        }
                    } else {
                        item
                    }
                }.toImmutableList()
            }.toImmutableMap()
        }
    }

    fun updateSettingItemState(
        settingItemType: SettingItemType,
        stateTextResId: Int?,
    ) {
        _settingItems.update {
            it.mapValues { values ->
                values.value.map { item ->
                    if (item.settingItemType == settingItemType) {
                        when (item) {
                            is SettingItem.StateItem -> item.copy(
                                stateTextResId = stateTextResId,
                            )

                            is SettingItem.ActionItem -> item
                        }
                    } else {
                        item
                    }
                }.toImmutableList()
            }.toImmutableMap()
        }
    }

    private fun syncHeadsetData() {
        // syncHeadsetDataJob is null, need to sync data. so use !=false
        if (syncHeadsetDataJob?.isCompleted != false) {
            syncHeadsetDataJob = launchOnIO {
                val updateSyncingStateJob = launchOnIO {
                    delay(UPDATE_STATE_DELAY_TIME)
                    updateState { copy(syncingData = true) }
                }
                headphoneSyncDataUseCase()
                if (updateSyncingStateJob.isCompleted) {
                    updateState { copy(syncingData = false) }
                } else {
                    updateSyncingStateJob.cancel()
                }
            }
        }
    }

    override suspend fun handleDeviceConnectedEvent() {
        super.handleDeviceConnectedEvent()
        updateState { copy(otaState = OtaState.None) }
        if (supportedHeadsetDevices.supportMultipleBattery(baseHeadsetFeature)) {
            handleMultiBattery()
        } else {
            handleGetBattery()
        }
        handleGetDeviceInfo()
    }

    override suspend fun handleDeviceDisconnectedEvent() {
        super.handleDeviceDisconnectedEvent()
        updateState {
            copy(
                versionNumber = null,
                latestVersionResponse = null
            )
        }
    }

    override suspend fun dispatchIntentOnIO(intent: HeadsetSettingContract.UIIntent) {
        super.dispatchIntentOnIO(intent)
        intent.checkIntent<HeadsetSettingContract.UIIntent.LoadSportsMode> {
            handleLoadSportsMode()
        }.checkIntent<HeadsetSettingContract.UIIntent.SetLowLatency> {
            handleSetLowLatency(it.lowLatencyMode)
        }.checkIntent<HeadsetSettingContract.UIIntent.GetLowLatency> {
            handleGetLowLatency()
        }.checkIntent<HeadsetSettingContract.UIIntent.ConnectDevice> {
            handleConnectDevice(it.mac, it.pid)
        }.checkIntent<HeadsetSettingContract.UIIntent.ResetNeedPopupUpdate> {
            updateState { copy(needPopupUpdate = false) }
        }.checkIntent<HeadsetSettingContract.UIIntent.UpdateMusicMode> {
            getMusicMode()
        }.checkIntent<HeadsetSettingContract.UIIntent.SyncCallStatus> {
            getCallStatus()
        }.checkIntent<HeadsetSettingContract.UIIntent.UpdateNeckReminderInterval> {
            getNeckReminderInterval()
        }.checkIntent<HeadsetSettingContract.UIIntent.SyncCurrentSportType> {
            getCurrentSportType()
        }.checkIntent<HeadsetSettingContract.UIIntent.SyncCurrentSportStatus> {
            getCurrentSportStatus(it.sportSupports)
        }.checkIntent<HeadsetSettingContract.UIIntent.SyncHeadphoneData> {
            syncHeadsetData()
        }.checkIntent<HeadsetSettingContract.UIIntent.GetColorType> {
            getColorType()
        }
    }

    private fun getCurrentSportStatus(supports: SportSupports) {
        launchOnIO {
            runSuspendCatching {
                baseHeadsetFeature.getSportStatus(supports)
            }.onSuccess { sportStatus ->
                updateState { copy(sportStatus = sportStatus) }
                sendConnectionAnalysisEvent(
                    AnalyticsEventProperty.SPORTS_SWITCH, when {
                        sportStatus.isRunning -> AnalyticsPropertyValue.SportSwitchValue.RUNNING
                        sportStatus.isOpenWaterSwimming -> AnalyticsPropertyValue.SportSwitchValue.OPEN_WATER_SWIMMING
                        sportStatus.isPoolSwimming -> AnalyticsPropertyValue.SportSwitchValue.POOL_SWIMMING
                        sportStatus.isJumpRoping -> AnalyticsPropertyValue.SportSwitchValue.JUMP_ROPE
                        else -> ""
                    }
                )
            }.onFailure {
                Timber.w(it, "get current sport status failed: ${it.message}")
            }
        }
    }

    private fun getCurrentSportType() {
        launchOnIO {
            runSuspendCatching {
                baseHeadsetFeature.getSportType()
            }.onSuccess { sportType ->
                updateState { copy(currentSportType = sportType) }
            }.onFailure {
                Timber.w(it, "get current sport type failed: ${it.message}")
            }
        }
    }

    private fun getNeckReminderInterval() {
        launchOnIO {
            runSuspendCatching {
                baseHeadsetFeature.getNeckReminderInterval().let {
                    updateState { copy(neckIntervalData = it) }
                    sendConnectionAnalysisEvent(
                        AnalyticsEventProperty.NECK_FATIGUE_ALERT,
                        if (it.enable) AnalyticsPropertyValue.TRUE else AnalyticsPropertyValue.FALSE
                    )
                }
            }.onFailure {
                Timber.w(it, "get neck interval failed: ${it.message}")
            }
        }
    }

    private fun getMusicMode() {
        launchOnIO {
            runSuspendCatching {
                baseHeadsetFeature.getMusicMode()?.let {
                    updateState { copy(musicMode = it) }
                    sendConnectionAnalysisEvent(
                        AnalyticsEventProperty.MUSIC_MODE, when (it) {
                            MusicMode.ONLINE -> AnalyticsPropertyValue.MusicModeValue.BLUETOOTH_MUSIC
                            MusicMode.OFFLINE -> AnalyticsPropertyValue.MusicModeValue.OFFLINE_MUSIC
                        }
                    )
                }
            }.onFailure {
                Timber.w(it, "get music mode failed: ${it.message}")
            }
        }
    }

    private fun getCallStatus() {
        launchOnIO {
            runSuspendCatching {
                baseHeadsetFeature.getCallingStatus()?.let {
                    updateState { copy(callStatus = it) }
                }
            }.onFailure {
                Timber.w(it, "get call status failed: ${it.message}")
            }
        }
    }

    fun initOtaStateObserve() = viewModelScope.launch {
        otaHelper.getOtaStateFlow()
            .onStart {
                updateState { copy(otaState = OtaState.None) }
                Timber.d("HeadsetSettingViewModel otaState onStart")
            }
            .distinctUntilChanged()
            .collect { otaState ->
                Timber.d("HeadsetSettingViewModel otaState:$otaState")
                updateState { copy(otaState = otaState) }
                if (otaState != OtaState.Succeed) return@collect
                Timber.d("HeadsetSettingViewModel prepare to connect")
                // After the OTA transmission is successful, the device needs time to installation and restart
                // App will reconnect device after 60s.
                delay(60_000)
                if (connectStates.getState().isConnected) {
                    Timber.d("HeadsetSettingViewModel isConnected")
                    return@collect
                }
                val mac = headsetConfigUseCase.getHeadsetLastMac()
                val pid = headsetConfigUseCase.getHeadsetPid()
                Timber.d("HeadsetSettingViewModel start connect")
                handleConnectDevice(mac, pid)
            }
    }

    private suspend fun handleConnectDevice(mac: String, pid: Int?) {
        connectHelper.handleConnectDevice(mac, pid, onConnectSuccess = {
            updateConnectionState(HeadsetSettingContract.ConnectState.Connected)
            if (state.latestVersionResponse == null) {
                viewModelScope.launch {
                    handleGetLatestVersion()
                }
            }
        }, onConnectFail = {
            Timber.w("HeadsetSettingViewModel handleConnectDevice: connect headphone failed $it")
            updateConnectionState(HeadsetSettingContract.ConnectState.Disconnected)
        }, retryCount = ConnectHelper.DEFAULT_RECONNECT_COUNT)
    }

    private suspend fun handleGetLowLatency() {
        runSuspendCatching {
            val lowLatencyMode = baseHeadsetFeature.getLowLatencyMode()
            updateState { copy(lowLatency = lowLatencyMode == LowLatencyMode.ON) }
        }.onFailure { thr ->
            Timber.w(thr, "handleGetLowLatency error: ${thr.message}")
        }
    }

    private suspend fun handleSetLowLatency(lowLatencyMode: LowLatencyMode) {
        runSuspendCatching {
            val result = baseHeadsetFeature.setLowLatencyMode(lowLatencyMode)
            updateState { copy(operationResult = result) }
        }.onFailure { thr ->
            Timber.w(thr, "handleSetLowLatency error: ${thr.message}")
            showErrorSnackBar(thr)
        }
    }

    private suspend fun handleGetBattery() {
        mutex.withLock {
            updateBatteryPower(null)
            val headsetConfig = setUserHeadsetConfigDataSource.getHeadsetConfig()
            val powerPercent = runSuspendCatching {
                val battery = deviceInfoFeatures.getBattery()
                headsetConfig.powerPercent = battery
                setUserHeadsetConfigDataSource.saveHeadsetConfig(headsetConfig)
                headsetConfig.powerPercent
            }.getOrElse { thr ->
                Timber.w(thr, "handleGetBattery error: ${thr.message}")
                if (thr is DisconnectionException) {
                    null
                } else {
                    headsetConfig.powerPercent
                }
            }
            updateBatteryPower(powerPercent)
        }
    }

    private suspend fun handleMultiBattery() {
        mutex.withLock {
            updateMultipleBatteryPower(null)
            val multipleBattery = runSuspendCatching {
                val battery = deviceInfoFeatures.getMultiBattery()
                val headsetConfig = setUserHeadsetConfigDataSource.getHeadsetConfig()
                headsetConfig.multipleBattery = MultipleBattery(
                    firstValue = battery.getPrimaryBattery(),
                    secondValue = battery.getSecondBattery(),
                    thirdValue = battery.getThirdBattery()
                )
                setUserHeadsetConfigDataSource.saveHeadsetConfig(headsetConfig)
                headsetConfig.multipleBattery
            }.getOrElse { thr ->
                Timber.w(thr, "handleGetBattery error: ${thr.message}")
                if (thr is DisconnectionException) {
                    null
                } else {
                    setUserHeadsetConfigDataSource.getHeadsetConfig().multipleBattery
                }
            }
            updateMultipleBatteryPower(multipleBattery)
        }
    }

    @SuppressLint("MissingPermission")
    private suspend fun handleGetDeviceInfo() {
        runSuspendCatching {
            if (state.versionNumber != null) return
            val updateFileInfo = deviceInfoFeatures.getUpdateFileInfo()
            val version = updateFileInfo.version?.formattedVersion() ?: ""
            updateState { copy(versionNumber = version) }
            mutex.withLock {
                val currentDevice = savedStateHandle.get<BluetoothDevice>(CURRENT_DEVICE)
                if (currentDevice == null) {
                    val headsetConfig = setUserHeadsetConfigDataSource.getHeadsetConfig()
                    headsetConfig.formattedVersion = version
                    setUserHeadsetConfigDataSource.saveHeadsetConfig(headsetConfig)
                } else {
                    val headsetConfig =
                        setUserHeadsetConfigDataSource.getHeadsetConfig(currentDevice.address)
                    headsetConfig.deviceName = currentDevice.validName()
                    headsetConfig.mac = currentDevice.address
                    headsetConfig.formattedVersion = version
                    setUserHeadsetConfigDataSource.saveHeadsetConfig(headsetConfig)
                    handleUploadDeviceInfo(
                        currentDevice.validName(),
                        currentDevice.address,
                        version
                    )
                }
            }
        }.onFailure { thr ->
            Timber.w(thr, "handleGetDeviceInfo error: ${thr.message}")
        }
    }

    private suspend fun handleGetLatestVersion() {
        mutex.withLock {
            val headsetConfig = setUserHeadsetConfigDataSource.getHeadsetConfig()
            val productName = runSuspendCatching {
                val productName = deviceInfoFeatures.getProductName()
                saveProductName(headsetConfig, productName)
                productName
            }.getOrElse { e ->
                Timber.w(e, "getProductName fail")
                headsetConfig.productName
            }
            if (productName.isNotEmpty()) {
                val updateFileInfo =
                    runSuspendCatching { deviceInfoFeatures.getUpdateFileInfo() }.getOrNull()
                val version = updateFileInfo?.version?.formattedVersion() ?: ""
                fetchOTAInfoUseCase.invoke(version, productName)
                    .onSuccess {
                        Timber.i("handleGetLatestVersion $it")
                        updateState { copy(latestVersionResponse = it) }
                        if (it.configId != null) {
                            updateState {
                                copy(needPopupUpdate = true)
                            }
                        }
                    }.onFailure {
                        Timber.w(it, "handleGetLatestVersion error: ${it.message}")
                    }
            }
        }
    }

    private fun initSettingItemState() {
        val headsetConfig = setUserHeadsetConfigDataSource.getHeadsetConfig()
        if (supportedHeadsetDevices.supportBodySense(baseHeadsetFeature)) {
            updateSettingItemState(
                SettingItemType.BODY_SENSE, when (headsetConfig.bodySenseEnabled) {
                    true -> R.string.headset_setting_on
                    false -> R.string.headset_setting_off
                }
            )
        }
        if (supportedHeadsetDevices.supportEqMode(baseHeadsetFeature)) {
            updateSettingItemState(
                SettingItemType.SOUND_MODE, when (headsetConfig.soundMode) {
                    EqMode.OUTDOOR -> R.string.outdoor
                    EqMode.UNDERWATER -> R.string.sound_mode_underwater
                    EqMode.NORMAL -> R.string.normal
                    else -> null
                }
            )
        }

        if (supportedHeadsetDevices.supportLEDLightMode(baseHeadsetFeature)) {
            updateSettingItemState(
                SettingItemType.INDICATOR, when (headsetConfig.sportsMode) {
                    SportsMode.CLOSE -> R.string.headset_setting_off
                    SportsMode.RUNNING -> R.string.custom_mode_running
                    SportsMode.SOS -> R.string.custom_mode_sos
                    SportsMode.TEAM -> R.string.custom_mode_constant_light
                    SportsMode.LEADER -> R.string.custom_mode_flashing
                    SportsMode.CYCLING -> R.string.custom_mode_cycling
                }
            )
        }
        if (supportedHeadsetDevices.supportDualDeviceConnect(baseHeadsetFeature)) {
            updateSettingItemState(
                SettingItemType.DUAL_DEVICE_CONNECTION,
                when (headsetConfig.dualDeviceConnectionEnabled) {
                    true -> R.string.headset_setting_on
                    false -> R.string.headset_setting_off
                }
            )
        }
    }

    private fun handleUploadDeviceInfo(
        name: String,
        headsetMac: String,
        formattedVersion: String
    ) = viewModelScope.launch {
        mutex.withLock {
            val headsetConfig = setUserHeadsetConfigDataSource.getHeadsetConfig()
            val productName = runSuspendCatching {
                val productName = deviceInfoFeatures.getProductName()
                saveProductName(headsetConfig, productName)
                productName
            }.getOrElse { e ->
                Timber.w(e, "getProductName fail")
                headsetConfig.productName
            }
            if (productName.isNotEmpty()) {
                val algorithmVersion = runSuspendCatching {
                    val algorithmVersion = deviceInfoFeatures.getAlgorithmVersion()
                    headsetConfig.algorithmVersion = algorithmVersion
                    setUserHeadsetConfigDataSource.saveHeadsetConfig(headsetConfig)
                    algorithmVersion
                }.getOrElse { e ->
                    Timber.w(e, "getAlgorithmVersion fail")
                    headsetConfig.algorithmVersion
                }
                runSuspendCatching {
                    headsetApi.uploadDeviceInfo(
                        DeviceBindInfoRequest(
                            name,
                            productModel = productName,
                            mac = headsetMac,
                            firmwareVersion = formattedVersion,
                            algorithmVersion = algorithmVersion
                        )
                    )
                }.onSuccess {
                    Timber.i("uploadDeviceInfo onSuccess")
                }.onFailure {
                    Timber.w(it, "uploadDeviceInfo error: ${it.message}")
                }
            }
        }
    }

    private fun saveProductName(headsetConfig: HeadsetConfig, productName: String) {
        headsetConfig.productName = productName
        setUserHeadsetConfigDataSource.saveHeadsetConfig(headsetConfig)
    }

    private suspend fun handleLoadSportsMode() {
        mutex.withLock {
            val headsetConfig = setUserHeadsetConfigDataSource.getHeadsetConfig()
            val sportsMode = runSuspendCatching {
                val sportsMode = baseHeadsetFeature.getSportsMode()
                headsetConfig.sportsMode = sportsMode
                setUserHeadsetConfigDataSource.saveHeadsetConfig(headsetConfig)
                headsetConfig.sportsMode
            }.getOrElse { thr ->
                Timber.w(thr, "get sports mode and sports custom mode failed.")
                headsetConfig.sportsMode
            }
            updateState {
                copy(
                    sportsMode = sportsMode,
                    ledState = if (sportsMode == SportsMode.CLOSE) LedStatus.CLOSE else LedStatus.OPEN
                )
            }
        }
    }

    private suspend fun getColorType() {
        mutex.withLock {
            val colorType = runSuspendCatching {
                val colorType = deviceInfoFeatures.getColorType()
                val headsetConfig = setUserHeadsetConfigDataSource.getHeadsetConfig()
                headsetConfig.colorType = colorType
                setUserHeadsetConfigDataSource.saveHeadsetConfig(headsetConfig)
                headsetConfig.colorType
            }.getOrElse { thr ->
                Timber.w(thr, "get color type failed.")
                setUserHeadsetConfigDataSource.getHeadsetConfig().colorType
            }
            updateState { copy(colorType = colorType) }
        }
    }

    companion object {
        private const val UPDATE_STATE_DELAY_TIME = 5 * 1000L // ms
    }
}
