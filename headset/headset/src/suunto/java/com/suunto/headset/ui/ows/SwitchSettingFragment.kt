package com.suunto.headset.ui.ows

import androidx.compose.runtime.Composable
import androidx.fragment.app.viewModels
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.theme.M3AppTheme
import com.suunto.extension.popBackStack
import com.suunto.headset.viewmodel.SwitchSettingViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SwitchSettingFragment : BaseFragment() {
    private val viewModel by viewModels<SwitchSettingViewModel>()

    @Composable
    override fun SetContentView() {
        M3AppTheme {
            SwitchSettingScreen(
                switchSettingInfo = viewModel.switchSettingInfo.value,
                onBackClick = {
                    popBackStack()
                },
            )
        }
    }

    companion object {
        const val KEY_SWITCH_SETTING_TYPE =
            "com.suunto.headset.ui.ows.SwitchSettingFragment.KEY_SWITCH_SETTING_TYPE"
    }
}
