package com.suunto.headset.ui.ows

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.suunto.headset.R
import com.suunto.headset.contract.HeadsetSettingContract
import com.suunto.headset.model.FindHeadphoneInfo
import com.suunto.headset.model.FindHeadphoneType
import com.suunto.headset.model.HeadphoneConnectionState
import com.suunto.headset.model.getAllHeadphoneConnectionState
import com.suunto.headset.model.getDisconnectedMessage
import com.suunto.headset.ui.components.CommonTopAppBar
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

@Composable
fun FindHeadphoneScreen(
    headphoneConnectionState: HeadphoneConnectionState,
    findHeadphoneInfos: ImmutableList<FindHeadphoneInfo>,
    onClickItem: (FindHeadphoneInfo) -> Unit,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val snackbarState = remember { SnackbarHostState() }
    val context = LocalContext.current
    Scaffold(modifier = modifier, topBar = {
        CommonTopAppBar(
            title = stringResource(R.string.find_headphone_title),
            onBackClick = onBackClick
        )
    }, snackbarHost = {
        SnackbarHost(snackbarState)
    }) { innerPadding ->
        LaunchedEffect(headphoneConnectionState) {
            val messageResId = headphoneConnectionState.getDisconnectedMessage()
            messageResId?.let {
                val message = context.getString(it)
                snackbarState.showSnackbar(message)
            }

        }
        Column(
            modifier = Modifier
                .padding(innerPadding)
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.surface)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.spacing.medium)
            ) {
                val leftConnected = headphoneConnectionState.left == HeadsetSettingContract.ConnectState.Connected
                val rightConnected = headphoneConnectionState.right == HeadsetSettingContract.ConnectState.Connected
                Image(
                    modifier = Modifier
                        .weight(1f)
                        .alpha(if (leftConnected) 1f else 0.5f),
                    painter = painterResource(R.drawable.icon_left_headphone),
                    contentDescription = null,
                )
                Image(
                    modifier = Modifier
                        .weight(1f)
                        .alpha(if (rightConnected) 1f else 0.5f),
                    painter = painterResource(R.drawable.icon_right_headphone),
                    contentDescription = null,
                )
            }
            Text(
                modifier = Modifier.padding(MaterialTheme.spacing.medium),
                text = stringResource(id = R.string.find_headphone_notice),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            findHeadphoneInfos.forEach {
                FindHeadphoneItem(
                    connectionState = when (it.type) {
                        FindHeadphoneType.ONLY_LEFT -> headphoneConnectionState.left
                        FindHeadphoneType.ONLY_RIGHT -> headphoneConnectionState.right
                        FindHeadphoneType.BOTH -> headphoneConnectionState.getAllHeadphoneConnectionState()
                    },
                    info = it,
                    onClickItem = onClickItem,
                )
                HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
            }
        }
    }
}

@Composable
private fun FindHeadphoneItem(
    info: FindHeadphoneInfo,
    connectionState: HeadsetSettingContract.ConnectState,
    onClickItem: (FindHeadphoneInfo) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .alpha(
                if (connectionState == HeadsetSettingContract.ConnectState.Disconnected) 0.5f else 1f
            )
            .clickable(enabled = connectionState == HeadsetSettingContract.ConnectState.Connected) {
                onClickItem(info)
            }
            .fillMaxWidth()
            .padding(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            modifier = Modifier.weight(1f),
            text = stringResource(
                id = when (info.type) {
                    FindHeadphoneType.ONLY_LEFT -> R.string.only_left
                    FindHeadphoneType.ONLY_RIGHT -> R.string.only_right
                    FindHeadphoneType.BOTH -> R.string.both
                }
            ),
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.Left
        )
        if (info.playedSound) {
            val composition by rememberLottieComposition(
                LottieCompositionSpec.Asset(
                    stringResource(
                        R.string.headphone_play_sound
                    )
                )
            )
            LottieAnimation(
                composition = composition,
                iterations = LottieConstants.IterateForever,
            )
        } else {
            Icon(
                modifier = Modifier
                    .size(MaterialTheme.iconSizes.small),
                painter = painterResource(R.drawable.icon_play_sound),
                contentDescription = null,
                tint = Color.Unspecified
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun FindHeadphoneScreenPreview() {
    val mockInfos = persistentListOf(
        FindHeadphoneInfo(
            FindHeadphoneType.ONLY_LEFT,
            playedSound = false,
        ),
        FindHeadphoneInfo(
            FindHeadphoneType.ONLY_RIGHT,
            playedSound = false,
        ),
        FindHeadphoneInfo(FindHeadphoneType.BOTH, playedSound = false)
    )

    M3AppTheme {
        FindHeadphoneScreen(
            headphoneConnectionState = HeadphoneConnectionState(
                left = HeadsetSettingContract.ConnectState.Connected,
                right = HeadsetSettingContract.ConnectState.Disconnected
            ),
            findHeadphoneInfos = mockInfos,
            onClickItem = {},
            onBackClick = {}
        )
    }
}
