package com.suunto.headset.model

import androidx.annotation.StringRes

data class SwitchSettingInfo(
    @StringRes val topBarTitleRes: Int,
    @StringRes val nameRes: Int,
    val switchState: Boolean,
    val switchSettingType: SwitchSettingType,
    val onSwitchChange: (Boolean) -> Unit,
    @StringRes val descriptionRes: Int? = null,
)

enum class SwitchSettingType {
    NOISE_REDUCTION, SPATIAL_AUDIO,
}
