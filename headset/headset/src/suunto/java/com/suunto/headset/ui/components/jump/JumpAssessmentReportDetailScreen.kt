package com.suunto.headset.ui.components.jump

import android.icu.text.DecimalFormat
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.TextUnitType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.utils.Utils
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodyMegaBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.darkestGrey
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.theme.suuntoCoach
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.ui.utils.TextFormatter
import com.suunto.headset.R
import com.suunto.headset.assessmentstandard.JumpAssessmentHelper
import com.suunto.headset.assessmentstandard.NeuromuscularFatigueLevel
import com.suunto.headset.model.JumpAssessmentGraphData
import com.suunto.headset.model.JumpAssessmentGraphType
import com.suunto.headset.model.JumpAssessmentItemValue
import com.suunto.headset.model.JumpAssessmentPointValue
import com.suunto.headset.model.JumpAssessmentReportDetail
import com.suunto.headset.ui.components.CommonTopAppBar
import com.suunto.headset.ui.components.ReassessConfirmDialog
import com.suunto.headset.ui.components.SU07GraphLineChartView
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlin.math.roundToInt

@Composable
fun JumpAssessmentReportDetailScreen(
    reportDetail: JumpAssessmentReportDetail,
    saveAndReassess: () -> Unit,
    abandonAndReassess: () -> Unit,
    onBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Scaffold(
        modifier = modifier,
        topBar = {
            CommonTopAppBar(
                title = stringResource(id = R.string.jump_assessment_result),
                onBackClick = onBack,
                navigationIcon = {
                    Icon(SuuntoIcons.ActionBack.asPainter(), "")
                }
            )
        },
        containerColor = MaterialTheme.colors.surface
    ) { padding ->
        BackHandler {
            // intercept system back
        }
        ContentCenteringColumn(
            modifier = Modifier
                .padding(padding)
                .background(MaterialTheme.colors.surface)
                .verticalScroll(state = rememberScrollState())
        ) {
            var showReassessConfirmDialog by remember {
                mutableStateOf(false)
            }
            Column(Modifier.padding(MaterialTheme.spacing.medium)) {
                Text(
                    text = buildAnnotatedString {
                        append(stringResource(id = R.string.hi))
                        withStyle(
                            SpanStyle(
                                fontWeight = FontWeight.Bold,
                                color = MaterialTheme.colors.nearBlack
                            )
                        ) {
                            append(reportDetail.name)
                        }
                        append("\n${stringResource(id = R.string.jump_assessment_result_hint)}")
                    },
                    color = MaterialTheme.colors.darkGrey,
                    style = MaterialTheme.typography.bodyLarge
                )
                Text(
                    modifier = Modifier.padding(vertical = MaterialTheme.spacing.xlarge),
                    text = stringResource(id = getNeuromuscularFatigueResultSummaryRes(reportDetail.level)),
                    color = reportDetail.level.color,
                    style = MaterialTheme.typography.bodyMegaBold
                )
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.icon_suunto_coach_tag),
                        contentDescription = "",
                        tint = Color.Unspecified
                    )
                    Text(
                        modifier = Modifier.padding(start = MaterialTheme.spacing.small),
                        text = stringResource(id = R.string.suunto_coach),
                        color = MaterialTheme.colors.suuntoCoach,
                        style = MaterialTheme.typography.bodyLargeBold
                    )
                }
                Text(
                    modifier = Modifier.padding(top = MaterialTheme.spacing.small),
                    text = stringResource(id = getNeuromuscularFatigueAdviceRes(reportDetail.level)),
                    color = MaterialTheme.colors.darkestGrey,
                    style = MaterialTheme.typography.body
                )
                NeuromuscularFatigueValue(zScore = reportDetail.zScore)
            }
            val showGraph =
                reportDetail.graphData.isNotEmpty() && reportDetail.graphData.first().data.isNotEmpty()
            if (showGraph) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(50.dp)
                        .background(MaterialTheme.colors.lightGrey),
                    contentAlignment = Alignment.CenterStart
                ) {
                    Text(
                        modifier = Modifier.padding(MaterialTheme.spacing.medium),
                        text = stringResource(id = R.string.comparison_data),
                        color = MaterialTheme.colors.darkGrey,
                        style = MaterialTheme.typography.body,
                    )
                }
                reportDetail.graphData.forEach {
                    JumpAssessmentDetailGraph(
                        when (it.type) {
                            JumpAssessmentGraphType.JUMP_HEIGHT -> buildString {
                                append(
                                    TextFormatter.formatRoundJumpHeight(
                                        reportDetail.currentMeasurementValue.jumpHeightInCm,
                                        reportDetail.unit
                                    )
                                )
                                append(" ")
                                append(stringResource(id = reportDetail.unit.shorterDistanceUnitResId))
                            }

                            JumpAssessmentGraphType.FLIGHT_TIME -> buildString {
                                append(reportDetail.currentMeasurementValue.flightTimeInMs.toString())
                                append(" ")
                                append(stringResource(id = com.stt.android.core.R.string.ms))
                            }

                            JumpAssessmentGraphType.TAKEOFF_V -> buildString {
                                append(
                                    TextFormatter.formatTakeoffVelocity(
                                        reportDetail.currentMeasurementValue.takeoffVelocityInMeterPerSecond,
                                        reportDetail.unit
                                    )
                                )
                                append(" ")
                                append(stringResource(id = reportDetail.unit.shortSpeedUnitResId))
                            }
                            // do not support
                            JumpAssessmentGraphType.N_FATIGUE -> ""
                        }, avgValueUnit = when (it.type) {
                            JumpAssessmentGraphType.JUMP_HEIGHT -> stringResource(
                                id = reportDetail.unit.shorterDistanceUnitResId
                            )

                            JumpAssessmentGraphType.FLIGHT_TIME -> stringResource(id = com.stt.android.core.R.string.ms)
                            JumpAssessmentGraphType.TAKEOFF_V -> stringResource(id = reportDetail.unit.shortSpeedUnitResId)
                            // do not support
                            JumpAssessmentGraphType.N_FATIGUE -> ""
                        }, it
                    )
                }
            }

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(80.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    modifier = Modifier
                        .padding(MaterialTheme.spacing.medium)
                        .clickable {
                            showReassessConfirmDialog = true
                        },
                    text = stringResource(id = R.string.reassess).uppercase(),
                    color = MaterialTheme.colors.primary,
                    style = MaterialTheme.typography.bodyBold,
                )
            }

            if (showReassessConfirmDialog) {
                ReassessConfirmDialog(
                    onDismissRequest = { showReassessConfirmDialog = false },
                    abandonAndReassess = abandonAndReassess,
                    saveAndReassess = saveAndReassess
                )
            }
        }
    }
}

@Composable
private fun NeuromuscularFatigueValue(zScore: Float, modifier: Modifier = Modifier) {
    Column(
        modifier = modifier
            .padding(top = MaterialTheme.spacing.xlarge)
            .fillMaxWidth()
    ) {
        Text(
            text = stringResource(id = R.string.neuromuscular_fatigue),
            color = MaterialTheme.colors.nearBlack,
            style = MaterialTheme.typography.bodyLargeBold
        )
        ConstraintLayout(
            modifier = Modifier.padding(
                top = MaterialTheme.spacing.xlarge,
                bottom = MaterialTheme.spacing.medium
            )
        ) {
            val startGuideline =
                createGuidelineFromStart(JumpAssessmentHelper.getZScoreRatio(zScore))
            val (iconRef, progressRes, dashLineRef) = createRefs()
            Icon(
                modifier = Modifier.constrainAs(iconRef) {
                    end.linkTo(startGuideline)
                    bottom.linkTo(progressRes.top)
                },
                imageVector = Icons.Default.ArrowDropDown,
                contentDescription = ""
            )
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(progressRes) {
                        top.linkTo(iconRef.bottom)
                    }) {
                Box(
                    modifier = Modifier
                        .weight(1.0f)
                        .height(8.dp)
                        .clip(
                            RoundedCornerShape(
                                topStart = MaterialTheme.spacing.xsmall,
                                bottomStart = MaterialTheme.spacing.xsmall
                            )
                        )
                        .background(NeuromuscularFatigueLevel.PEAK.color)
                )
                Box(
                    modifier = Modifier
                        .weight(1.0f)
                        .height(8.dp)
                        .background(NeuromuscularFatigueLevel.HIGHER.color)
                )
                Box(
                    modifier = Modifier
                        .weight(1.0f)
                        .height(8.dp)
                        .background(NeuromuscularFatigueLevel.LESS.color)
                )
                Box(
                    modifier = Modifier
                        .weight(1.0f)
                        .height(8.dp)
                        .clip(
                            RoundedCornerShape(
                                topEnd = MaterialTheme.spacing.xsmall,
                                bottomEnd = MaterialTheme.spacing.xsmall
                            )
                        )
                        .background(NeuromuscularFatigueLevel.LEAST.color)
                )
            }
            val pathEffect = PathEffect.dashPathEffect(floatArrayOf(5f, 5f), 0f)
            Canvas(
                Modifier
                    .size(2.dp, 20.dp)
                    .constrainAs(dashLineRef) {
                        top.linkTo(iconRef.bottom)
                        bottom.linkTo(progressRes.bottom)
                        centerHorizontallyTo(parent)
                    }
                    .padding(start = 1.dp)) {
                drawLine(
                    color = Color.Black,
                    start = Offset(0f, 0f),
                    end = Offset(0f, size.height),
                    pathEffect = pathEffect,
                    strokeWidth = 1.dp.toPx()
                )
            }
        }

        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
            Text(
                text = "-1",
                color = MaterialTheme.colors.nearBlack,
                style = MaterialTheme.typography.bodyLarge
            )
            Text(
                text = "0",
                color = MaterialTheme.colors.nearBlack,
                style = MaterialTheme.typography.bodyLarge
            )
            Text(
                text = "1",
                color = MaterialTheme.colors.nearBlack,
                style = MaterialTheme.typography.bodyLarge
            )
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = MaterialTheme.spacing.medium),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = stringResource(id = R.string.fatigue_high),
                color = MaterialTheme.colors.nearBlack,
                style = MaterialTheme.typography.body
            )
            Text(
                text = stringResource(id = R.string.fatigue_free),
                color = MaterialTheme.colors.nearBlack,
                style = MaterialTheme.typography.body
            )
        }
    }
}

@Composable
private fun JumpAssessmentDetailGraph(
    currentValue: String,
    avgValueUnit: String,
    jumpAssessmentGraphData: JumpAssessmentGraphData,
    modifier: Modifier = Modifier
) {
    val avgValueFormatter = remember {
        DecimalFormat("0.0")
    }
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(bottom = MaterialTheme.spacing.medium)
    ) {
        Box(
            Modifier
                .height(56.dp)
                .padding(horizontal = MaterialTheme.spacing.medium),
            contentAlignment = Alignment.CenterStart
        ) {
            Text(
                text = stringResource(id = getGraphTitleRes(jumpAssessmentGraphData.type)),
                color = MaterialTheme.colors.nearBlack,
                style = MaterialTheme.typography.bodyLarge,
            )
        }

        Divider(color = MaterialTheme.colors.lightGrey)
        Row(
            modifier = Modifier.padding(
                top = MaterialTheme.spacing.medium,
                start = MaterialTheme.spacing.medium,
                end = MaterialTheme.spacing.medium
            )
        ) {
            Text(
                modifier = Modifier.weight(1.0f),
                text = buildAnnotatedString {
                    append(stringResource(id = R.string.measured_now))
                    append("\n")
                    withStyle(
                        SpanStyle(
                            fontWeight = FontWeight.Bold, fontSize = TextUnit(
                                16f,
                                TextUnitType.Sp
                            ), color = MaterialTheme.colors.primary
                        )
                    ) {
                        append(currentValue)
                    }
                },
                color = MaterialTheme.colors.nearBlack,
                style = MaterialTheme.typography.bodySmall,
                lineHeight = 22.sp
            )
            Text(
                modifier = Modifier.weight(1.0f),
                text = buildAnnotatedString {
                    append(stringResource(id = com.stt.android.R.string.avg))
                    append("\n")
                    withStyle(
                        SpanStyle(
                            fontWeight = FontWeight.Bold, fontSize = TextUnit(
                                16f,
                                TextUnitType.Sp
                            )
                        )
                    ) {
                        append(
                            when (jumpAssessmentGraphData.type) {
                                JumpAssessmentGraphType.N_FATIGUE, JumpAssessmentGraphType.JUMP_HEIGHT, JumpAssessmentGraphType.FLIGHT_TIME ->
                                    jumpAssessmentGraphData.averageValue.roundToInt().toString()

                                JumpAssessmentGraphType.TAKEOFF_V -> avgValueFormatter.format(
                                    jumpAssessmentGraphData.averageValue
                                )
                            }
                        )
                        append(" ")
                        append(avgValueUnit)
                    }
                },
                color = MaterialTheme.colors.nearBlack,
                style = MaterialTheme.typography.bodySmall,
                lineHeight = 22.sp
            )
        }
        SU07GraphLineChartView(
            chartTitle = stringResource(id = getGraphTitleRes(jumpAssessmentGraphData.type)),
            data = jumpAssessmentGraphData.data.map {
                Entry(it.time.toFloat(), it.value)
            }.toImmutableList(),
            dataUnit = avgValueUnit,
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.xsmall),
            limitLineValues = persistentListOf(
                Pair(
                    jumpAssessmentGraphData.averageValue,
                    colorResource(R.color.near_black).toArgb()
                )
            ),
            circleColors = persistentListOf(MaterialTheme.colors.nearBlack.toArgb()),
            intValue = when (jumpAssessmentGraphData.type) {
                JumpAssessmentGraphType.JUMP_HEIGHT, JumpAssessmentGraphType.N_FATIGUE, JumpAssessmentGraphType.FLIGHT_TIME -> true
                JumpAssessmentGraphType.TAKEOFF_V -> false
            }
        ) { lineChart ->
            lineChart.apply {
                axisLeft.setLabelCount(5, true)
                axisLeft.xOffset = Utils.convertDpToPixel(5f)
            }

        }
    }
}

private fun getGraphTitleRes(type: JumpAssessmentGraphType): Int {
    return when (type) {
        JumpAssessmentGraphType.N_FATIGUE -> R.string.neuromuscular_fatigue
        JumpAssessmentGraphType.JUMP_HEIGHT -> R.string.jump_height
        JumpAssessmentGraphType.FLIGHT_TIME -> R.string.flight_time
        JumpAssessmentGraphType.TAKEOFF_V -> R.string.takeoff_v
    }
}

private fun getNeuromuscularFatigueAdviceRes(neuromuscularFatigueLevel: NeuromuscularFatigueLevel): Int {
    return when (neuromuscularFatigueLevel) {
        NeuromuscularFatigueLevel.LEAST -> R.string.least_neuromuscular_fatigue_advice
        NeuromuscularFatigueLevel.LESS -> R.string.less_neuromuscular_fatigue_advice
        NeuromuscularFatigueLevel.MODERATE -> R.string.moderate_neuromuscular_fatigue_advice
        NeuromuscularFatigueLevel.HIGHER -> R.string.higher_neuromuscular_fatigue_advice
        NeuromuscularFatigueLevel.PEAK -> R.string.extreme_neuromuscular_fatigue_advice
    }
}

private fun getNeuromuscularFatigueResultSummaryRes(neuromuscularFatigueLevel: NeuromuscularFatigueLevel): Int {
    return when (neuromuscularFatigueLevel) {
        NeuromuscularFatigueLevel.LEAST -> R.string.no_neuromuscular_fatigue
        NeuromuscularFatigueLevel.LESS -> R.string.some_neuromuscular_fatigue
        NeuromuscularFatigueLevel.MODERATE -> R.string.moderate_neuromuscular_fatigue
        NeuromuscularFatigueLevel.HIGHER -> R.string.higher_neuromuscular_fatigue
        NeuromuscularFatigueLevel.PEAK -> R.string.extreme_neuromuscular_fatigue
    }
}

@Preview
@Composable
private fun NeuromuscularFatigueValuePreview() {
    NeuromuscularFatigueValue(
        zScore = 0f,
        modifier = Modifier.background(MaterialTheme.colors.surface)
    )
}

@Preview
@Composable
private fun JumpAssessmentReportDetailScreenPreview() {
    JumpAssessmentReportDetailScreen(
        reportDetail = JumpAssessmentReportDetail(
        "aaa",
        NeuromuscularFatigueLevel.LESS,
        1.0f,
        JumpAssessmentItemValue(14f, 345, 3.2f),
        persistentListOf(
            JumpAssessmentGraphData(
                JumpAssessmentGraphType.JUMP_HEIGHT,
                persistentListOf(JumpAssessmentPointValue(1110011111, 10f))
            )
        ),
        MeasurementUnit.IMPERIAL
    ), onBack = {}, saveAndReassess = {}, abandonAndReassess = {})
}
