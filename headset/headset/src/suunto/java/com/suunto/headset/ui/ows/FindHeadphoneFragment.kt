package com.suunto.headset.ui.ows

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.fragment.app.viewModels
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.theme.M3AppTheme
import com.suunto.extension.popBackStack
import com.suunto.headset.viewmodel.FindHeadphoneViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FindHeadphoneFragment : BaseFragment() {
    private val viewModel by viewModels<FindHeadphoneViewModel>()

    @Composable
    override fun SetContentView() {
        M3AppTheme {
            FindHeadphoneScreen(
                findHeadphoneInfos = viewModel.findHeadphoneInfos.collectAsState().value,
                headphoneConnectionState = viewModel.headphoneConnectionState.collectAsState().value,
                onBackClick = {
                    popBackStack()
                },
                onClickItem = {
                    viewModel.handleFindHeadphone(it)
                }
            )
        }
    }
}
