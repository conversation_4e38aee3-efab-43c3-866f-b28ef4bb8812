package com.suunto.headset.ui

import android.content.res.Configuration
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.findNavController
import com.google.android.material.snackbar.Snackbar
import com.squareup.moshi.Moshi
import com.suunto.headset.R
import com.suunto.headset.databinding.ActivityHeadsetBinding
import com.suunto.headset.repository.CurrentlyPairedHeadsetConfigUseCase
import com.suunto.headset.viewmodel.ConnectHelper
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 *  The headset Setting Activity contain navigation
 */
@AndroidEntryPoint
class HeadsetActivity : AppCompatActivity() {
    private var showAboutDevice = false
    private var showInfo = false
    private var showFindHeadphone = false
    private var backEnable = true
    private var stateTipBar: Snackbar? = null
    private lateinit var binding: ActivityHeadsetBinding

    @Inject
    lateinit var moshi: <PERSON><PERSON>

    @Inject
    lateinit var currentlyPairedHeadsetConfigUseCase: CurrentlyPairedHeadsetConfigUseCase

    @Inject
    lateinit var connectHelper: ConnectHelper

    private val controller by lazy {
        findNavController(R.id.nav_host_fragment_content_main)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityHeadsetBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun onSupportNavigateUp(): Boolean {
        if (backEnable) {
            onBackPressed()
        } else {
            Snackbar.make(binding.root, getString(R.string.not_allowed_exit), Snackbar.LENGTH_SHORT).show()
        }
        return true
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.headset_setting_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.aboutDevice -> {
                controller.navigate(R.id.fragment_device_info)
            }
            R.id.find_headphone -> {
                controller.navigate(R.id.find_headphone)
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onPrepareOptionsMenu(menu: Menu?): Boolean {
        menu?.findItem(R.id.aboutDevice)?.isVisible = showAboutDevice
        menu?.findItem(R.id.infoOutline)?.isVisible = showInfo
        menu?.findItem(R.id.find_headphone)?.isVisible = showFindHeadphone
        return super.onPrepareOptionsMenu(menu)
    }

    fun setShowAboutDeviceMenu(visible: Boolean) {
        showAboutDevice = visible
        invalidateOptionsMenu()
    }

    fun setShowInfoMenu(visible: Boolean) {
        showInfo = visible
        invalidateOptionsMenu()
    }

    fun setShowFindHeadphone(visible: Boolean) {
        showFindHeadphone = visible
        invalidateOptionsMenu()
    }

    fun setBackEnabled(enable: Boolean) {
        backEnable = enable
    }

    fun showConnectStatus(disconnected: Boolean) {
        if (disconnected) {
            stateTipBar = stateTipBar ?: kotlin.run {
                Snackbar.make(
                    binding.root,
                    getString(R.string.headphone_not_connected),
                    Snackbar.LENGTH_INDEFINITE
                )
            }
            stateTipBar?.takeIf { !it.isShown }?.show()
        } else {
            stateTipBar?.dismiss()
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        // for customization button fragment, need to distinguish between horizontal and vertical screens
        if (controller.currentDestination?.id == R.id.fragment_customize_button) {
            controller.popBackStack()
            controller.navigate(R.id.fragment_customize_button)
        }
    }

    override fun onDestroy() {
        connectHelper.disconnectIfNotInOTA()
        setSupportActionBar(null)
        super.onDestroy()
    }
}
