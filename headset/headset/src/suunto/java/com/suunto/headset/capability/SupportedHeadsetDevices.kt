package com.suunto.headset.capability

import android.content.SharedPreferences
import com.stt.android.utils.STTConstants.FeatureTogglePreferences
import com.suunto.headset.model.ButtonType
import com.suunto.headset.model.DeviceCapability
import com.suunto.headset.model.HeadsetButton
import com.suunto.headset.model.LEDLightItem
import com.suunto.headset.model.SportSupports
import com.suunto.headset.model.SportType
import com.suunto.headset.repository.SetUserHeadsetConfigDataSource
import com.suunto.headset.ui.onboarding.HeadsetOnBoardingPage
import com.suunto.soa.ble.control.attr.EqMode
import com.suunto.soa.ble.control.attr.SportsMode
import com.suunto.soa.ble.response.ButtonFunction
import com.suunto.soa.ble.response.ButtonShortcutKey
import javax.inject.Inject

class SupportedHeadsetDevices @Inject constructor(
    @com.stt.android.di.FeatureTogglePreferences
    private val featureTogglePreferences: SharedPreferences,
    private val setUserHeadsetConfigDataSource: SetUserHeadsetConfigDataSource,
) {
    private val deviceCapability: DeviceCapability
        get() = setUserHeadsetConfigDataSource.getHeadsetConfig().deviceCapability

    fun supportLanguageSetting(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportLanguageSetting()
    }

    fun supportEqMode(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportEqMode()
    }

    fun supportBodySense(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportBodySense()
    }

    fun supportDualDeviceConnect(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportDualDeviceConnect()
    }

    fun supportGetSerial(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportGetSerial()
    }

    fun supportLowLatency(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportLowLatency()
    }

    fun supportGetConnectedDevices(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportGetConnectedDevices()
    }

    fun supportLEDLightMode(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportLEDLightMode()
    }

    fun supportOfflineMusic(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportOfflineMusic()
    }

    fun supportNeckMovementAssessment(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportNeckMovementAssessment()
    }

    fun supportButtonCustomization(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportButtonCustomization()
    }

    fun supportSwimFeature(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportSwimFeature()
    }

    fun supportJumpRopeFeature(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportJumpRopeFeature(setUserHeadsetConfigDataSource.getHeadsetConfig().deviceCapability)
    }

    fun supportRunningFeature(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportRunningFeature(setUserHeadsetConfigDataSource.getHeadsetConfig().deviceCapability)
    }

    fun getSportSupports(baseHeadsetFeature: BaseHeadsetFeature): SportSupports {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.getSportSupports(setUserHeadsetConfigDataSource.getHeadsetConfig().deviceCapability)
    }

    fun getUserGuideUrl(baseHeadsetFeature: BaseHeadsetFeature): Int? {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.getUserGuideUrl()
    }

    fun getMoreInformationUrl(baseHeadsetFeature: BaseHeadsetFeature): Int? {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.getMoreInformationUrl()
    }

    fun getFunctionIntroduction(baseHeadsetFeature: BaseHeadsetFeature): List<HeadsetOnBoardingPage> {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.getFunctionIntroduction()
    }

    fun getSportsModeList(baseHeadsetFeature: BaseHeadsetFeature): List<SportsMode> {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.getSportsModeList()
    }

    fun getSoundModeNormalPhotoResIds(baseHeadsetFeature: BaseHeadsetFeature): Int? {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.getSoundModeNormalPhotoResIds()
    }

    fun getSoundModeOutdoorPhotoResIds(baseHeadsetFeature: BaseHeadsetFeature): Int? {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.getSoundModeOutdoorPhotoResIds()
    }

    fun getButtonList(baseHeadsetFeature: BaseHeadsetFeature): List<HeadsetButton> {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.getButtonList()
    }

    fun getButtonShortcutKeyList(baseHeadsetFeature: BaseHeadsetFeature): List<ButtonShortcutKey> {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.getCustomizationShortcutKeyList()
    }

    fun getButtonType(
        baseHeadsetFeature: BaseHeadsetFeature,
        buttonFunction: ButtonFunction
    ): ButtonType {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.getHeadsetButtonType(buttonFunction)
    }

    fun getSoundModeList(baseHeadsetFeature: BaseHeadsetFeature): List<EqMode> {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.getSoundModeList()
    }

    fun isCheckDeviceAvailableBeforeOTA(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.isCheckDeviceAvailableBeforeOTA()
    }

    fun supportMusicModeFeature(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportMusicModeFeature()
    }

    fun supportCallStatusFeature(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportCallStatusFeature()
    }

    fun supportJumpAssessmentFeature(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportJumpAssessment()
    }

    fun supportSportsSwitchFeature(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportSportsSwitch(deviceCapability)
    }

    fun supportSportButtonModify(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportSportButtonModify(deviceCapability)
    }

    fun getSportTypes(baseHeadsetFeature: BaseHeadsetFeature): List<SportType> {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        val sportTypeList = capabilities.getSportTypes(deviceCapability).toMutableList()
        if (!featureTogglePreferences.getBoolean(
                FeatureTogglePreferences.KEY_ENABLE_HEADSET_RUNNING,
                FeatureTogglePreferences.KEY_ENABLE_HEADSET_RUNNING_DEFAULT
            )
        ) {
            sportTypeList.remove(SportType.RUNNING)
        }
        return sportTypeList
    }

    fun getDefaultSportType(baseHeadsetFeature: BaseHeadsetFeature): SportType? {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.getDefaultSportType()
    }

    fun getHeadMovementInstructions(baseHeadsetFeature: BaseHeadsetFeature): List<HeadControlInstruction> {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.getHeadControlInstructions()
    }

    fun supportPoolSwimSport(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportsPoolSwimSport(deviceCapability)
    }

    fun supportPoolDistanceSetting(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportsPoolDistanceSetting(deviceCapability)
    }

    fun supportUserInfoSetting(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportsUserInfoSetting(deviceCapability)
    }

    fun supportGetDeviceLedStatus(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportsGetLedStatus()
    }

    fun getLightModeList(baseHeadsetFeature: BaseHeadsetFeature): List<LEDLightItem> {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.getLightModeList()
    }

    fun supportGetColorType(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportGetColorType()
    }

    fun supportMultipleBattery(baseHeadsetFeature: BaseHeadsetFeature): Boolean {
        val deviceType = baseHeadsetFeature.getHeadsetBtDevice().headsetDeviceType
        val capabilities = HeadsetCapabilityProvider[deviceType]
        return capabilities.supportMultiBattery()
    }

}
