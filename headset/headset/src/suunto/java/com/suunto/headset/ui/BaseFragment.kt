package com.suunto.headset.ui

import android.animation.ObjectAnimator
import android.animation.StateListAnimator
import android.os.Bundle
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.annotation.LayoutRes
import androidx.annotation.StringRes
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import by.kirich1409.viewbindingdelegate.viewBinding
import com.google.android.material.snackbar.Snackbar
import com.suunto.extension.convertDpToPixel
import com.suunto.extension.showSnackBar
import com.suunto.headset.R
import com.suunto.headset.databinding.FragmentBaseBinding
import timber.log.Timber

abstract class BaseFragment : Fragment(R.layout.fragment_base) {
    private val binding by viewBinding(FragmentBaseBinding::bind)

    @LayoutRes
    protected abstract fun getLayoutResId(): Int

    protected open fun showAboutDeviceMenu(): Boolean = false

    protected open fun showInfoMenu(): Boolean = false

    protected open fun showFindHeadphoneMenu(): Boolean = false

    @StringRes
    protected open fun getTitleResId(): Int = 0

    protected open fun appbarElevationEnabled(): Boolean = true

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val content = layoutInflater.inflate(getLayoutResId(), null)
        binding.layoutContent.addView(content)
        if (getTitleResId() != 0) {
            setupToolbar()
        }
        (activity as? HeadsetActivity)?.setShowAboutDeviceMenu(showAboutDeviceMenu())
        (activity as? HeadsetActivity)?.setShowInfoMenu(showInfoMenu())
        (activity as? HeadsetActivity)?.setShowFindHeadphone(showFindHeadphoneMenu())
    }

    protected fun setupToolbar(title: String = "") {
        if (appbarElevationEnabled()) {
            val stateListAnimator = StateListAnimator()
            stateListAnimator.addState(
                IntArray(0),
                ObjectAnimator.ofFloat(binding.fragmentBaseAppbar, "elevation", resources.convertDpToPixel(4f))
            )
            binding.fragmentBaseAppbar.stateListAnimator = stateListAnimator
        }
        (activity as? AppCompatActivity)?.setSupportActionBar(binding.fragmentBaseToolbar)
        (activity as? AppCompatActivity)?.supportActionBar?.let {
            it.setDisplayShowHomeEnabled(false)
            it.setDisplayHomeAsUpEnabled(true)
            if (title.isEmpty() && getTitleResId() != 0) {
                it.setTitle(getTitleResId())
            } else {
                it.title = title
            }
        }
    }

    protected fun getActionbarTitle(): CharSequence? {
        return (activity as? AppCompatActivity)?.supportActionBar?.title
    }

    fun setNavigationClickFinishActivity() {
        binding.fragmentBaseToolbar.setNavigationOnClickListener { activity?.finish() }
    }

    fun showSnackBar(throwable: Throwable, duration: Int = Snackbar.LENGTH_LONG) {
        view?.showSnackBar(throwable, duration)
    }

    fun setBackEnabled(enable: Boolean) {
        (activity as? HeadsetActivity)?.setBackEnabled(enable)
    }

    fun setBackFinishActivity() {
        runCatching {
            requireActivity().onBackPressedDispatcher.addCallback(
                viewLifecycleOwner,
                object : OnBackPressedCallback(true) {
                    override fun handleOnBackPressed() {
                        requireActivity().finish()
                    }
                }
            )
        }.onFailure {
            Timber.w("finish activity error: ${it.message}")
        }
    }
}
