package com.suunto.headset.viewmodel

import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.suunto.headset.contract.HeadsetSettingContract
import com.suunto.headset.model.FindHeadphoneInfo
import com.suunto.headset.model.FindHeadphoneType
import com.suunto.headset.model.HeadphoneConnectionState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

@HiltViewModel
class FindHeadphoneViewModel @Inject constructor(
    private val dispatchers: CoroutinesDispatchers
) : CoroutineViewModel(dispatchers) {

    private val _headphoneConnectionState = MutableStateFlow(
        HeadphoneConnectionState(
            left = HeadsetSettingContract.ConnectState.Disconnected,
            right = HeadsetSettingContract.ConnectState.Disconnected
        )
    )
    val headphoneConnectionState = _headphoneConnectionState.asStateFlow()

    private val _findHeadphoneInfos = MutableStateFlow(
        persistentListOf(
            FindHeadphoneInfo(type = FindHeadphoneType.ONLY_LEFT, playedSound = false),
            FindHeadphoneInfo(type = FindHeadphoneType.ONLY_RIGHT, playedSound = false),
            FindHeadphoneInfo(type = FindHeadphoneType.BOTH, playedSound = false)
        )
    )
    val findHeadphoneInfos = _findHeadphoneInfos.asStateFlow()

    fun handleFindHeadphone(headphoneInfo: FindHeadphoneInfo) {
        // TODO implement the logic to handle finding the headphone
    }
}
