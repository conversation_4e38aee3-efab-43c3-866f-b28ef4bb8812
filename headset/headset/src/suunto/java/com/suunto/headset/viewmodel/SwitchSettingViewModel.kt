package com.suunto.headset.viewmodel

import androidx.lifecycle.SavedStateHandle
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.suunto.headset.R
import com.suunto.headset.model.SwitchSettingInfo
import com.suunto.headset.model.SwitchSettingType
import com.suunto.headset.ui.ows.SwitchSettingFragment.Companion.KEY_SWITCH_SETTING_TYPE
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class SwitchSettingViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val dispatchers: CoroutinesDispatchers,
) : CoroutineViewModel(dispatchers) {
    private val _switchSettingInfo = MutableStateFlow(getSwitchSettingInfo())
    val switchSettingInfo = _switchSettingInfo.asStateFlow()

    private fun getSwitchSettingInfo(): SwitchSettingInfo {
        val typeName = savedStateHandle.get<String>(KEY_SWITCH_SETTING_TYPE)
        val switchSettingType = SwitchSettingType.entries.find { it.name == typeName }
            ?: SwitchSettingType.NOISE_REDUCTION
        return when (switchSettingType) {
            SwitchSettingType.NOISE_REDUCTION -> {
                SwitchSettingInfo(
                    topBarTitleRes = R.string.noise_reduction,
                    nameRes = R.string.noise_reduction,
                    switchState = false,
                    switchSettingType = switchSettingType,
                    onSwitchChange = ::handleSwitchChange,
                    descriptionRes = R.string.noise_reduction_description
                )
            }

            SwitchSettingType.SPATIAL_AUDIO -> {
                SwitchSettingInfo(
                    topBarTitleRes = R.string.spatial_audio,
                    nameRes = R.string.spatial_audio,
                    switchState = false,
                    switchSettingType = switchSettingType,
                    onSwitchChange = ::handleSwitchChange,
                )
            }
        }
    }

    /**
     * TODO: Implement the logic to handle switch change.
     */
    private fun handleSwitchChange(isChecked: Boolean) {
        _switchSettingInfo.update {
            it.copy(switchState = isChecked)
        }
    }
}
