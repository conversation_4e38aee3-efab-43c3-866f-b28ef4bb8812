package com.suunto.headset.model

import android.os.Parcelable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.suunto.soa.ble.control.attr.ColorType
import com.suunto.soa.ble.control.attr.EqMode
import com.suunto.soa.ble.control.attr.SportsMode
import kotlinx.parcelize.Parcelize

/**
 * headset config cache
 */
@Parcelize
@JsonClass(generateAdapter = true)
data class HeadsetConfig(
    @Json(name = "mac") var mac: String = "",
    @<PERSON>son(name = "productName") var productName: String = "",
    @<PERSON><PERSON>(name = "productId") var productId: Int? = null,
    @Json(name = "deviceName") var deviceName: String = "",
    @Json(name = "serialNumber") var serialNumber: String = "",
    @<PERSON>son(name = "formattedVersion") var formattedVersion: String = "",
    @<PERSON><PERSON>(name = "powerPercent") var powerPercent: Int? = null,
    @Json(name = "soundMode") var soundMode: EqMode = EqMode.NORMAL,
    @Json(name = "bodySenseEnabled") var bodySenseEnabled: Boolean = false,
    @Json(name = "lowLatency") var lowLatency: Boolean = false,
    @Json(name = "dualDeviceConnectionEnabled") var dualDeviceConnectionEnabled: Boolean = false,
    @Json(name = "sportsMode") var sportsMode: SportsMode = SportsMode.CLOSE,
    @Json(name = "algorithmVersion") var algorithmVersion: String = "",
    @Json(name = "appLanguage") var appLanguage: String = "",
    @Json(name = "headsetSupports") var deviceCapability: DeviceCapability = DeviceCapability(),
    @Json(name = "colorType") var colorType: ColorType = ColorType.DEFAULT,
    @Json(name = "multipleBattery") var multipleBattery: MultipleBattery? = null,
) : Parcelable

@Parcelize
@JsonClass(generateAdapter = true)
data class DeviceCapability(
    @Json(name = "poolSwimSportSupported") var poolSwimSportSupported: Boolean = false,
    @Json(name = "poolSwimDistanceSupported") var poolSwimDistanceSupported: Boolean = false,
    @Json(name = "jumpRopeSportSupported") var jumpRopeSportSupported: Boolean = false,
    @Json(name = "runningSportSupported") var runningSportSupported: Boolean = false,
    @Json(name = "sportButtonCustomizedSupported") var sportButtonCustomizedSupported: Boolean = false,
    @Json(name = "userInfoSettingSupported") var userInfoSettingSupported: Boolean = false,
) : Parcelable

@Parcelize
@JsonClass(generateAdapter = true)
data class MultipleBattery(
    @Json(name = "firstValue")
    val firstValue: Int? = null,
    @Json(name = "secondValue")
    val secondValue: Int? = null,
    @Json(name = "thirdValue")
    val thirdValue: Int? = null,
) : Parcelable
