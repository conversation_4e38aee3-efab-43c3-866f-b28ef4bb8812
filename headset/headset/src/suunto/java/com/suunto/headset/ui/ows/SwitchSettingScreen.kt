package com.suunto.headset.ui.ows

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.component.SuuntoSwitch
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.spacing
import com.suunto.headset.R
import com.suunto.headset.model.SwitchSettingInfo
import com.suunto.headset.model.SwitchSettingType
import com.suunto.headset.ui.components.CommonTopAppBar

@Composable
fun SwitchSettingScreen(
    switchSettingInfo: SwitchSettingInfo,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Scaffold(
        modifier = modifier,
        topBar = {
            CommonTopAppBar(
                title = stringResource(switchSettingInfo.topBarTitleRes),
                onBackClick = onBackClick
            )
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(it)
                .background(MaterialTheme.colorScheme.surface)
        ) {
            SwitchSettingContent(switchSettingInfo)
        }
    }
}

@Composable
private fun SwitchSettingContent(
    switchSettingInfo: SwitchSettingInfo,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = stringResource(id = switchSettingInfo.nameRes),
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface
                )
                switchSettingInfo.descriptionRes?.let {
                    Text(
                        modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
                        text = stringResource(id = switchSettingInfo.descriptionRes),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.darkGrey
                    )
                }
            }
            SuuntoSwitch(
                modifier = Modifier.padding(start = MaterialTheme.spacing.medium),
                checked = switchSettingInfo.switchState,
                onCheckedChange = switchSettingInfo.onSwitchChange
            )
        }
        HorizontalDivider(color = MaterialTheme.colorScheme.dividerColor)
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewSwitchSettingScreen() {
    val dummyInfo = SwitchSettingInfo(
        topBarTitleRes = R.string.noise_reduction_top_bar_title,
        nameRes = R.string.noise_reduction,
        descriptionRes = R.string.noise_reduction_description,
        switchState = true,
        onSwitchChange = {},
        switchSettingType = SwitchSettingType.NOISE_REDUCTION
    )
    M3AppTheme {
        SwitchSettingScreen(
            switchSettingInfo = dummyInfo,
            onBackClick = {}
        )
    }
}
