package com.suunto.soa.ble.client

import com.suunto.soa.ble.control.attr.ColorType
import com.suunto.soa.ble.control.attr.DeviceAttr
import com.suunto.soa.ble.response.DeviceInfoResponse
import com.suunto.soa.ble.response.UpdateFileInfo
import com.suunto.soa.ble.response.attr.AttrMultiBattery

interface DeviceInfoFeatures {

    suspend fun getDeviceInfo(vararg attrs: DeviceAttr): DeviceInfoResponse

    suspend fun getBattery(): Int

    suspend fun getSerial(): String

    suspend fun getUpdateFileInfo(): UpdateFileInfo

    suspend fun getAdvertiseName(): String

    suspend fun getProductName(): String

    suspend fun getAlgorithmVersion(): String

    suspend fun supportSyncDeviceCapability(): Boolean

    suspend fun getColorType(): ColorType

    suspend fun getMultiBattery(): AttrMultiBattery
}
