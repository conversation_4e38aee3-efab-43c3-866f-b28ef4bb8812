package com.stt.android.workout.details.analysis

import android.content.SharedPreferences
import androidx.annotation.StringRes
import com.google.android.gms.maps.model.LatLng
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutAnalysisScreenSource
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutDetailsContext.WORKOUT_DETAILS_SCREEN
import com.stt.android.analytics.AnalyticsPropertyValue.WorkoutDetailsContext.WORKOUT_MULTISPORT_DETAILS_SCREEN
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.SummaryExtensionDataModel
import com.stt.android.core.domain.GraphType
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.isMultisport
import com.stt.android.extensions.getIndexOfHighlightedRoute
import com.stt.android.extensions.loadExtension
import com.stt.android.extensions.multisportRoutes
import com.stt.android.mapping.getActivitySummaryForActivityId
import com.stt.android.ui.fragments.workout.analysis.WorkoutAnalysisHelper.Companion.createGraphList
import com.stt.android.utils.traceSuspend
import com.stt.android.workout.details.NavigationEventDispatcher
import com.stt.android.workout.details.WorkoutAnalysisData
import com.stt.android.workout.details.WorkoutAnalysisPagerData
import com.stt.android.workout.details.WorkoutDetailsFullscreenChartNavEvent
import com.stt.android.workout.details.WorkoutDetailsMapGraphAnalysisNavEvent
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.charts.AnalysisGraphXValueType
import com.stt.android.workout.details.charts.GenerateAnalysisGraphDataUseCase
import com.stt.android.workout.details.charts.WorkoutLineChartData
import com.stt.android.workout.details.charts.defaultAnalysisGraphXValueType
import com.stt.android.workout.details.extensions.DiveExtensionDataLoader
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workout.details.workoutdata.WorkoutDataLoader
import com.stt.android.workout.details.workoutheader.WorkoutHeaderLoader
import com.stt.android.workouts.details.values.isSuuntoRun
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers.Default
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import com.stt.android.R as BaseR

interface WorkoutAnalysisDataLoader {
    /**
     * @param xValueType - Pass null to use default for each SummaryGraph type
     */
    suspend fun loadWorkoutAnalysisData(
        workoutHeader: WorkoutHeader,
        xValueType: AnalysisGraphXValueType? = null,
        scope: CoroutineScope
    ): Flow<ViewState<WorkoutAnalysisData?>>
}

@ExperimentalCoroutinesApi
@FlowPreview
@ActivityRetainedScoped
class DefaultWorkoutAnalysisDataLoader
@Inject constructor(
    private val workoutDataLoader: WorkoutDataLoader,
    private val smlDataLoader: SmlDataLoader,
    private val navigationEventDispatcher: NavigationEventDispatcher,
    private val diveExtensionDataLoader: DiveExtensionDataLoader,
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val multisportPartActivityLoader: MultisportPartActivityLoader,
    private val workoutHeaderLoader: WorkoutHeaderLoader,
    private val generateAnalysisGraphDataUseCase: GenerateAnalysisGraphDataUseCase,
    private val summaryExtensionDataModel: SummaryExtensionDataModel,
) : WorkoutAnalysisDataLoader {
    private val defaultWorkoutAnalysisStateFlow =
        MutableStateFlow<ViewState<WorkoutAnalysisData>>(loading())
    private val durationWorkoutAnalysisStateFlow =
        MutableStateFlow<ViewState<WorkoutAnalysisData>>(loading())
    private val distanceWorkoutAnalysisStateFlow =
        MutableStateFlow<ViewState<WorkoutAnalysisData>>(loading())

    private lateinit var workoutHeader: WorkoutHeader

    override suspend fun loadWorkoutAnalysisData(
        workoutHeader: WorkoutHeader,
        xValueType: AnalysisGraphXValueType?,
        scope: CoroutineScope
    ): Flow<ViewState<WorkoutAnalysisData?>> {
        this.workoutHeader = workoutHeader
        scope.launch {
            combine(
                workoutHeaderLoader.workoutHeaderFlow,
                workoutDataLoader.workoutDataStateFlow,
                smlDataLoader.smlStateFlow,
                diveExtensionDataLoader.diveExtensionStateFlow
            ) { workoutHeaderState, workoutDataState, smlDataState, diveDataState ->
                LoaderStates(
                    workoutHeaderState = workoutHeaderState,
                    workoutDataState = workoutDataState,
                    smlDataState = smlDataState,
                    diveDataState = diveDataState
                )
            }
                .conflate()
                .onEach { states ->
                    traceSuspend("loadWorkoutAnalysisData") {
                        states.workoutHeaderState.data?.apply {
                            <EMAIL> = this
                        }

                        val workoutAnalysisData = createData(
                            states.workoutHeaderState.data ?: workoutHeader,
                            states = states,
                            xValueType = xValueType
                        )

                        val splitData = workoutAnalysisData?.splitByXValueModes(xValueType == null)
                        val combinedDefaults =
                            splitData?.first?.combinePagerDataWith(defaultWorkoutAnalysisStateFlow.value.data)
                        val combinedDurations =
                            splitData?.second?.combinePagerDataWith(durationWorkoutAnalysisStateFlow.value.data)
                        val combinedDistances =
                            splitData?.third?.combinePagerDataWith(distanceWorkoutAnalysisStateFlow.value.data)

                        if (states.workoutDataState.isLoading() ||
                            states.smlDataState.isLoading()
                        ) {
                            defaultWorkoutAnalysisStateFlow.value = loading(combinedDefaults)
                            durationWorkoutAnalysisStateFlow.value = loading(combinedDurations)
                            distanceWorkoutAnalysisStateFlow.value = loading(combinedDistances)
                        } else {
                            defaultWorkoutAnalysisStateFlow.value = loaded(combinedDefaults)
                            durationWorkoutAnalysisStateFlow.value = loaded(combinedDurations)
                            distanceWorkoutAnalysisStateFlow.value = loaded(combinedDistances)
                        }
                    }
                }
                .collect()
        }

        return when (xValueType) {
            null -> defaultWorkoutAnalysisStateFlow
            AnalysisGraphXValueType.DURATION -> durationWorkoutAnalysisStateFlow
            AnalysisGraphXValueType.DISTANCE -> distanceWorkoutAnalysisStateFlow
        }
    }

    private suspend fun createData(
        workoutHeader: WorkoutHeader,
        states: LoaderStates,
        xValueType: AnalysisGraphXValueType?
    ): WorkoutAnalysisData? = withContext(Default) {
        runSuspendCatching {
            val sml = states.smlDataState.data
            val workoutData = states.workoutDataState.data
            val graphs = createGraphList(
                workoutHeader.activityType,
                workoutData?.routePoints,
                sml
            )
            val multisportAnalysisData =
                createMultisportAnalysisData(
                    workoutHeader,
                    states,
                    sml,
                    workoutData,
                    xValueType
                )
            createWorkoutAnalysisData(
                graphs = graphs,
                states = states,
                sml = sml,
                workoutHeader = workoutHeader,
                workoutData = workoutData,
                xValueType = xValueType,
                multisportPartAnalysisData = multisportAnalysisData,
            )
        }.onFailure {
            Timber.w(it, "Failed to create workout analysis data")
        }.getOrNull()
    }

    private suspend fun createMultisportAnalysisData(
        workoutHeader: WorkoutHeader,
        states: LoaderStates,
        sml: Sml?,
        workoutData: WorkoutData?,
        xValueType: AnalysisGraphXValueType?
    ) = if (states.smlDataState.isLoaded() && workoutHeader.isMultisport) {
        val multisportRoutes = sml?.multisportRoutes
        sml?.streamData?.multisportPartActivities?.associate { multisportPartActivity ->
            val multisportGraphs = createGraphList(
                ActivityType.valueOf(multisportPartActivity.activityType),
                workoutData?.routePoints,
                sml,
                multisportPartActivity
            )
            multisportPartActivity to createWorkoutAnalysisData(
                graphs = multisportGraphs,
                states = states,
                sml = sml,
                workoutHeader = workoutHeader,
                workoutData = workoutData,
                xValueType = xValueType,
                multisportPartActivity = multisportPartActivity,
                multisportRoutes = multisportRoutes
            )
        } ?: emptyMap()
    } else {
        emptyMap()
    }

    private suspend fun createWorkoutAnalysisData(
        graphs: List<GraphType>,
        states: LoaderStates,
        sml: Sml?,
        workoutHeader: WorkoutHeader,
        workoutData: WorkoutData?,
        xValueType: AnalysisGraphXValueType?,
        multisportPartActivity: MultisportPartActivity? = null,
        multisportPartAnalysisData: Map<MultisportPartActivity, WorkoutAnalysisData> = emptyMap(),
        multisportRoutes: List<List<LatLng>>? = null
    ): WorkoutAnalysisData {
        val workoutLineChartData = graphs.mapNotNull { graphType ->
            generateAnalysisGraphDataUseCase.generate(
                graphType,
                workoutHeader = workoutHeader,
                sml = sml,
                workoutData = workoutData,
                activityWindow = sml?.getActivityWindow(multisportPartActivity),
                multisportPartActivity = multisportPartActivity,
                diveExtension = states.diveDataState.data,
                isSuuntoRun = summaryExtensionDataModel.loadExtension(workoutHeader).isSuuntoRun(),
                xValueType = xValueType
            )
        }

        val showViewOnMap = if (sml != null && multisportPartActivity != null && multisportRoutes != null) {
            val routeIndex = sml.getIndexOfHighlightedRoute(multisportPartActivity)
            if (routeIndex >= 0) {
                multisportRoutes[routeIndex].isNotEmpty()
            } else {
                false
            }
        } else {
            workoutData?.routePoints?.isNotEmpty() ?: false
        }

        return WorkoutAnalysisData(
            buttonStringRes = getButtonText(),
            pagerData = WorkoutAnalysisPagerData(
                workoutHeader = workoutHeader,
                graphData = workoutLineChartData,
                workoutData = workoutData,
                sml = sml,
                diveExtension = states.diveDataState.data,
                onGraphTapped = ::onGraphTapped,
                multisportPartActivity = multisportPartActivity,
            ),
            onAnalysisTapped = ::onAnalysisTapped,
            showViewOnMap = showViewOnMap,
            multisportPartAnalysisData = multisportPartAnalysisData
        )
    }

    /**
     * Return values in order: Default, Duration, Distance
     */
    private fun WorkoutAnalysisData.splitByXValueModes(loadedDefaults: Boolean): Triple<WorkoutAnalysisData, WorkoutAnalysisData, WorkoutAnalysisData> {
        val fullWorkoutDefaults = mutableListOf<WorkoutLineChartData>()
        val partWorkoutDefaults = mutableMapOf<MultisportPartActivity, WorkoutAnalysisData>()

        val fullWorkoutDurations = mutableListOf<WorkoutLineChartData>()
        val partWorkoutDurations = mutableMapOf<MultisportPartActivity, WorkoutAnalysisData>()

        val fullWorkoutDistances = mutableListOf<WorkoutLineChartData>()
        val partWorkoutDistances = mutableMapOf<MultisportPartActivity, WorkoutAnalysisData>()

        pagerData.graphData.forEach {
            if (loadedDefaults || it.xValueType == it.graphType.defaultAnalysisGraphXValueType) {
                fullWorkoutDefaults.add(it)
            }

            when (it.xValueType) {
                AnalysisGraphXValueType.DURATION -> fullWorkoutDurations.add(it)
                AnalysisGraphXValueType.DISTANCE -> fullWorkoutDistances.add(it)
            }
        }

        multisportPartAnalysisData.forEach { entry ->
            val (partDefaults, partDurations, partDistances) = entry.value.splitByXValueModes(loadedDefaults)
            partWorkoutDefaults[entry.key] = partDefaults
            partWorkoutDurations[entry.key] = partDurations
            partWorkoutDistances[entry.key] = partDistances
        }

        val defaults = copy(
            pagerData = pagerData.copy(graphData = fullWorkoutDefaults),
            multisportPartAnalysisData = partWorkoutDefaults
        )
        val durations = copy(
            pagerData = pagerData.copy(graphData = fullWorkoutDurations),
            multisportPartAnalysisData = partWorkoutDurations
        )
        val distances = copy(
            pagerData = pagerData.copy(graphData = fullWorkoutDistances),
            multisportPartAnalysisData = partWorkoutDistances
        )

        return Triple(defaults, durations, distances)
    }

    private fun WorkoutAnalysisData?.combinePagerDataWith(other: WorkoutAnalysisData?): WorkoutAnalysisData? {
        if (this == null) return other
        if (other == null) return this

        val activityType = pagerData.multisportPartActivity?.activityType
            ?: pagerData.workoutHeader.activityTypeId

        val summaryGraphList = getActivitySummaryForActivityId(activityType).graphs
        // fixme temporary new logic for combining the chart caches, but this should be clearer
        val combinedCharts = pagerData.graphData + other.pagerData.graphData.filter { old ->
            pagerData.graphData.none { new -> old.graphType == new.graphType }
        }.sortedBy {
            when (it.graphType) {
                is GraphType.Summary -> summaryGraphList.indexOf(it.graphType.summaryGraph)
                is GraphType.SuuntoPlus -> Int.MAX_VALUE
            }
        }

        val combinedParts =
            (this.multisportPartAnalysisData.keys + other.multisportPartAnalysisData.keys).associateWith { key ->
                val myPartData = this.multisportPartAnalysisData[key]
                val otherPartData = other.multisportPartAnalysisData[key]
                myPartData.combinePagerDataWith(otherPartData)!!
            }

        return this.copy(
            pagerData = pagerData.copy(graphData = combinedCharts),
            multisportPartAnalysisData = combinedParts
        )
    }

    private fun onAnalysisTapped(graphType: GraphType) {
        val multisportPartActivity = multisportPartActivityLoader.multisportPartActivityFlow.value.data
        navigationEventDispatcher.dispatchEvent(
            WorkoutDetailsMapGraphAnalysisNavEvent(
                workoutHeader,
                multisportPartActivity,
                workoutDetailsAnalytics,
                false,
                graphType,
                WorkoutAnalysisScreenSource.VIEW_ON_MAP_INSIGHTS_GRAPH,
                if (multisportPartActivity == null) WORKOUT_DETAILS_SCREEN else WORKOUT_MULTISPORT_DETAILS_SCREEN
            )
        )
    }

    private fun onGraphTapped(
        graphType: GraphType,
        multisportPartActivity: MultisportPartActivity?
    ) {
        navigationEventDispatcher.dispatchEvent(
            WorkoutDetailsFullscreenChartNavEvent(
                workoutHeader,
                graphType,
                WorkoutAnalysisScreenSource.INSIGHTS_GRAPH,
                multisportPartActivity,
            )
        )
    }

    @StringRes
    private fun getButtonText(): Int = BaseR.string.workout_analysis_view_on_map

    private data class LoaderStates constructor(
        val workoutHeaderState: ViewState<WorkoutHeader?>,
        val workoutDataState: ViewState<WorkoutData?>,
        val smlDataState: ViewState<Sml?>,
        val diveDataState: ViewState<DiveExtension?>
    )
}
