package com.stt.android.workout.details.graphanalysis.map

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asFlow
import androidx.lifecycle.asLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.mapbox.geojson.Point
import com.mapbox.turf.TurfConstants
import com.mapbox.turf.TurfMeasurement
import com.soy.algorithms.camerapath.entities.CameraPath
import com.soy.algorithms.camerapath.entities.LonLatAltTimestamp
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.colorfultrack.HeartRateWorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.PaceWorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.PowerWorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.WorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.WorkoutColorfulTrackMapData
import com.stt.android.colorfultrack.WorkoutGeoPointsWithColor
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.core.domain.GraphType
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.Forced2dPlaybackMode
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.camerapath.CreateCameraPathUseCase
import com.stt.android.extensions.combineLatest
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.maps.MAP_TYPE_SATELLITE
import com.stt.android.maps.MAP_TYPE_SKI
import com.stt.android.maps.MapFloatingActionButtonsState
import com.stt.android.maps.MapType
import com.stt.android.maps.MapTypeHelper
import com.stt.android.maps.SuuntoCameraPosition
import com.stt.android.maps.mapbox.delegate.MapboxMapDelegate
import com.stt.android.maps.mapbox.domain.TerrainExaggerationUseCase
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.map.Map3dEnabledLiveData
import com.stt.android.ui.map.SelectedMapTypeLiveData
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.STTConstants.ExtraKeys.NAVIGATED_FROM_SOURCE
import com.stt.android.utils.STTConstants.WorkoutAnalysis.MIN_PLAYBACK_DISTANCE_METERS
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.analytics.navigatedFromValueToPlaybackInitiatedFrom
import com.stt.android.workout.details.graphanalysis.laps.LapMarker
import com.stt.android.workout.details.graphanalysis.laps.LapMarkerModel
import com.stt.android.workout.details.graphanalysis.map.WorkoutMapRouteDataGenerator.generateWorkoutMapRouteDataForFullWorkout
import com.stt.android.workout.details.graphanalysis.map.WorkoutMapRouteDataGenerator.generateWorkoutRouteMapDataForTimeWindow
import com.stt.android.workout.details.graphanalysis.playback.PlaybackLapWindow
import com.stt.android.workout.details.graphanalysis.playback.PlaybackProgressionReason
import com.stt.android.workout.details.graphanalysis.playback.PlaybackStateModel
import com.stt.android.workout.details.graphanalysis.playback.PlaybackType
import com.stt.android.workout.details.graphanalysis.playback.Workout2DPlaybackCameraConfig
import com.stt.android.workout.details.graphanalysis.playback.Workout3DPlaybackCameraConfig
import com.stt.android.workout.details.graphanalysis.playback.WorkoutPlaybackCameraConfig
import com.stt.android.workout.details.graphanalysis.playback.WorkoutPlaybackGeopointLoader
import com.stt.android.workout.details.graphanalysis.playback.WorkoutPlaybackPauseReason
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workout.details.workoutdata.WorkoutDataLoader
import com.stt.android.workout.details.workoutheader.WorkoutHeaderLoader
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.drop
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.roundToLong

@HiltViewModel
class WorkoutMapGraphAnalysisViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val dispatchers: CoroutinesDispatchers,
    private val workoutHeaderLoader: WorkoutHeaderLoader,
    private val multisportPartActivityLoader: MultisportPartActivityLoader,
    private val smlDataLoader: SmlDataLoader,
    private val workoutPlaybackGeopointLoader: WorkoutPlaybackGeopointLoader,
    private val terrainExaggerationUseCase: TerrainExaggerationUseCase,
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val mapSelectionModel: MapSelectionModel,
    private val playbackStateModel: PlaybackStateModel,
    private val selectedMapTypeLiveData: SelectedMapTypeLiveData,
    private val map3dEnabledLiveData: Map3dEnabledLiveData,
    private val createCameraPathUseCase: CreateCameraPathUseCase,
    @Forced2dPlaybackMode private val forced2dPlaybackMode: Boolean,
    private val workoutDataLoader: WorkoutDataLoader,
    private val heartRateWorkoutColorfulTrackLoader: HeartRateWorkoutColorfulTrackLoader,
    private val paceWorkoutColorfulTrackLoader: PaceWorkoutColorfulTrackLoader,
    private val powerWorkoutColorfulTrackLoader: PowerWorkoutColorfulTrackLoader,
    private val lapMarkerModel: LapMarkerModel,
    isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
) : ViewModel() {
    val workoutMapRouteData: LiveData<WorkoutMapRouteData?>
        get() = _workoutMapRouteData
    private val _workoutMapRouteData = MutableLiveData<WorkoutMapRouteData?>()

    val workoutColorfulTrackMapData: LiveData<WorkoutColorfulTrackMapData?>
        get() = _workoutColorfulTrackMapData
    private val _workoutColorfulTrackMapData = MutableLiveData<WorkoutColorfulTrackMapData?>()

    private val _mapFloatingActionButtonState = MutableStateFlow(
        MapFloatingActionButtonsState(
            showLocation = false,
            show3D = mapSelectionModel.show3dOption
        )
    )
    val mapFloatingActionButtonState = _mapFloatingActionButtonState.asStateFlow()

    /**
     * First is if playback is enabled
     * Second is if map interactions should be disabled during playback
     */
    val playbackEnabled: LiveData<Pair<Boolean, Boolean>>
        get() = playbackStateModel.playbackStateFlow.map {
            it.resumed to distanceBasedPlayback
        }.asLiveData()

    val supportsPlayback: LiveData<Boolean>
        get() = _supportsPlayback
    private val _supportsPlayback = MutableLiveData(true)

    val cameraConfig: LiveData<WorkoutPlaybackCameraConfig>
        get() = _cameraConfig
    private val _cameraConfig = MutableLiveData<WorkoutPlaybackCameraConfig>()

    val loadingPlaybackData: LiveData<Boolean>
        get() = combineLatest(loadingCameraPath, chartReadyForPlayback)
            .map { it.first || !it.second }
    private val loadingCameraPath = MutableLiveData(false)
    private val chartReadyForPlayback = MutableLiveData(false)

    val closeBottomSheetEvent: LiveData<Any>
        get() = _closeBottomSheetEvent
    private val _closeBottomSheetEvent = SingleLiveEvent<Any>()

    private val isAutoPlaybackEnabled: Boolean
        get() = savedStateHandle[ARG_AUTO_PLAYBACK] ?: false

    val isPlaybackResumed: Boolean
        get() = playbackStateModel.playbackState.resumed

    val showLoadingErrorMessage: LiveData<Boolean>
        get() = _showLoadingErrorMessage
    private val _showLoadingErrorMessage = MutableLiveData(false)

    val lapMarkerListLiveData: LiveData<List<LapMarker>>
        get() = lapMarkerModel.lapMarkerListLiveData

    val hasPremium: StateFlow<Boolean> = isSubscribedToPremiumUseCase().stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = false,
    )

    private val mapSettingsHolder = MapSettingsHolder(
        mapSelectionModel.selectedMapType,
        mapSelectionModel.map3dEnabled
    )

    val userSelectedMapType: MapType
        get() = mapSettingsHolder.mapType

    val userSelectedMap3dEnabled: Boolean
        get() = mapSettingsHolder.map3dEnabled

    private var geoPoints = listOf<WorkoutGeoPoint>()
    private var highlightedGeoPoint: WorkoutGeoPoint? = null
    private var cameraPath: CameraPath? = null
    private var distanceBasedPlayback = false
    private var bottomSheetDown = true
    private val mapGraphTimeInWorkoutAndAnimationInterpolator =
        MapGraphTimeInWorkoutAndAnimationInterpolator()

    private var bottomSheetAutomatedOpeningContext: String? = null
    private var loadJob: Job? = null

    var aspectRatio = 0.5

    private val _workoutMapAnalysisDataShareFlow = MutableStateFlow<WorkoutMapAnalysisData?>(null)
    val workoutMapAnalysisDataShareFlow = _workoutMapAnalysisDataShareFlow.asStateFlow()

    private var currentMainGraphType: GraphType = GraphType.NONE

    init {
        playbackStateModel.setReadyForAnimation(MAP_READY_FOR_ANIMATION_KEY, false)
        playbackStateModel.setReadyForAnimation(CHART_READY_FOR_ANIMATION_KEY, false)
        playbackStateModel.setReadyForAnimation(CAMERA_PATH_READY_FOR_ANIMATION_KEY, false)
        if (distanceBasedPlayback) {
            playbackStateModel.setTimeInWorkoutAndAnimationInterpolator(
                mapGraphTimeInWorkoutAndAnimationInterpolator
            )
        }

        loadData()

        viewModelScope.launch {
            launch {
                playbackStateModel.playbackStateFlow
                    .drop(1) // Skip initial state
                    .collect {
                        if (it.resumed) {
                            if (distanceBasedPlayback &&
                                !forced2dPlaybackMode &&
                                !mapSelectionModel.map3dEnabled
                            ) {
                                playbackStateModel.setReadyForAnimation(
                                    MAP_3D_TILT_READY_FOR_ANIMATION_KEY,
                                    false
                                )
                                mapSettingsHolder.onDeveloperMap3dEnabledChange()
                                mapSelectionModel.map3dEnabled = true
                                delay(MapboxMapDelegate.TILT_ANIMATION_DURATION_MS)
                                playbackStateModel.setReadyForAnimation(
                                    MAP_3D_TILT_READY_FOR_ANIMATION_KEY,
                                    true
                                )
                            }
                        } else {
                            // Let everything else run with the playback type that was actually used
                            // before switching to time based. Default dispatcher is immediate
                            // so specify the one that goes to back of queue
                            launch(dispatchers.main) {
                                changePlaybackType(false)
                            }
                        }
                    }
            }

            launch {
                selectedMapTypeLiveData.asFlow()
                    .drop(1)
                    .collect {
                        onMapTypeChanged(it)
                    }
            }

            launch(dispatchers.computation) {
                combine(
                    workoutPlaybackGeopointLoader.observeGeoPointForPlayback(),
                    workoutHeaderLoader.workoutHeaderFlow.filter { it.isLoaded() },
                    smlDataLoader.smlStateFlow.filter { it.isLoaded() },
                    multisportPartActivityLoader.multisportPartActivityFlow.filter { it.isLoaded() }
                ) { geoPoints, headerState, smlState, multisportPartState ->
                    val header = headerState.data ?: return@combine null
                    val sml = smlState.data
                    val multisportPart = multisportPartState.data
                    val workoutMapRouteData = generateWorkoutMapRouteDataForFullWorkout(
                        geoPoints,
                        header,
                        multisportPart,
                        sml
                    )
                    WorkoutMapAnalysisData(header, sml, geoPoints, workoutMapRouteData, null)
                }
                    .combine(playbackStateModel.playbackTimeWindowFlow) { workoutMapAnalysisData, timeWindow ->
                       workoutMapAnalysisData?.copy(timeWindow = timeWindow)
                    }
                    .collect { workoutMapAnalysisData ->
                        if (workoutMapAnalysisData?.geoPoints == null || workoutMapAnalysisData.fullWorkoutMapRouteData == null) return@collect
                        _workoutMapAnalysisDataShareFlow.value = workoutMapAnalysisData
                    }
            }

            launch {
                combine(map3dEnabledLiveData.asFlow(), playbackEnabled.asFlow()) { enabled, playback ->
                    Pair(enabled, playback.first)
                }
                    .catch { Timber.w(it, "Observing 3D state failed.") }
                    .collectLatest { (enabled, playbackEnabled) ->
                        set3dOptionState(enabled, playbackEnabled)
                    }
            }

            launch(dispatchers.io) {
                lapMarkerModel.transform { millis ->
                    val index = getGeoPointIndexByWorkoutTime(millis)
                    geoPoints.getOrNull(index)?.latLng
                }
            }
        }

        if (isAutoPlaybackEnabled) {
            startPlayback(
                navigatedFromValueToPlaybackInitiatedFrom(savedStateHandle.get(NAVIGATED_FROM_SOURCE))
            )

            if (!forced2dPlaybackMode && !mapSettingsHolder.forceSkiMap) {
                mapSettingsHolder.onDeveloperMapTypeChange()
                mapSelectionModel.selectedMapType = MapTypeHelper.getOrDefault(MAP_TYPE_SATELLITE)
            }
        }
    }

    fun updateWorkoutMapAnalysisData() {
        val previousValue = _workoutColorfulTrackMapData.value
        previousValue?.apply {
            _workoutColorfulTrackMapData.value = copy(updateLapTrack = false)
        }
    }

    fun loadColorfulTrackRouteData(
        graphType: GraphType,
        workoutMapAnalysisData: WorkoutMapAnalysisData
    ) {
        if (getColorfulTrackLoader(graphType) == null || workoutMapAnalysisData.geoPoints.isEmpty()) {
            currentMainGraphType = graphType
            setWorkoutMapRouteData(workoutMapAnalysisData)
            return
        }

        fun getSplitPointsIndex(
            segment: WorkoutGeoPointsWithColor,
            splitTimestamp: Long
        ): Int {
            val points = segment.points
            return points.indexOfFirst {
                it.timestamp >= splitTimestamp
            }.let {
                if (it == -1) points.lastIndex else it
            }
        }

        fun getMiddleLapSegments(
            timeWindow: PlaybackLapWindow,
            segments: List<WorkoutGeoPointsWithColor>
        ): List<WorkoutGeoPointsWithColor> {
            return segments.filter {
                it.startTime >= timeWindow.windowStartMillis && it.endTime <= timeWindow.windowEndMillis
            }
        }

        fun adjustedSegment(
            segment: WorkoutGeoPointsWithColor,
            windowStart: Long,
            windowEnd: Long,
            adjustStart: Boolean = true
        ): WorkoutGeoPointsWithColor {
            val startIndex = if (adjustStart) getSplitPointsIndex(segment, windowStart) else 0
            val endIndex = (getSplitPointsIndex(segment, windowEnd) + 1).coerceAtMost(segment.points.size)
            return segment.copy(points = segment.points.subList(startIndex, endIndex))
        }

        fun segmentIncludes(pointInTime: Long, segment: WorkoutGeoPointsWithColor): Boolean {
            return pointInTime > segment.startTime && pointInTime < segment.endTime
        }

        fun getSelectedColorTrackSegments(
            timeWindow: PlaybackLapWindow,
            colorfulTrackSegments: List<WorkoutGeoPointsWithColor>
        ): List<WorkoutGeoPointsWithColor> {
            return mutableListOf<WorkoutGeoPointsWithColor>().apply {
                val middleLapSegments = getMiddleLapSegments(timeWindow, colorfulTrackSegments)
                addAll(middleLapSegments)
                val firstLapSegment =
                    colorfulTrackSegments.find { segmentIncludes(timeWindow.windowStartMillis, it) }
                firstLapSegment?.let {
                    add(
                        adjustedSegment(
                            it,
                            timeWindow.windowStartMillis,
                            timeWindow.windowEndMillis
                        )
                    )
                }

                colorfulTrackSegments.findLast { segmentIncludes(timeWindow.windowEndMillis, it) }
                    ?.let { endLapSegment ->
                        // This check prevents duplicate addition when window is within a single segment.
                        if (endLapSegment != firstLapSegment) {
                            add(
                                adjustedSegment(
                                    endLapSegment,
                                    timeWindow.windowStartMillis,
                                    timeWindow.windowEndMillis,
                                    adjustStart = false
                                )
                            )
                        }
                    }
            }
        }

        val timeWindow = workoutMapAnalysisData.timeWindow
        fun handleColorTrackData(data: WorkoutColorfulTrackMapData) {
            if (data.activityRoutesWithColor.isEmpty()) {
                setWorkoutMapRouteData(workoutMapAnalysisData)
            } else {
                // clear other route data
                _workoutMapRouteData.postValue(null)
                _workoutColorfulTrackMapData.postValue(
                    if (timeWindow != null && timeWindow is PlaybackLapWindow) {
                        val lapTracks = getSelectedColorTrackSegments(
                            timeWindow,
                            data.activityRoutesWithColor
                        )
                        data.copy(
                            lapTracks = lapTracks,
                            bounds = lapTracks.flatMap { it.points }.map { it.latLng }
                                .toBoundsOrNull()
                                ?: workoutMapAnalysisData.geoPoints.map { it.latLng }.toBounds(),
                        )
                    } else {
                        data
                    }
                )
            }
        }
        if (currentMainGraphType != graphType) {
            viewModelScope.launch(dispatchers.io) {
                val colorfulTrackLoader = getColorfulTrackLoader(graphType)
                colorfulTrackLoader?.apply {
                    if (this is HeartRateWorkoutColorfulTrackLoader) {
                        workoutDataLoader.workoutDataStateFlow.filter { it.isLoaded() }
                            .collect { workoutDataState ->
                                handleColorTrackData(
                                    loadColorfulTrack(
                                        workoutMapAnalysisData.geoPoints,
                                        workoutMapAnalysisData.header,
                                        workoutMapAnalysisData.sml,
                                        workoutDataState.data?.heartRateEvents
                                    )
                                )
                            }
                    } else {
                        handleColorTrackData(
                            loadColorfulTrack(
                                workoutMapAnalysisData.geoPoints,
                                workoutMapAnalysisData.header,
                                workoutMapAnalysisData.sml,
                            )
                        )
                    }
                }
            }
            currentMainGraphType = graphType
        } else {
            // select lap
            val previousValue = _workoutColorfulTrackMapData.value
            if (previousValue != null) {
                previousValue.apply {
                    if (timeWindow != null && timeWindow is PlaybackLapWindow) {
                        viewModelScope.launch(dispatchers.io) {
                            val lapTracks = getSelectedColorTrackSegments(
                                timeWindow,
                                activityRoutesWithColor
                            )
                            _workoutColorfulTrackMapData.postValue(
                                copy(
                                    lapTracks = lapTracks,
                                    updateLapTrack = true,
                                    bounds = lapTracks.flatMap { it.points }.map { it.latLng }
                                        .toBoundsOrNull()
                                        ?: workoutMapAnalysisData.geoPoints.map { it.latLng }
                                            .toBounds(),
                                )
                            )
                        }
                    } else {
                        _workoutColorfulTrackMapData.postValue(
                            copy(
                                lapTracks = emptyList(),
                                updateLapTrack = true,
                                bounds = buildList {
                                    addAll(nonActivityRoutes.flatten())
                                    addAll(activityRoutes.flatten())
                                }.toBoundsOrNull()
                                    ?: workoutMapAnalysisData.geoPoints.map { it.latLng }
                                        .toBounds(),
                            )
                        )
                    }
                }
            } else {
                setWorkoutMapRouteData(workoutMapAnalysisData)
            }
        }
    }

    private fun setWorkoutMapRouteData(workoutMapAnalysisData: WorkoutMapAnalysisData) {
        if (workoutMapAnalysisData.fullWorkoutMapRouteData == null || workoutMapAnalysisData.timeWindow == null) return
        // clear colorful track data, when select other main graph type
        _workoutColorfulTrackMapData.postValue(null)
        _workoutMapRouteData.postValue(
            generateWorkoutRouteMapDataForTimeWindow(
                smlDataLoader.smlStateFlow.value.data?.streamData,
                workoutMapAnalysisData.geoPoints,
                workoutMapAnalysisData.fullWorkoutMapRouteData,
                workoutMapAnalysisData.timeWindow
            )
        )
    }

    private fun getColorfulTrackLoader(
        graphType: GraphType,
    ): WorkoutColorfulTrackLoader? {
        return when (graphType) {
            GraphType.Summary(SummaryGraph.HEARTRATE),GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS) -> heartRateWorkoutColorfulTrackLoader
            GraphType.Summary(SummaryGraph.PACE) -> paceWorkoutColorfulTrackLoader
            GraphType.Summary(SummaryGraph.POWER) -> powerWorkoutColorfulTrackLoader
            else -> null
        }
    }

    fun forceSkiMap() {
        MapTypeHelper.find(MAP_TYPE_SKI)?.let { skiMap ->
            mapSettingsHolder.onDeveloperMapTypeChange(forceSkiMap = true)
            mapSelectionModel.selectedMapType = skiMap
        }
    }

    private fun loadData() {
        loadJob?.cancel()
        loadJob = viewModelScope.launch {
            val firstHeaderFlow = workoutHeaderLoader.workoutHeaderFlow
                .mapNotNull { it.data }
                .take(1)

            workoutPlaybackGeopointLoader
                .observeGeoPointForPlayback()
                .combine(firstHeaderFlow) { lhs, rhs -> lhs to rhs }
                .distinctUntilChanged()
                .flowOn(dispatchers.computation)
                .collect {
                    onGeoPointsLoaded(it.first, it.second)
                }
        }
    }

    fun onTogglePlaybackClicked() {
        if (playbackStateModel.isRunning) {
            pausePlayback(WorkoutPlaybackPauseReason.PauseButton)
        } else {
            startPlayback(AnalyticsPropertyValue.WorkoutPlaybackInitiatedFrom.WORKOUT_ANALYSIS_SCREEN)
        }
    }

    fun onMapReadyForPlayback() {
        showHighlightedGeoPointOnMap(mapSelectionModel.map3dEnabled)
        playbackStateModel.setReadyForAnimation(MAP_READY_FOR_ANIMATION_KEY, true)
    }

    fun onChartReadyForPlayback() {
        chartReadyForPlayback.postValue(true)
        playbackStateModel.setReadyForAnimation(CHART_READY_FOR_ANIMATION_KEY, true)
    }

    fun onMap3dModeChanged(map3dEnabled: Boolean) {
        mapSettingsHolder.onMap3dEnabledChanged(map3dEnabled)

        if (playbackStateModel.isRunning) {
            if (!playbackStateModel.isWaitingForReadyState) {
                workoutDetailsAnalytics.trackMap3dModeChangedDuringPlayback(map3dEnabled)
            }
        } else {
            showHighlightedGeoPointOnMap(map3dEnabled)
        }
    }

    private fun showHighlightedGeoPointOnMap(map3dEnabled: Boolean) {
        highlightedGeoPoint?.let { gp ->
            getCameraConfigForGeoPoint(map3dEnabled, gp, false)?.let {
                _cameraConfig.postValue(it)
            }
        }
    }

    fun pausePlayback(reason: WorkoutPlaybackPauseReason) {
        playbackStateModel.pausePlayback(reason)
        savedStateHandle.set(ARG_AUTO_PLAYBACK, false)
    }

    private fun startPlayback(initiatedFrom: String) = viewModelScope.launch {
        if (!bottomSheetDown) {
            playbackStateModel.setReadyForAnimation(BOTTOM_SHEET_READY_FOR_ANIMATION_KEY, false)
        }
        _closeBottomSheetEvent.call()
        changePlaybackType(true)
        playbackStateModel.resumePlayback(initiatedFrom)
        savedStateHandle.set(ARG_AUTO_PLAYBACK, true)
    }

    private fun onGeoPointsLoaded(
        geoPoints: List<WorkoutGeoPoint>,
        workoutHeader: WorkoutHeader
    ) {
        this.geoPoints = geoPoints
        val supportsPlayback = !workoutHeader.activityType.isDiving &&
            geoPoints.size > 1 && geoPoints.last().totalDistance >= MIN_PLAYBACK_DISTANCE_METERS
        _supportsPlayback.postValue(supportsPlayback)

        // If the header suggests that playback should be possible but we didn't get geoPoints,
        // let the user know that there's something wrong
        _showLoadingErrorMessage.value = !supportsPlayback &&
            geoPoints.isEmpty() &&
            !workoutHeader.activityType.isDiving &&
            workoutHeader.polyline != null &&
            workoutHeader.startPosition != workoutHeader.stopPosition &&
            workoutHeader.totalDistance >= MIN_PLAYBACK_DISTANCE_METERS

        if (!supportsPlayback) {
            return
        }

        val startPoint = geoPoints.first()
        val stopPoint = geoPoints.last()

        highlightedGeoPoint = startPoint

        playbackStateModel.setPlaybackDuration(
            getPlaybackDuration(stopPoint.totalDistance)
        )

        val initialMillisInWorkout = savedStateHandle.get<Long>(
            HIGHLIGHTED_MILLISECOND_IN_WORKOUT
        )
        if (initialMillisInWorkout != null) {
            viewModelScope.launch {
                playbackStateModel
                    .playbackProgressFlow
                    .filter { it.workoutDurationMillis != PlaybackStateModel.DURATION_NOT_SET }
                    .take(1)
                    .collect {
                        playbackStateModel.seekToTimeInWorkout(
                            initialMillisInWorkout,
                            PlaybackProgressionReason.STARTING_POINT
                        )
                    }
            }
            savedStateHandle.remove<Long>(HIGHLIGHTED_MILLISECOND_IN_WORKOUT)

            val geoPointIdxAtInitialMillis =
                getGeoPointIndexByWorkoutTime(initialMillisInWorkout)
            if (geoPointIdxAtInitialMillis > 0) {
                highlightedGeoPoint = geoPoints[geoPointIdxAtInitialMillis]
            }
        }

        viewModelScope.launch(dispatchers.main) {
            if (!forced2dPlaybackMode) {
                // Naive center point is enough here
                val center = LatLng(
                    (stopPoint.latitude + startPoint.latitude) / 2.0,
                    (stopPoint.longitude + startPoint.longitude) / 2.0
                )

                loadingCameraPath.postValue(true)
                runSuspendCatching {
                    if (!terrainExaggerationUseCase.initialized) {
                        terrainExaggerationUseCase.initialize()
                    }
                    val exaggeration = terrainExaggerationUseCase.getExaggerationFactor(
                        SuuntoCameraPosition(center, 12f)
                    )

                    cameraPath = createCameraPath(exaggeration)

                    cameraPath?.let {
                        playbackStateModel.setPlaybackDuration(
                            getPlaybackDuration(it.distance)
                        )
                    }
                }.onFailure {
                    Timber.w("Loading camera path failed.")
                }

                loadingCameraPath.postValue(false)
            }

            playbackStateModel.setReadyForAnimation(CAMERA_PATH_READY_FOR_ANIMATION_KEY, true)

            playbackStateModel.playbackProgressFlow
                // don't move camera to initial position if playback isn't running
                .drop(if (playbackStateModel.isRunning) 0 else 1)
                .collect { step ->
                    val phase = step.timeInAnimationFraction

                    if (phase > 1) {
                        mapSelectionModel.selectedMapType = if (mapSettingsHolder.forceSkiMap) {
                            MapTypeHelper.getOrDefault(MAP_TYPE_SKI, mapSettingsHolder.mapType)
                        } else {
                            mapSettingsHolder.mapType
                        }
                    }

                    if (!distanceBasedPlayback) {
                        // Force reinterpolation on next distance based animation start after
                        // something else has manipulated the phase
                        mapGraphTimeInWorkoutAndAnimationInterpolator.previousTimeInAnimationMillis = 0L
                    }

                    val (cameraConfig, geoPointIdx) = if (distanceBasedPlayback) {
                        if (mapSelectionModel.map3dEnabled && cameraPath != null) {
                            val cameraConfig3d = get3DCameraConfig(phase)
                            val newGeoPointIndex = cameraConfig3d?.let {
                                getGeoPointIndexByDistance(it.markerDistance)
                            }
                            cameraConfig3d to newGeoPointIndex
                        } else {
                            val distanceInWorkout = (geoPoints.last().totalDistance * phase)
                            val newGeoPointIndex = getGeoPointIndexByDistance(distanceInWorkout)
                            val cameraConfig2d = geoPoints.getOrNull(newGeoPointIndex)?.let {
                                getCameraConfigForGeoPoint(false, it)
                            }
                            cameraConfig2d to newGeoPointIndex
                        }
                    } else {
                        val newGeoPointIndex =
                            getGeoPointIndexByWorkoutTime(step.timeInWorkoutMillis)
                        val cameraConfig = geoPoints.getOrNull(newGeoPointIndex)?.let {
                            getCameraConfigForGeoPoint(
                                mapSelectionModel.map3dEnabled,
                                geoPoints[newGeoPointIndex],
                                false
                            )
                        }
                        cameraConfig to newGeoPointIndex
                    }

                    if (geoPointIdx != null && geoPointIdx >= 0) {
                        highlightedGeoPoint = geoPoints[geoPointIdx]
                    }

                    cameraConfig?.let {
                        _cameraConfig.postValue(it)
                    }
                }
        }
    }

    private suspend fun createCameraPath(exaggeration: Double): CameraPath? =
        createCameraPathUseCase(
            CreateCameraPathUseCase.Params(
                geoPoints.map {
                    LonLatAltTimestamp(it.longitude, it.latitude, it.altitude, it.timestamp)
                },
                exaggeration
            )
        )

    private fun getCameraConfigForGeoPoint(
        map3dEnabled: Boolean,
        geoPoint: WorkoutGeoPoint,
        updateCamera: Boolean = true
    ): WorkoutPlaybackCameraConfig? {
        return if (map3dEnabled && cameraPath != null && _supportsPlayback.value!!) {
            val phase =
                (geoPoint.totalDistance / (TRACK_PROGRESS_FACTOR * cameraPath!!.distance))
                    .coerceAtMost(1.0)
            var cameraConfig = get3DCameraConfig(phase)
            if (!updateCamera) {
                cameraConfig = cameraConfig?.copy(cameraPosition = null)
            }
            cameraConfig
        } else {
            Workout2DPlaybackCameraConfig(
                markerPosition = geoPoint.latLng,
                cameraPosition = if (updateCamera) geoPoint.latLng else null
            )
        }
    }

    private fun get3DCameraConfig(phase: Double): Workout3DPlaybackCameraConfig? {
        return cameraPath?.getCamera(phase.coerceAtMost(1.0), aspectRatio)?.run {
            val points = geoPoints.map { Point.fromLngLat(it.longitude, it.latitude) }
            val markerPosition = TurfMeasurement.along(
                points,
                markerDistance,
                TurfConstants.UNIT_METERS
            )
            val userLocation = geoPoints[getGeoPointIndexByDistance(markerDistance)]

            Workout3DPlaybackCameraConfig(
                cameraPosition = LatLng(cameraPosition.lat, cameraPosition.lon),
                cameraAltitude = cameraPosition.alt,
                cameraPitch = pitch,
                cameraBearing = bearing,
                markerPosition = LatLng(markerPosition.latitude(), markerPosition.longitude()),
                markerAltitude = userLocation.altitude,
                markerDistance = markerDistance
            )
        }
    }

    private fun getGeoPointIndexByDistance(markerDistance: Double): Int {
        val index = with(geoPoints.indexOfFirst { it.totalDistance > markerDistance }) {
            if (this == -1) geoPoints.lastIndex else this
        }
        return index
    }

    private fun getGeoPointIndexByWorkoutTime(millisInWorkout: Long): Int {
        val index = with(geoPoints.indexOfFirst { it.millisecondsInWorkout >= millisInWorkout }) {
            if (this == -1) geoPoints.lastIndex else this
        }
        return index
    }

    private fun getPlaybackDuration(distance: Double): Long {
        return if (distance < BASE_DISTANCE) {
            MIN_3D_PLAYBACK_DURATION + (BASE_DURATION - MIN_3D_PLAYBACK_DURATION) * distance / BASE_DISTANCE
        } else {
            BASE_DURATION + (MAX_3D_PLAYBACK_DURATION - BASE_DURATION) * (1 - BASE_DISTANCE / distance)
        }.roundToLong()
    }

    override fun onCleared() {
        super.onCleared()
        playbackStateModel.pausePlayback(WorkoutPlaybackPauseReason.ScreenExit)
        playbackStateModel.reset()
        workoutPlaybackGeopointLoader.reset()
        lapMarkerModel.reset()
    }

    fun onBottomSheetAutomatedOpeningStarted(analyticsContext: String) {
        bottomSheetAutomatedOpeningContext = analyticsContext
    }

    fun onBottomSheetOpened() {
        bottomSheetDown = false
        playbackStateModel.pausePlayback(WorkoutPlaybackPauseReason.BottomSheetStateChanged)

        viewModelScope.launch {
            val openingContext = bottomSheetAutomatedOpeningContext
                ?: AnalyticsPropertyValue.WorkoutAnalysisBottomSheetOpenedContext.MANUAL
            bottomSheetAutomatedOpeningContext = null
            workoutDetailsAnalytics.trackAnalysisBottomSheetOpened(openingContext)
        }
    }

    fun onBottomSheetClosed() {
        bottomSheetDown = true
        playbackStateModel.setReadyForAnimation(BOTTOM_SHEET_READY_FOR_ANIMATION_KEY, true)
        if (!distanceBasedPlayback) {
            playbackStateModel.pausePlayback(WorkoutPlaybackPauseReason.BottomSheetStateChanged)
        }
    }

    private fun changePlaybackType(distanceBased: Boolean) {
        distanceBasedPlayback = distanceBased
        playbackStateModel.setTimeInWorkoutAndAnimationInterpolator(
            if (distanceBased) {
                mapGraphTimeInWorkoutAndAnimationInterpolator
            } else {
                PlaybackStateModel.LinearTimeInWorkoutAndAnimationInterpolator()
            }
        )

        updateAnalyticsPlaybackType()
    }

    private fun updateAnalyticsPlaybackType() {
        val analyticsPlaybackType = if (distanceBasedPlayback) {
            if (forced2dPlaybackMode) {
                PlaybackType.MOVING_CAMERA_2D
            } else {
                PlaybackType.MOVING_CAMERA_3D
            }
        } else {
            PlaybackType.STATIC_CAMERA
        }
        workoutDetailsAnalytics.setPlaybackType(analyticsPlaybackType)
    }

    fun onMapTypeChanged(type: MapType) {
        mapSettingsHolder.onMapTypeChanged(type)

        viewModelScope.launch {
            workoutDetailsAnalytics.trackAnalysisMapModeChanged()
        }
    }

    fun onFullscreenModeOpened() {
        viewModelScope.launch {
            workoutDetailsAnalytics.trackAnalysisFullscreenMode()
        }
    }

    /**
     * Its assumed that this gets called only in cases where loading the route data failed,
     * if called after some or all data has been loaded successfully the ViewModel's state
     * can be inconsistent.
     */
    fun retryLoading() {
        _supportsPlayback.postValue(false)
        _showLoadingErrorMessage.postValue(false)

        workoutPlaybackGeopointLoader.reset()
        workoutHeaderLoader.workoutHeaderFlow.value.data?.let { header ->
            workoutPlaybackGeopointLoader.resetUpstreamCaches(header)
        }

        loadData()
    }

    inner class MapGraphTimeInWorkoutAndAnimationInterpolator :
        PlaybackStateModel.TimeInWorkoutAndAnimationInterpolator {
        var previousTimeInAnimationMillis = 0L

        private val is3dAnimating: Boolean
            get() = mapSelectionModel.map3dEnabled &&
                cameraPath != null

        override fun timeInAnimationToTimeInWorkout(
            timeInAnimationMillis: Long,
            animationDurationMillis: Long,
            workoutDurationMillis: Long
        ): Long {
            val phase = timeInAnimationMillis.toDouble() / animationDurationMillis
            if (phase >= 1.0) {
                return workoutDurationMillis
            }

            val geoPointIndex = if (is3dAnimating) {
                val cameraConfig3d = get3DCameraConfig(phase)!!
                // Animation & the camera path expect first geopoint to be at distance 0, add the first
                // geoPoint's distance as an offset to compensate for missing data.
                getGeoPointIndexByDistance(geoPoints.first().totalDistance + cameraConfig3d.markerDistance)
            } else {
                val distanceInWorkout = (geoPoints.last().totalDistance * phase)
                getGeoPointIndexByDistance(distanceInWorkout)
            }

            previousTimeInAnimationMillis = timeInAnimationMillis
            return geoPoints[geoPointIndex].millisecondsInWorkout.toLong()
        }

        override fun timeInWorkoutToTimeInAnimation(
            timeInWorkoutMillis: Long,
            animationDurationMillis: Long,
            workoutDurationMillis: Long
        ): Long {
            if ((geoPoints.lastOrNull()?.totalDistance) ?: 0.0 == 0.0) {
                return 0L
            }

            if (timeInWorkoutMillis >= workoutDurationMillis) {
                return animationDurationMillis
            }

            val distancePhaseFactor = if (is3dAnimating) TRACK_PROGRESS_FACTOR else 1.0
            val curGeoPointIndex = getGeoPointIndexByWorkoutTime(timeInWorkoutMillis)
            val curGeoPoint = geoPoints[curGeoPointIndex]
            val curDistancePhase =
                (curGeoPoint.totalDistance / geoPoints.last().totalDistance) / distancePhaseFactor

            // curGeoPoint is ahead of the animation if we haven't gone past the last geoPoint,
            // if the data allows try to find the animation phase between the geoPoints
            val animationPhase = if (curGeoPoint.millisecondsInWorkout > timeInWorkoutMillis) {
                val prevGeoPoint = geoPoints[(curGeoPointIndex - 1).coerceAtLeast(0)]
                if (curGeoPoint.millisecondsInWorkout != prevGeoPoint.millisecondsInWorkout) {
                    val prevDistancePhase =
                        (prevGeoPoint.totalDistance / geoPoints.last().totalDistance) / distancePhaseFactor
                    val phaseDiff = curDistancePhase - prevDistancePhase
                    val betweenGeoPointsFraction =
                        (timeInWorkoutMillis - prevGeoPoint.millisecondsInWorkout).toDouble() /
                            (curGeoPoint.millisecondsInWorkout - prevGeoPoint.millisecondsInWorkout).toDouble()

                    prevDistancePhase + (betweenGeoPointsFraction * phaseDiff)
                } else {
                    // we're at the start
                    curDistancePhase
                }
            } else {
                // data missing at the end
                curDistancePhase
            }

            val timeInAnimationMillis = (animationPhase * animationDurationMillis).roundToLong()
            previousTimeInAnimationMillis = timeInAnimationMillis
            return timeInAnimationMillis
        }

        override fun timeInAnimationAfterInterpolatorChange(
            timeInWorkoutMillis: Long,
            timeInAnimationMillis: Long,
            animationDurationMillis: Long,
            workoutDurationMillis: Long
        ): Long {
            // Prevent jumping due to interpolation inaccuracies if animation time hasn't changed
            if (timeInAnimationMillis == previousTimeInAnimationMillis) {
                return timeInAnimationMillis
            }

            val geoPointIdx = getGeoPointIndexByWorkoutTime(timeInWorkoutMillis)
            return if (geoPointIdx == geoPoints.lastIndex) {
                0L
            } else {
                timeInWorkoutToTimeInAnimation(
                    timeInWorkoutMillis,
                    animationDurationMillis,
                    workoutDurationMillis
                )
            }
        }
    }

    fun handle3dOptionToggled() {
        mapSelectionModel.map3dEnabled = !mapSelectionModel.map3dEnabled
    }

    private fun set3dOptionState(enable3D: Boolean, playbackEnabled: Boolean) {
        _mapFloatingActionButtonState.update {
            it.copy(enable3D = enable3D, button3DEnabled = !playbackEnabled)
        }
    }

    private fun List<LatLng>.toBoundsOrNull() = if (isNotEmpty()) {
        LatLngBounds.builder().apply { forEach(::include) }.build()
    } else null

    private fun List<LatLng>.toBounds() =
        toBoundsOrNull() ?: LatLngBounds(LatLng(0.0, 0.0), LatLng(0.0, 0.0))

    companion object {
        const val ARG_AUTO_PLAYBACK = "autoPlayback"
        const val ARG_FORCE_SKI_MAP = "forceSkiMap"
        private const val HIGHLIGHTED_MILLISECOND_IN_WORKOUT = "highlightedMilliSecondInWorkout"

        private const val MAP_READY_FOR_ANIMATION_KEY =
            "WorkoutMapGraphAnalysisViewModel_MAP_READY_FOR_ANIMATION_KEY"
        private const val CHART_READY_FOR_ANIMATION_KEY =
            "WorkoutMapGraphAnalysisViewModel_CHART_READY_FOR_ANIMATION_KEY"
        private const val MAP_3D_TILT_READY_FOR_ANIMATION_KEY =
            "WorkoutMapGraphAnalysisViewModel_MAP_3D_TILT_READY_FOR_ANIMATION_KEY"
        private const val CAMERA_PATH_READY_FOR_ANIMATION_KEY =
            "WorkoutMapGraphAnalysisViewModel_CAMERA_PATH_READY_FOR_ANIMATION_KEY"
        private const val BOTTOM_SHEET_READY_FOR_ANIMATION_KEY =
            "WorkoutMapGraphAnalysisViewModel_BOTTOM_SHEET_READY_FOR_ANIMATION_KEY"

        private const val MIN_3D_PLAYBACK_DURATION = 15_000.0
        private const val MAX_3D_PLAYBACK_DURATION = 45_000.0
        private const val BASE_DURATION = 25_000.0
        private const val BASE_DISTANCE = 6_000.0
        private const val TRACK_PROGRESS_FACTOR = 1.1
    }
}
