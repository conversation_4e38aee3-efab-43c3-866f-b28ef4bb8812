package com.stt.android.workout.details.charts

import android.content.Context
import com.github.mikephil.charting.data.Entry
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.SmlEvent
import com.stt.android.domain.sml.SmlExtensionStreamPoint
import com.stt.android.domain.sml.SmlStreamData
import com.stt.android.domain.sml.SmlSummary
import com.stt.android.domain.sml.SmlTimedExtensionStreamPoint
import com.stt.android.domain.sml.TimedValueDataPoint
import com.stt.android.domain.sml.dataPointsWithoutPauses
import com.stt.android.domain.sml.doStreamDataHaveDistance
import com.stt.android.domain.sml.filterStreamPoint
import com.stt.android.domain.sml.reader.SmlFactory
import com.stt.android.domain.sml.toTimedStreamPoints
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.infomodel.SummaryItem
import com.stt.android.logbook.NgDiveHeader
import com.stt.android.logbook.SuuntoLogbookWindow
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.averageOrNull
import com.stt.android.utils.takeIfNotNaN
import com.stt.android.utils.traceSuspend
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.withContext
import sanitisePaceGeoPoints
import sanitisePaceStreamPoints
import sanitiseStokeRateStreamPoints
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import com.stt.android.R as BaseR

@FlowPreview
class GenerateAnalysisGraphDataUseCase @Inject constructor(
    context: Context,
    private val infoModelFormatter: InfoModelFormatter
) {
    private val chartColors = context.resources.getIntArray(BaseR.array.chart_color_sequence)

    /**
     * @param xValueType - Pass null to use default, which is duration for most SummaryGraphs but
     * for altitude distance is used if distance data is available.
     */
    suspend fun generate(
        graphType: GraphType,
        workoutHeader: WorkoutHeader,
        sml: Sml?,
        workoutData: WorkoutData?,
        activityWindow: SuuntoLogbookWindow?,
        diveExtension: DiveExtension?,
        isSuuntoRun: Boolean,
        multisportPartActivity: MultisportPartActivity? = null,
        xValueType: AnalysisGraphXValueType? = null
    ): WorkoutLineChartData? = traceSuspend("GenerateGraphData $graphType") {
        withContext(Dispatchers.IO) {
            val streamData = sml?.streamData?.takeIf {
                sml != SmlFactory.EMPTY
            }

            val geoPoints = workoutData?.routePoints?.takeIf { it.isNotEmpty() }

            if (streamData != null) {
                val activityType = multisportPartActivity?.activityType?.run {
                    ActivityType.valueOf(this)
                } ?: workoutHeader.activityType

                val valueUnitConverter = graphType.valueUnitConverter(activityType)
                val (multiSeriesDataSize, showMultiSeriesChart) = getMultiSeriesChartInfo(graphType, streamData)
                if (showMultiSeriesChart) {
                    // Dive extension required, return if not provided
                    val diveExt = diveExtension ?: return@withContext null
                    generateMultipleSeriesStreamPointChart(
                        multiSeriesDataSize,
                        streamData,
                        valueUnitConverter,
                        graphType,
                        diveExt,
                        sml.summary.diveHeader,
                        xValueType ?: getDefaultXAxisValueType(graphType) {
                            streamData.doStreamDataHaveDistance(streamData.altitude)
                        }
                    )
                } else {
                    generateStreamPointChart(
                        streamData,
                        sml.summary,
                        activityType,
                        valueUnitConverter,
                        graphType,
                        multisportPartActivity,
                        activityWindow,
                        diveExtension,
                        isSuuntoRun,
                        xValueType ?: getDefaultXAxisValueType(graphType) {
                            streamData.doStreamDataHaveDistance(
                                streamData.altitude,
                                multisportPartActivity
                            )
                        }
                    )
                }
            } else if (geoPoints != null) {
                generateFromGeoPoints(
                    graphType,
                    workoutHeader,
                    geoPoints,
                    xValueType ?: getDefaultXAxisValueType(graphType) { true }
                )
            } else {
                null
            }
        }
    }

    private fun getMultiSeriesChartInfo(
        graphType: GraphType,
        streamData: SmlStreamData
    ): Pair<Int, Boolean> {
        return when (graphType) {
            GraphType.Summary(SummaryGraph.TANKPRESSURE) ->
                streamData.tankPressures.size to (streamData.tankPressures.size > 1)

            GraphType.Summary(SummaryGraph.GASCONSUMPTION) ->
                streamData.gasConsumptions.size to (streamData.gasConsumptions.size > 1 &&
                    streamData.gasConsumptions.values.flatten().isNotEmpty())

            else -> 0 to false
        }
    }

    private fun GraphType.valueUnitConverter(activityType: ActivityType) = when (this) {
        is GraphType.Summary -> summaryGraph.valueUnitConverter(activityType)
        is GraphType.SuuntoPlus -> {
            val formatStyle = suuntoPlusChannel.formatStyleForSIM
            { value ->
                if (formatStyle != null) {
                    infoModelFormatter.convertFromSItoRangeUnit(formatStyle, value)
                } else {
                    value
                }
            }
        }
    }

    private fun generateFromGeoPoints(
        graphType: GraphType,
        workoutHeader: WorkoutHeader,
        geoPoints: List<WorkoutGeoPoint>,
        xValueType: AnalysisGraphXValueType
    ): WorkoutLineChartData? {
        val xValueExtractor = xValueExtractorForType(xValueType)
        val points = graphType
            .valueExtractor(
                workoutHeader.activityType,
                geoPoints
            )
            .sortedBy { it.time }

        if (points.isEmpty()) {
            return null
        }

        val valueUnitConverter = graphType.valueUnitConverter(workoutHeader.activityType)
        val entries = points
            .mapNotNull { point ->
                val convertedValue = infoModelFormatter.unit.valueUnitConverter(
                    point.value.toDouble()
                )?.toFloat()
                convertedValue?.let {
                    Entry(xValueExtractor(point), convertedValue)
                }
            }
            .reducePoints()
            .toImmutableList()

        val averageValue = points
            .map { it.value.toDouble() }
            .takeIf { it.isNotEmpty() }
            ?.average()
            ?.let {
                infoModelFormatter.unit.valueUnitConverter(it)?.toFloat()
            }

        return WorkoutLineChartData(
            graphType = graphType,
            xValueType = xValueType,
            data = listOf(WorkoutLineEntry(entries = entries)),
            events = emptyList(),
            minValueStrict = graphType.yMinValue(),
            minAllowedValue = graphType.yMinAllowedValue(entries),
            minRange = graphType.yMinRange(),
            isFilled = graphType.isFilled(),
            isInverted = graphType.isInverted(),
            isFillInverted = graphType.isInverted(),
            formatter = makeChartValueFormatter(graphType, workoutHeader.activityType, xValueType),
            averageLineValue = averageValue,
            infoModelFormatter = infoModelFormatter
        )
    }

    private fun generateStreamPointChart(
        streamData: SmlStreamData,
        smlSummary: SmlSummary,
        activityType: ActivityType,
        valueUnitConverter: MeasurementUnit.(Double) -> Double?,
        graphType: GraphType,
        multisportPartActivity: MultisportPartActivity?,
        activityWindow: SuuntoLogbookWindow?,
        diveExtension: DiveExtension?,
        isSuuntoRun: Boolean,
        xValueType: AnalysisGraphXValueType
    ): WorkoutLineChartData? {
        val xValueExtractor = xValueExtractorForType(xValueType)
        val points = graphType.valueExtractor(
            streamData,
            activityType,
            multisportPartActivity
        ).sortedBy { it.time }

        if (points.isEmpty()) {
            return null
        }

        val entries = points
            .mapNotNull { point ->
                val rawValue = point.value.toDouble()
                val convertedValue = infoModelFormatter.unit.valueUnitConverter(
                    if (graphType is GraphType.Summary && graphType.summaryGraph == SummaryGraph.CADENCE && isSuuntoRun) {
                        rawValue / 2
                    } else {
                        rawValue
                    }
                )?.toFloat()
                convertedValue?.let {
                    Entry(xValueExtractor(point), convertedValue)
                }
            }
            .reducePoints()
            .toImmutableList()

        val averageValue = graphType.avgValueExtractor(
            activityWindow,
            activityType,
            diveExtension = diveExtension,
            isSuuntoRun = isSuuntoRun,
            extractedValuesFromGraphType = points
        )?.let {
            infoModelFormatter.unit.valueUnitConverter(it.toDouble())?.toFloat()
        }

        return WorkoutLineChartData(
            graphType = graphType,
            xValueType = xValueType,
            data = listOf(
                WorkoutLineEntry(
                    entries = entries
                )
            ),
            events = graphType.getEvents(streamData, smlSummary),
            minValueStrict = graphType.yMinValue(),
            minAllowedValue = graphType.yMinAllowedValue(entries),
            minRange = graphType.yMinRange(),
            isFilled = graphType.isFilled(),
            isInverted = graphType.isInverted(),
            isFillInverted = graphType.isInverted(),
            formatter = makeChartValueFormatter(graphType, activityType, xValueType),
            averageLineValue = averageValue,
            infoModelFormatter = infoModelFormatter
        )
    }

    private fun generateMultipleSeriesStreamPointChart(
        nSeries: Int,
        streamData: SmlStreamData,
        valueUnitConverter: MeasurementUnit.(Double) -> Double?,
        graphType: GraphType,
        diveExtension: DiveExtension,
        diveHeader: NgDiveHeader?,
        xValueType: AnalysisGraphXValueType
    ): WorkoutLineChartData {
        val timestampsRelative = (0 until nSeries).asSequence()
            .map { graphType.seriesValueExtractor(streamData)(it) }
            .filter { it.isNotEmpty() }
            .flatten()
            .map { it.time }
            .distinct()
            .sorted()
            .toList()

        val seriesEntries = mutableListOf<WorkoutLineEntry>()
        for (i in 0 until nSeries) {
            val values = graphType.seriesValueExtractor(streamData)(i)
            val valuesByTimestamp = values.associateBy { it.time }
            val name = graphType.seriesNameExtractor(streamData, diveExtension, diveHeader)(i)
            val color = chartColors[i.rem(chartColors.size)]

            val entries = timestampsRelative
                .mapNotNull { timestamp -> valuesByTimestamp[timestamp] }
                .mapNotNull { point ->
                    val convertedValue = infoModelFormatter.unit.valueUnitConverter(
                        point.value.toDouble()
                    )?.toFloat()

                    convertedValue?.let {
                        Entry(
                            TimeUnit.MILLISECONDS.toSeconds(point.time).toFloat(),
                            convertedValue
                        )
                    }
                }
                .reducePoints()

            seriesEntries.add(
                WorkoutLineEntry(
                    entries,
                    name,
                    color
                )
            )
        }
        return WorkoutLineChartData(
            graphType = graphType,
            xValueType = xValueType,
            data = seriesEntries,
            minAllowedValue = graphType.yMinAllowedValue(),
            minRange = graphType.yMinRange(),
            isInverted = graphType.isInverted(),
            isFillInverted = graphType.isInverted(),
            formatter = makeChartValueFormatter(graphType, ActivityType.SCUBADIVING, xValueType),
            enableLegend = true,
            infoModelFormatter = infoModelFormatter
        )
    }

    private val durationExtractor: (TimedValueDataPoint) -> Float = { point ->
        TimeUnit.MILLISECONDS.toSeconds(
            point.time
        ).toFloat()
    }

    private val distanceExtractor: (TimedValueDataPoint) -> Float = { point ->
        point.cumulativeDistance
    }

    private val xAxisDurationFormatter: (Float, Context) -> String = { value, _ ->
        when {
            value == 0.0f -> "0"
            value.isSafeValue -> infoModelFormatter.formatValue(SummaryItem.DURATION, value).value.orEmpty()
            else -> ""
        }
    }

    private val xAxisDistanceFormatter: (Float, Context) -> String = { value, context ->
        if (value.isSafeValue) {
            try {
                val workoutValue = infoModelFormatter.formatValue(
                    SummaryItem.DISTANCE,
                    value.toDouble()
                )
                val unitLabel = workoutValue.getUnitLabel(context)
                "${workoutValue.value} $unitLabel".trim()
            } catch (t: Throwable) {
                Timber.w(t, "Distance formatting failed")
                null
            } ?: ""
        } else {
            ""
        }
    }

    private val Float.isSafeValue: Boolean
        get() = isFinite() && this != Float.MAX_VALUE && this != -Float.MAX_VALUE

    private fun GraphType.isInverted(): Boolean {
        return when (this) {
            GraphType.Summary(SummaryGraph.DEPTH),
            GraphType.Summary(SummaryGraph.PACE) -> true
            is GraphType.SuuntoPlus -> suuntoPlusChannel.inverted == true
            else -> false
        }
    }

    private fun GraphType.isFilled(): Boolean {
        return when (this) {
            GraphType.Summary(SummaryGraph.DEPTH) -> true
            else -> false
        }
    }

    private fun GraphType.getEvents(
        streamData: SmlStreamData,
        summary: SmlSummary
    ): List<SmlEvent> {
        return when (this) {
            GraphType.Summary(SummaryGraph.DEPTH) -> {
                streamData.getDiveEvents(summary.isDiveAfterTissueReset)
            }
            else -> emptyList()
        }
    }

    private fun GraphType.valueExtractor(
        streamData: SmlStreamData,
        activityType: ActivityType,
        multisportPartActivity: MultisportPartActivity?
    ): List<TimedValueDataPoint> {
        return when (this) {
            is GraphType.Summary -> when (summaryGraph) {
                SummaryGraph.SPEED,
                SummaryGraph.SPEEDKNOTS -> streamData.dataPointsWithoutPauses(
                    streamData.speed.filterStreamPoint(multisportPartActivity),
                    startTimestamp = multisportPartActivity?.startTime
                )
                SummaryGraph.ALTITUDE -> {
                    val altitudeData = streamData.altitude.filterStreamPoint(multisportPartActivity)
                    val xValueDistanceCorrection = if (multisportPartActivity != null) {
                        altitudeData.firstOrNull { it.cumulativeDistance != null }?.cumulativeDistance
                    } else {
                        null
                    }
                    streamData.dataPointsWithoutPauses(
                        altitudeData,
                        startDistance = xValueDistanceCorrection,
                        startTimestamp = multisportPartActivity?.startTime
                    )
                }
                SummaryGraph.PACE -> {
                    streamData.dataPointsWithoutPauses(
                        streamData.speed.filterStreamPoint(multisportPartActivity),
                        startTimestamp = multisportPartActivity?.startTime
                    ).sanitisePaceStreamPoints(activityType)
                }
                SummaryGraph.VERTICALSPEED -> {
                    streamData.dataPointsWithoutPauses(
                        streamData.verticalSpeed.filterStreamPoint(multisportPartActivity),
                        startTimestamp = multisportPartActivity?.startTime
                    )
                }
                SummaryGraph.TEMPERATURE -> {
                    if (activityType.isDiving) {
                        streamData.temperature.toTimedStreamPoints()
                    } else {
                        streamData.dataPointsWithoutPauses(
                            streamData.temperature.filterStreamPoint(multisportPartActivity),
                            startTimestamp = multisportPartActivity?.startTime
                        )
                    }
                }
                SummaryGraph.CADENCE -> {
                    streamData.dataPointsWithoutPauses(
                        streamData.cadence.filterStreamPoint(multisportPartActivity),
                        startTimestamp = multisportPartActivity?.startTime
                    )
                }
                SummaryGraph.POWER -> {
                    streamData.dataPointsWithoutPauses(
                        streamData.power.filterStreamPoint(multisportPartActivity),
                        startTimestamp = multisportPartActivity?.startTime
                    )
                }
                SummaryGraph.SWIMSTROKERATE -> {
                    streamData.dataPointsWithoutPauses(
                        streamData.strokeRate.filterStreamPoint(multisportPartActivity),
                        startTimestamp = multisportPartActivity?.startTime
                    ).sanitiseStokeRateStreamPoints(activityType)
                }
                SummaryGraph.SWOLF -> {
                    streamData.dataPointsWithoutPauses(
                        streamData.swolf.filterStreamPoint(multisportPartActivity),
                        startTimestamp = multisportPartActivity?.startTime
                    )
                }

                SummaryGraph.GASCONSUMPTION -> {
                    if (streamData.ventilation.isNotEmpty()) {
                        // Legacy gas consumption data
                        streamData.ventilation.toTimedStreamPoints()
                    } else {
                        // Ng dive gas consumption data
                        streamData.gasConsumptions.entries.first().value.toTimedStreamPoints()
                    }
                }
                SummaryGraph.TANKPRESSURE -> {
                    streamData.tankPressures.entries.first().value.toTimedStreamPoints()
                }
                SummaryGraph.DEPTH -> streamData.depth.toTimedStreamPoints()
                SummaryGraph.GROUNDCONTACTTIME -> streamData.dataPointsWithoutPauses(
                    streamData.groundContactTimes.filterStreamPoint(multisportPartActivity),
                    startTimestamp = multisportPartActivity?.startTime
                )
                SummaryGraph.VERTICALOSCILLATION -> streamData.dataPointsWithoutPauses(
                    streamData.verticalOscillations.filterStreamPoint(multisportPartActivity),
                    startTimestamp = multisportPartActivity?.startTime
                )
                SummaryGraph.DURATION -> streamData.dataPointsWithoutPauses(
                    streamData.duration.filterStreamPoint(multisportPartActivity),
                    startTimestamp = multisportPartActivity?.startTime
                )
                SummaryGraph.FREESTYLEPITCHANGLE -> streamData.dataPointsWithoutPauses(
                    streamData.freestyleHeadAngle.filterStreamPoint(multisportPartActivity),
                    startTimestamp = multisportPartActivity?.startTime
                )
                SummaryGraph.BREASTSTROKEHEADANGLE -> streamData.dataPointsWithoutPauses(
                    streamData.breaststrokeHeadAngle.filterStreamPoint(multisportPartActivity),
                    startTimestamp = multisportPartActivity?.startTime
                )
                SummaryGraph.AVGFREESTYLEBREATHANGLE -> streamData.dataPointsWithoutPauses(
                    streamData.freestyleAvgBreathAngle.filterStreamPoint(multisportPartActivity),
                    startTimestamp = multisportPartActivity?.startTime
                )
                SummaryGraph.AVGBREASTSTROKEBREATHANGLE -> streamData.dataPointsWithoutPauses(
                    streamData.breaststrokeAvgBreathAngle.filterStreamPoint(multisportPartActivity),
                    startTimestamp = multisportPartActivity?.startTime
                )
                SummaryGraph.BREASTSTROKEGLIDETIME -> streamData.dataPointsWithoutPauses(
                    streamData.breaststrokeGlideTime.filterStreamPoint(multisportPartActivity),
                    startTimestamp = multisportPartActivity?.startTime
                )
                SummaryGraph.BREATHINGRATE -> streamData.dataPointsWithoutPauses(
                    streamData.breathingRate.filterStreamPoint(multisportPartActivity),
                    startTimestamp = multisportPartActivity?.startTime
                )
                SummaryGraph.AVGSKIPSRATE -> streamData.dataPointsWithoutPauses(
                    streamData.cadence.filterStreamPoint(multisportPartActivity),
                    startTimestamp = multisportPartActivity?.startTime
                )
                SummaryGraph.AVGSKIPSPERROUND -> streamData.dataPointsWithoutPauses(
                    streamData.skipsPerRound.filterStreamPoint(multisportPartActivity),
                    startTimestamp = multisportPartActivity?.startTime
                )
                else -> throw IllegalStateException("Not implemented: $this")
            }
            is GraphType.SuuntoPlus -> {
                val points = streamData.suuntoPlusSamplePoints[suuntoPlusChannel] ?: listOf()
                streamData.dataPointsWithoutPauses(
                    points.filterStreamPoint(multisportPartActivity),
                    startTimestamp = multisportPartActivity?.startTime
                )
            }
            else -> throw IllegalStateException("Not implemented: $this")
        }
    }

    private fun GraphType.seriesValueExtractor(
        streamData: SmlStreamData,
    ): (Int) -> List<SmlTimedExtensionStreamPoint> {
        val streamEntries = when (this) {
            GraphType.Summary(SummaryGraph.TANKPRESSURE) -> streamData.tankPressures
            GraphType.Summary(SummaryGraph.GASCONSUMPTION) -> streamData.gasConsumptions
            else -> null
        }
        return streamEntries?.let { sortedStreamPointsExtractor(it) }
            ?: throw IllegalStateException("Not implemented: $this")
    }

    private fun sortedStreamPointsExtractor(
        streamEntries: Map<Int, List<SmlExtensionStreamPoint>>
    ): (Int) -> List<SmlTimedExtensionStreamPoint> {
        val sortedEntries = sortEntriesByTimestamp(streamEntries)
        // Get earliest sample timestamp to calculate relative time from start for
        // each sample.
        val startTimestamp = sortedEntries.flatMap { it.value }
            .minByOrNull { it.timestamp }?.timestamp
        return { index -> sortedEntries[index].value.toTimedStreamPoints(startTimestamp) }
    }

    private fun GraphType.seriesNameExtractor(
        streamData: SmlStreamData,
        diveExtension: DiveExtension,
        diveHeader: NgDiveHeader?
    ): (Int) -> String {
        val sortedEntries = when (this) {
            GraphType.Summary(SummaryGraph.TANKPRESSURE) -> { sortEntriesByTimestamp(streamData.tankPressures) }
            GraphType.Summary(SummaryGraph.GASCONSUMPTION) -> { sortEntriesByTimestamp(streamData.gasConsumptions) }
            else -> throw IllegalStateException("Not implemented: $this")
        }
        return { index ->
            val gasIndex = sortedEntries[index].key
            diveHeader?.getGasNameByIndex(gasIndex) ?: diveExtension.getGasNameByIndex(gasIndex)
        }
    }

    private fun sortEntriesByTimestamp(
        streamEntries: Map<Int, List<SmlExtensionStreamPoint>>
    ): List<Map.Entry<Int, List<SmlExtensionStreamPoint>>> {
        return streamEntries.entries
            .toList()
            .sortedBy { it.value.firstOrNull()?.timestamp ?: Long.MAX_VALUE }
    }

    private fun GraphType.avgValueExtractor(
        activityWindow: SuuntoLogbookWindow?,
        activityType: ActivityType,
        diveExtension: DiveExtension?,
        isSuuntoRun: Boolean,
        extractedValuesFromGraphType: List<TimedValueDataPoint>?
    ): Float? {
        return when (this) {
            is GraphType.Summary -> {
                when (summaryGraph) {
                    SummaryGraph.SPEED,
                    SummaryGraph.PACE -> activityWindow?.speed?.firstOrNull()?.avg
                    SummaryGraph.VERTICALSPEED -> activityWindow?.verticalSpeed?.firstOrNull()?.avg
                    SummaryGraph.TEMPERATURE -> {
                        extractedValuesFromGraphType?.takeIf { !activityType.isDiving }?.map { it.value }?.averageOrNull()
                    }
                    SummaryGraph.CADENCE -> {
                        val cadence = activityWindow?.cadence?.firstOrNull()?.avg
                        if (isSuuntoRun) cadence?.div(2) else cadence
                    }
                    SummaryGraph.POWER -> activityWindow?.power?.firstOrNull()?.avg
                    // Hz -> strokes/min
                    SummaryGraph.SWIMSTROKERATE -> activityWindow?.strokeRate?.firstOrNull()?.avg?.times(60)
                    SummaryGraph.SWOLF -> activityWindow?.swolf?.firstOrNull()?.avg

                    SummaryGraph.GASCONSUMPTION -> diveExtension?.gasConsumption
                    SummaryGraph.GROUNDCONTACTTIME -> activityWindow?.groundContactTime?.firstOrNull()?.avg
                    SummaryGraph.VERTICALOSCILLATION -> activityWindow?.verticalOscillation?.firstOrNull()?.avg
                    else -> null
                }
            }
            else -> null
        }
    }

    private fun getDefaultXAxisValueType(
        graphType: GraphType,
        checkIfHasDistance: () -> Boolean
    ): AnalysisGraphXValueType =
        if (graphType.defaultAnalysisGraphXValueType == AnalysisGraphXValueType.DISTANCE && checkIfHasDistance()) {
            AnalysisGraphXValueType.DISTANCE
        } else {
            AnalysisGraphXValueType.DURATION
        }

    private fun xValueFormatterForType(xValueType: AnalysisGraphXValueType): (Float, Context) -> String =
        when (xValueType) {
            AnalysisGraphXValueType.DURATION -> xAxisDurationFormatter
            AnalysisGraphXValueType.DISTANCE -> xAxisDistanceFormatter
        }

    private fun xValueExtractorForType(xValueType: AnalysisGraphXValueType): (TimedValueDataPoint) -> Float =
        when (xValueType) {
            AnalysisGraphXValueType.DURATION -> durationExtractor
            AnalysisGraphXValueType.DISTANCE -> distanceExtractor
        }

    private fun GraphType.yMinRange(): Float? {
        return when (this) {
            GraphType.Summary(SummaryGraph.ALTITUDE) -> 50f
            else -> null
        }
    }

    private fun GraphType.yMinValue(): Float? {
        return when (this) {
            GraphType.Summary(SummaryGraph.DEPTH) -> 0f
            else -> null
        }
    }

    private fun GraphType.yMinAllowedValue(entries : List<Entry>? = null): Float? {
        return when (this) {
            is GraphType.Summary -> {
                when (summaryGraph) {
                    SummaryGraph.HEARTRATE,
                    SummaryGraph.SPEED,
                    SummaryGraph.CADENCE,
                    SummaryGraph.EPOC,
                    SummaryGraph.POWER,
                    SummaryGraph.SEALEVELPRESSURE,
                    SummaryGraph.BIKECADENCE,
                    SummaryGraph.SWIMSTROKERATE,
                    SummaryGraph.SWIMPACE,
                    SummaryGraph.SWOLF,
                    SummaryGraph.SPEEDKNOTS,
                    SummaryGraph.DEPTH,
                    SummaryGraph.GASCONSUMPTION,
                    SummaryGraph.TANKPRESSURE,
                    SummaryGraph.RECOVERYHRINTHREEMINS,
                    SummaryGraph.GROUNDCONTACTTIME,
                    SummaryGraph.VERTICALOSCILLATION,
                    SummaryGraph.AVGSKIPSRATE -> 0f
                    SummaryGraph.PACE -> {
                        val min = entries?.minOfOrNull { it.y }
                        (min?.takeIfNotNaN() ?: 0f) * 0.95f
                    }
                    else -> null
                }
            }
            else -> null
        }
    }

    // Extract values from geopoints
    private fun GraphType.valueExtractor(
        activityType: ActivityType,
        geoPoints: List<WorkoutGeoPoint>
    ): List<TimedValueDataPoint> =
        when (this) {
            is GraphType.Summary -> when (summaryGraph) {
                SummaryGraph.PACE,
                SummaryGraph.SPEED,
                SummaryGraph.SPEEDKNOTS -> summaryGraph.extractSpeedValues(
                    activityType,
                    geoPoints
                )
                SummaryGraph.ALTITUDE -> extractAltitudeValues(geoPoints)
                SummaryGraph.DEPTH -> listOf()
                else -> throw IllegalArgumentException("Cannot handle extracting values for geopoints for graph type $this")
            }
            else -> throw IllegalArgumentException("Cannot handle extracting values for geopoints for graph type $this")
        }

    private fun extractAltitudeValues(
        geoPoints: List<WorkoutGeoPoint>
    ): List<TimedValueDataPoint> {
        return geoPoints
            .dropWhile {
                // sometimes the first points have exactly altitude=0.0 for a bug, we exclude those
                // we exclude also points for which [WorkoutGeoPoint.hasAltitude] is false
                !it.hasAltitude() || it.altitude == 0.0
            }
            .map {
                object : TimedValueDataPoint {
                    override val value: Float = it.altitude.toFloat()
                    override val time: Long = it.millisecondsInWorkout.toLong()
                    override val cumulativeDistance: Float = it.totalDistance.toFloat()
                }
            }
    }

    private fun SummaryGraph.extractSpeedValues(
        activityType: ActivityType,
        geoPoints: List<WorkoutGeoPoint>
    ): List<TimedValueDataPoint> {
        return if (this == SummaryGraph.PACE) {
            geoPoints.sanitisePaceGeoPoints(activityType)
        } else {
            geoPoints
        }.map { geoPoint ->
            object : TimedValueDataPoint {
                override val value: Float = geoPoint.speedMetersPerSecond
                override val time: Long = geoPoint.millisecondsInWorkout.toLong()
                override val cumulativeDistance: Float = geoPoint.totalDistance.toFloat()
            }
        }
    }

    private fun makeChartValueFormatter(
        graphType: GraphType,
        activityType: ActivityType,
        xValueType: AnalysisGraphXValueType
    ): WorkoutChartValueFormatter = WorkoutChartValueFormatterImpl(
        graphType = graphType,
        activityType = activityType,
        xValueFormatter = xValueFormatterForType(xValueType),
        infoModelFormatter = infoModelFormatter
    )
}

val GraphType.defaultAnalysisGraphXValueType: AnalysisGraphXValueType
    get() = if (this == GraphType.Summary(SummaryGraph.ALTITUDE)) AnalysisGraphXValueType.DISTANCE else AnalysisGraphXValueType.DURATION

enum class AnalysisGraphXValueType {
    DURATION,
    DISTANCE,
}

private fun List<Entry>.reducePoints(maxPoints: Int = 3_600): List<Entry> =
    DouglasPeuckerPointReducer().reduce(this, maxPoints)
