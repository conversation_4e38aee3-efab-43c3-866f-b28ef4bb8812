package com.stt.android.suuntoplusstore.analytics

import com.stt.android.suuntoplusstore.Item
import com.stt.android.suuntoplusstore.MultiItemTypeCategory

interface SuuntoPlusStoreAnalytics {
    fun trackHomeScreen()

    fun trackGuidesScreen()
    fun trackPartnersScreen()
    fun trackFeaturesScreen()
    fun trackCategoryScreen(category: MultiItemTypeCategory)

    fun trackGuideDetailScreen(guide: Item.Guide?, source: String)
    fun trackFeatureDetailScreen(feature: Item.Feature)

    fun trackItemAddedToLibrary(item: Item, addToWatch: Boolean)
    fun trackItemRemovedFromLibrary(item: Item)
}
