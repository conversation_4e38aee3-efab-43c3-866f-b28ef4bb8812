package com.stt.android.suuntoplusstore

import android.content.Context
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.navArgument
import com.google.accompanist.navigation.animation.AnimatedNavHost
import com.stt.android.analytics.AnalyticsPropertyValue.SuuntoPlusDetailSourceValue
import com.stt.android.common.viewstate.ViewState
import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusStoreItemType
import com.stt.android.home.settings.connectedservices.ConnectedServicesActivity
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.CATEGORY_ID_KEY
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.CATEGORY_ROUTE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.FEATURES_ROUTE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.FEATURE_DETAIL_ID_KEY
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.FEATURE_DETAIL_ROUTE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.FEATURE_DETAIL_ROUTE_TEMPLATE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.GUIDES_ROUTE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.GUIDE_DETAIL_ID_KEY
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.GUIDE_DETAIL_ROUTE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.GUIDE_DETAIL_ROUTE_TEMPLATE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.HOME_ROUTE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.PARTNERS_ROUTE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.PLAN_DAY_DETAIL_ID_KEY
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.PLAN_DAY_DETAIL_ROUTE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.PLAN_DAY_DETAIL_ROUTE_TEMPLATE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.PLAN_DAY_SELECTED_KEY
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.PLAN_DETAIL_ID_KEY
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.PLAN_DETAIL_ROUTE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.PLAN_DETAIL_ROUTE_TEMPLATE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.PLAN_WEEK_DETAIL_ID_KEY
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.REPORT_SPORTS_APP_NAME_KEY
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.REPORT_SPORTS_APP_ROUTE_TEMPLATE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.REST_DAY_DETAIL_ID_KEY
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.REST_DAY_DETAIL_ROUTE
import com.stt.android.suuntoplusstore.SuuntoPlusStoreDestinations.REST_DAY_ROUTE_TEMPLATE
import com.stt.android.suuntoplusstore.analytics.SuuntoPlusStoreAnalytics
import com.stt.android.suuntoplusstore.category.SuuntoPlusStoreCategoryRoute
import com.stt.android.suuntoplusstore.domain.SuuntoPlusStoreCategory.Companion.WATCHFACE_CATEGORY_ID
import com.stt.android.suuntoplusstore.features.SuuntoPlusStoreFeatureDetailRoute
import com.stt.android.suuntoplusstore.features.SuuntoPlusStoreFeaturesScreen
import com.stt.android.suuntoplusstore.features.SuuntoPlusStoreReportSportsAppRoute
import com.stt.android.suuntoplusstore.guides.SuuntoPlusStoreGuideDetailRoute
import com.stt.android.suuntoplusstore.guides.SuuntoPlusStoreGuidesScreen
import com.stt.android.suuntoplusstore.guides.trainingplan.RestDayDetailRoute
import com.stt.android.suuntoplusstore.guides.trainingplan.TrainingDayDetailRoute
import com.stt.android.suuntoplusstore.guides.trainingplan.TrainingPlanDetailRoute
import com.stt.android.suuntoplusstore.home.ItemType
import com.stt.android.suuntoplusstore.home.SuuntoPlusStoreHomeScreen
import com.stt.android.suuntoplusstore.partners.PartnersScreenViewState.Partner
import com.stt.android.suuntoplusstore.partners.SuuntoPlusStorePartnersScreen
import com.stt.android.suuntoplusstore.ui.composableWitDefaultAnimations
import com.stt.android.ui.utils.WindowInfo
import com.stt.android.utils.CustomTabsUtils
import com.stt.android.watch.SuuntoPlusNavigator

object SuuntoPlusStoreDestinations {
    fun fromItemType(itemType: ItemType): String {
        return when (itemType) {
            ItemType.GUIDES -> GUIDES_ROUTE
            ItemType.PARTNERS -> PARTNERS_ROUTE
            ItemType.FEATURES -> FEATURES_ROUTE
            ItemType.WATCHFACE -> "$CATEGORY_ROUTE/$WATCHFACE_CATEGORY_ID"
        }
    }

    const val HOME_ROUTE = "home"

    const val GUIDES_ROUTE = "guides"
    const val GUIDE_DETAIL_ROUTE = "guideDetail"
    const val GUIDE_DETAIL_ID_KEY = "guideId"
    const val GUIDE_DETAIL_ROUTE_TEMPLATE = "$GUIDE_DETAIL_ROUTE/{$GUIDE_DETAIL_ID_KEY}"

    const val PLAN_DETAIL_ROUTE = "planDetail"
    const val PLAN_DETAIL_ID_KEY = "planId"
    const val PLAN_DAY_DETAIL_ROUTE = "planDayDetail"
    const val PLAN_WEEK_DETAIL_ID_KEY = "planWeekId"
    const val PLAN_DAY_DETAIL_ID_KEY = "planDayId"
    const val PLAN_DAY_SELECTED_KEY = "planDaySelected"
    const val PLAN_DETAIL_ROUTE_TEMPLATE = "$PLAN_DETAIL_ROUTE/{$PLAN_DETAIL_ID_KEY}"
    const val PLAN_DAY_DETAIL_ROUTE_TEMPLATE = "$PLAN_DAY_DETAIL_ROUTE/{$PLAN_DETAIL_ID_KEY}/{$PLAN_WEEK_DETAIL_ID_KEY}/{$PLAN_DAY_DETAIL_ID_KEY}/{$PLAN_DAY_SELECTED_KEY}"
    const val REST_DAY_DETAIL_ROUTE = "restDayDetail"
    const val REST_DAY_DETAIL_ID_KEY = "restDayId"
    const val REST_DAY_SELECTED_KEY = "restDaySelected"
    const val REST_DAY_ROUTE_TEMPLATE = "$REST_DAY_DETAIL_ROUTE/{$REST_DAY_DETAIL_ID_KEY}/{$REST_DAY_SELECTED_KEY}"

    const val PARTNERS_ROUTE = "partners"

    const val FEATURES_ROUTE = "features"
    const val FEATURE_DETAIL_ROUTE = "featureDetail"
    const val FEATURE_DETAIL_ID_KEY = "featureId"
    const val FEATURE_DETAIL_ROUTE_TEMPLATE = "$FEATURE_DETAIL_ROUTE/{$FEATURE_DETAIL_ID_KEY}"

    const val REPORT_SPORTS_APP_NAME_KEY = "featureName"
    const val REPORT_SPORTS_APP_ROUTE_TEMPLATE = "$FEATURE_DETAIL_ROUTE/{$FEATURE_DETAIL_ID_KEY}/report?$REPORT_SPORTS_APP_NAME_KEY={$REPORT_SPORTS_APP_NAME_KEY}"

    const val CATEGORY_ROUTE = "category"
    const val CATEGORY_ID_KEY = "categoryId"
}

data class SuuntoPlusStoreDeepLinkOptions(
    val deepLinkDestination: String?,
    val startDestination: String?,
)

@OptIn(ExperimentalAnimationApi::class)
@Composable
fun SuuntoPlusStoreNavGraph(
    navController: NavHostController,
    deepLinkOptions: SuuntoPlusStoreDeepLinkOptions?,
    finishActivity: () -> Unit,
    navigateToUrl: (String) -> Unit,
    shareLink: (type: SuuntoPlusStoreItemType, id: String) -> Unit,
    suuntoPlusNavigator: SuuntoPlusNavigator,
    windowInfo: WindowInfo,
    analyticsTracker: SuuntoPlusStoreAnalytics,
    modifier: Modifier = Modifier,
    viewModel: SuuntoPlusStoreViewModel = hiltViewModel(),
    noBackStack: Boolean = false,
) {
    val startDestination = deepLinkOptions?.startDestination ?: HOME_ROUTE
    val actions = remember(navController, startDestination) {
        MainActions(navController, startDestination, navigateToUrl, suuntoPlusNavigator)
    }

    val homeScreenViewState by viewModel.homeScreenViewState.collectAsState(
        initial = ViewState.Loading()
    )

    val homeScreenSearchViewState by viewModel.homeScreenSearchViewState.collectAsState(
        initial = ViewState.Loaded(null)
    )

    val guidesScreenViewState by viewModel.guidesScreenViewState.collectAsState(
        initial = ViewState.Loading()
    )
    val partnersScreenViewState by viewModel.partnersScreenViewState.collectAsState()
    val featuresScreenViewState by viewModel.featuresScreenViewState.collectAsState(
        initial = ViewState.Loading()
    )

    val showNoInternetSnackbar by viewModel.showNoInternetSnackbar.collectAsState(
        initial = false
    )

    LaunchedEffect(deepLinkOptions) {
        deepLinkOptions?.deepLinkDestination?.let {
            if (listOf(PLAN_DETAIL_ROUTE, GUIDE_DETAIL_ROUTE).any { route -> deepLinkOptions.deepLinkDestination.contains(route) }) {
                analyticsTracker.trackGuideDetailScreen(null, SuuntoPlusDetailSourceValue.H5)
            }
            navController.navigate(it)
        }
    }

    AnimatedNavHost(
        navController = navController,
        startDestination = startDestination,
    ) {
        composableWitDefaultAnimations(
            HOME_ROUTE,
        ) { backStackEntry ->
            val context = LocalContext.current
            SuuntoPlusStoreHomeScreen(
                viewState = homeScreenViewState,
                showNoInternetSnackbar = showNoInternetSnackbar,
                onItemSelected = { item -> actions.openItemDetails(analyticsTracker, item, backStackEntry) },
                onPartnerSelected = { partner ->
                    actions.openPartner(
                        partner,
                        context,
                        backStackEntry
                    )
                },
                onItemTypeSelected = { itemType ->
                    actions.openItemType(itemType, backStackEntry)
                },
                onCategorySelected = { category ->
                    actions.openCategory(category, backStackEntry)
                    analyticsTracker.trackCategoryScreen(category)
                },
                onRefresh = { viewModel.refreshHome() },
                onUpPressed = { actions.upPress(backStackEntry, finishActivity) },
                onStart = { analyticsTracker.trackHomeScreen() },
                searchViewState = homeScreenSearchViewState,
                searchTerm = viewModel.homeScreenSearchTerm,
                onSearchTermChange = { viewModel.updateHomeScreenSearchTerm(it) },
                windowInfo = windowInfo,
                modifier = modifier,
            )
        }

        composableWitDefaultAnimations(GUIDES_ROUTE) { backStackEntry ->
            SuuntoPlusStoreGuidesScreen(
                screenViewState = guidesScreenViewState,
                showNoInternetSnackbar = showNoInternetSnackbar,
                onItemSelected = { guide ->
                    if (guide.isTrainingPlan) {
                        actions.openTrainingPlan(guide, backStackEntry)
                    } else {
                        actions.openGuideDetails(guide, backStackEntry)
                    }
                    analyticsTracker.trackGuideDetailScreen(guide, SuuntoPlusDetailSourceValue.GUIDES_SCREEN)
                },
                onGuidesRefresh = { viewModel.refreshGuides() },
                onUpPressed = { actions.upPress(backStackEntry, finishActivity) },
                onStart = { analyticsTracker.trackGuidesScreen() },
                windowInfo = windowInfo,
                modifier = modifier,
            )
        }

        composableWitDefaultAnimations(PARTNERS_ROUTE) { backStackEntry ->
            val context = LocalContext.current
            SuuntoPlusStorePartnersScreen(
                screenViewState = partnersScreenViewState,
                showNoInternetSnackbar = false,
                onUpPressed = { actions.upPress(backStackEntry, finishActivity) },
                onPartnerSelected = { partner ->
                    actions.openPartner(
                        partner,
                        context,
                        backStackEntry
                    )
                },
                onPartnersRefresh = { forceFetching ->
                    viewModel.refreshPartnersIfNeeded(
                        forceFetching
                    )
                },
                onStart = { analyticsTracker.trackPartnersScreen() },
                windowInfo = windowInfo,
                modifier = modifier,
            )
        }

        composableWitDefaultAnimations(FEATURES_ROUTE) { backStackEntry ->
            SuuntoPlusStoreFeaturesScreen(
                screenViewState = featuresScreenViewState,
                showNoInternetSnackbar = showNoInternetSnackbar,
                onItemSelected = { feature ->
                    actions.openFeatureDetails(
                        feature,
                        backStackEntry
                    )
                    analyticsTracker.trackFeatureDetailScreen(feature)
                },
                onFeaturesRefresh = { viewModel.refreshFeatures() },
                onUpPressed = { actions.upPress(backStackEntry, finishActivity) },
                onStart = { analyticsTracker.trackFeaturesScreen() },
                windowInfo = windowInfo,
                modifier = modifier,
            )
        }

        composableWitDefaultAnimations(
            FEATURE_DETAIL_ROUTE_TEMPLATE,
            arguments = listOf(
                navArgument(FEATURE_DETAIL_ID_KEY) { type = NavType.StringType },
            ),
        ) { backStackEntry: NavBackStackEntry ->
            val context = LocalContext.current
            SuuntoPlusStoreFeatureDetailRoute(
                onNavigateToLibrary = { dismissDetailScreen, isWatchface ->
                    actions.openMySportsApps(
                        popUpToIncluding = if (dismissDetailScreen) {
                            FEATURE_DETAIL_ROUTE_TEMPLATE
                        } else {
                            null
                        },
                        context = context,
                        from = backStackEntry,
                        isWatchface = isWatchface,
                    )
                },
                onLearnMore = { url -> actions.openLearnMoreItem(url, backStackEntry) },
                onReportSportsApp = { id, name ->
                    actions.openReportSportsAppScreen(
                        featureId = id,
                        featureName = name,
                        from = backStackEntry
                    )
                },
                onShareLink = { shareLink(SuuntoPlusStoreItemType.FEATURE, it) },
                onDismiss = { actions.popBackStack(backStackEntry) },
                onShowTerms = { url ->
                    // Use custom tab instead of standalone browser as for other links
                    CustomTabsUtils.launchCustomTab(context, url)
                },
                modifier = modifier,
            )
        }

        composableWitDefaultAnimations(
            REPORT_SPORTS_APP_ROUTE_TEMPLATE,
            arguments = listOf(
                navArgument(FEATURE_DETAIL_ID_KEY) { type = NavType.StringType },
                navArgument(REPORT_SPORTS_APP_NAME_KEY) { nullable = true }
            ),
        ) { backStackEntry: NavBackStackEntry ->
            val sportsAppId = backStackEntry.arguments?.getString(FEATURE_DETAIL_ID_KEY)
            val sportsAppName = backStackEntry.arguments?.getString(REPORT_SPORTS_APP_NAME_KEY)
            if (sportsAppId != null) {
                SuuntoPlusStoreReportSportsAppRoute(
                    sportsAppId = sportsAppId,
                    sportsAppName = sportsAppName,
                    onDismiss = { actions.popBackStack(backStackEntry) },
                    modifier = modifier,
                )
            }
        }

        composableWitDefaultAnimations(
            REST_DAY_ROUTE_TEMPLATE,
            arguments = listOf(
                navArgument(REST_DAY_DETAIL_ID_KEY) { type = NavType.IntType }
            )
        ) { backStackEntry ->
            val context = LocalContext.current
            RestDayDetailRoute(
                onDismiss = { actions.popBackStack(backStackEntry) },
                onNavigateToLibrary = {
                    actions.openMyGuides(
                        popUpToIncluding = null,
                        context = context,
                        from = backStackEntry
                    )
                },
                onShareLink = { shareLink(SuuntoPlusStoreItemType.GUIDE, it) },
                modifier = modifier,
            )
        }



        composableWitDefaultAnimations(
            PLAN_DAY_DETAIL_ROUTE_TEMPLATE,
            arguments = listOf(
                navArgument(PLAN_WEEK_DETAIL_ID_KEY) { type = NavType.IntType },
                navArgument(PLAN_DAY_DETAIL_ID_KEY) { type = NavType.IntType },
                navArgument(PLAN_DAY_SELECTED_KEY) { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val context = LocalContext.current
            TrainingDayDetailRoute(
                onDismiss = { actions.popBackStack(backStackEntry) },
                onNavigateToLibrary = {
                    actions.openMyGuides(
                        popUpToIncluding = null,
                        context = context,
                        from = backStackEntry
                    )
                },
                onShareLink = { shareLink(SuuntoPlusStoreItemType.GUIDE, it) },
                modifier = modifier,
            )
        }

        composableWitDefaultAnimations(
            PLAN_DETAIL_ROUTE_TEMPLATE,
            arguments = listOf(
                navArgument(PLAN_DETAIL_ID_KEY) { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val context = LocalContext.current
            TrainingPlanDetailRoute(
                onDismiss = {
                    if (noBackStack) finishActivity()
                    else
                        actions.popBackStack(backStackEntry)
                },
                onNavigateToLibrary = {
                    actions.openMyGuides(
                        popUpToIncluding = null,
                        context = context,
                        from = backStackEntry
                    )
                },
                onShareLink = { shareLink(SuuntoPlusStoreItemType.GUIDE, it) },
                modifier = modifier,
                onTrainingDayClicked = { planId, weekId, dayId, selectedTrainingDayJson->
                    actions.openTrainingDay(planId, weekId, dayId, selectedTrainingDayJson, backStackEntry)
                },
                onRestDayClicked = { dayId, selectedRestDayJson ->
                    actions.openRestDay(dayId, selectedRestDayJson, backStackEntry)
                },
                onReportSportsApp = { id, name ->
                    actions.openReportSportsAppScreen(
                        featureId = id,
                        featureName = name,
                        from = backStackEntry
                    )
                },
            )
        }

        composableWitDefaultAnimations(
            GUIDE_DETAIL_ROUTE_TEMPLATE,
            arguments = listOf(
                navArgument(GUIDE_DETAIL_ID_KEY) { type = NavType.StringType }
            )
        ) { backStackEntry: NavBackStackEntry ->
            val context = LocalContext.current
            SuuntoPlusStoreGuideDetailRoute(
                onDismiss = {
                    if (noBackStack) finishActivity()
                    else actions.popBackStack(backStackEntry)
                },
                onNavigateToLibrary = {
                    actions.openMyGuides(
                        popUpToIncluding = null,
                        context = context,
                        from = backStackEntry
                    )
                },
                onLearnMore = { guide -> actions.openLearnMoreItem(guide, backStackEntry) },
                onShareLink = { shareLink(SuuntoPlusStoreItemType.GUIDE, it) },
                onReportSportsApp = { id, name ->
                    actions.openReportSportsAppScreen(
                        featureId = id,
                        featureName = name,
                        from = backStackEntry
                    )
                },
                modifier = modifier,
            )
        }

        composableWitDefaultAnimations(
            "$CATEGORY_ROUTE/{$CATEGORY_ID_KEY}",
            arguments = listOf(
                navArgument(CATEGORY_ID_KEY) { type = NavType.StringType }
            )
        ) { backStackEntry: NavBackStackEntry ->
            val context = LocalContext.current
            SuuntoPlusStoreCategoryRoute(
                onItemSelected = { item -> actions.openItemDetails(analyticsTracker, item, backStackEntry) },
                onPartnerSelected = { partner ->
                    actions.openPartner(
                        partner,
                        context,
                        backStackEntry
                    )
                },
                onUpPressed = { actions.upPress(backStackEntry, finishActivity) },
                windowInfo = windowInfo,
                modifier = modifier,
            )
        }
    }
}

/**
 * Models the navigation actions in the SuuntoPlus Store.
 */
class MainActions(
    private val navController: NavHostController,
    private val startDestination: String,
    private val navigateToUrl: (String) -> Unit,
    private val suuntoPlusNavigator: SuuntoPlusNavigator,
) {
    // Used from HOME_ROUTE
    fun openItemType(itemType: ItemType, from: NavBackStackEntry) {
        if (from.lifecycleIsResumed()) {
            navController.navigate(SuuntoPlusStoreDestinations.fromItemType(itemType))
        }
    }

    // Used from PARTNERS_ROUTE
    fun openPartner(partner: Partner, context: Context, from: NavBackStackEntry) {
        if (from.lifecycleIsResumed()) {
            context.startActivity(
                ConnectedServicesActivity.newIntent(
                    context = context,
                    serviceMetadata = partner.serviceMetadata,
                    source =
                    com.stt.android.analytics.AnalyticsPropertyValue.ConnectedServicesListSource.STORE_PARTNERS_SCREEN,
                )
            )
        }
    }

    fun openFeatureDetails(feature: Item.Feature, from: NavBackStackEntry) {
        if (from.lifecycleIsResumed()) {
            navController.navigate("$FEATURE_DETAIL_ROUTE/${feature.id}")
        }
    }

    fun openReportSportsAppScreen(
        featureId: String,
        featureName: String?,
        from: NavBackStackEntry
    ) {
        if (from.lifecycleIsResumed()) {
            if (featureName != null) {
                navController.navigate("$FEATURE_DETAIL_ROUTE/$featureId/report?$REPORT_SPORTS_APP_NAME_KEY=$featureName")
            } else {
                navController.navigate("$FEATURE_DETAIL_ROUTE/$featureId/report")
            }
        }
    }

    fun openGuideDetails(guide: Item.Guide, from: NavBackStackEntry) {
        if (from.lifecycleIsResumed()) {
            navController.navigate("$GUIDE_DETAIL_ROUTE/${guide.id}")
        }
    }

    fun openTrainingPlan(guide: Item.Guide, from: NavBackStackEntry) {
        if (from.lifecycleIsResumed()) {
            navController.navigate("$PLAN_DETAIL_ROUTE/${guide.id}")
        }
    }

    fun openTrainingDay(planId: String, weekId: Int, dayId:Int, selectedTrainingDayJson: String, from: NavBackStackEntry) {
        if (from.lifecycleIsResumed()) {
            navController.navigate("$PLAN_DAY_DETAIL_ROUTE/${planId}/${weekId}/${dayId}/${selectedTrainingDayJson}")
        }
    }

    fun openRestDay(dayId:Int, selectedRestDayJson: String, from: NavBackStackEntry) {
        if (from.lifecycleIsResumed()) {
            navController.navigate("$REST_DAY_DETAIL_ROUTE/${dayId}/${selectedRestDayJson}")
        }
    }

    fun openItemDetails(analyticsTracker: SuuntoPlusStoreAnalytics, item: Item, backStackEntry: NavBackStackEntry) {
        when (item) {
            is Item.Feature -> openFeatureDetails(item, backStackEntry)
            is Item.Guide -> {
                if (item.isTrainingPlan) {
                    openTrainingPlan(item, backStackEntry)
                } else {
                    openGuideDetails(item, backStackEntry)
                }
                analyticsTracker.trackGuideDetailScreen(item, SuuntoPlusDetailSourceValue.STORE_HOME_SCREEN)
            }
        }
    }

    fun openLearnMoreItem(url: String, from: NavBackStackEntry) {
        if (from.lifecycleIsResumed()) {
            navigateToUrl(url)
        }
    }

    fun openMySportsApps(
        popUpToIncluding: String?,
        context: Context,
        from: NavBackStackEntry?,
        isWatchface: Boolean
    ) {
        if (from?.lifecycleIsResumed() == true) {
            popUpToIncluding?.let { navController.popBackStack(it, inclusive = true) }
            if (isWatchface) {
                suuntoPlusNavigator.newSuuntoPlusWatchfaceIntent(context)
            } else {
                suuntoPlusNavigator.newSuuntoPlusFeaturesIntent(context)
            }.let {
                context.startActivity(it)
            }
        }
    }

    fun openMyGuides(
        popUpToIncluding: String?,
        context: Context,
        from: NavBackStackEntry?
    ) {
        if (from?.lifecycleIsResumed() == true) {
            popUpToIncluding?.let { navController.popBackStack(it, inclusive = true) }
            context.startActivity(suuntoPlusNavigator.newSuuntoPlusGuideIntent(context))
        }
    }

    fun openCategory(category: MultiItemTypeCategory, from: NavBackStackEntry) {
        if (from.lifecycleIsResumed()) {
            navController.navigate("$CATEGORY_ROUTE/${category.id}")
        }
    }

    // Used from all routes
    fun upPress(from: NavBackStackEntry, finishActivity: () -> Unit) {
        if (!from.lifecycleIsResumed()) return

        if (from.destination.route == startDestination) {
            finishActivity()
        } else {
            navController.navigateUp()
        }
    }

    fun popBackStack(from: NavBackStackEntry) {
        if (from.lifecycleIsResumed()) {
            navController.popBackStack()
        }
    }
}

/**
 * If the lifecycle is not resumed it means this NavBackStackEntry already processed a nav event.
 *
 * This is used to de-duplicate navigation events.
 */
private fun NavBackStackEntry.lifecycleIsResumed() =
    lifecycle.currentState == Lifecycle.State.RESUMED
