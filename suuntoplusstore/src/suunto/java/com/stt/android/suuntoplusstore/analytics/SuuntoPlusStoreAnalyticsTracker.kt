package com.stt.android.suuntoplusstore.analytics

import android.content.SharedPreferences
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.SuuntoPlusDetailSourceValue
import com.stt.android.analytics.AnalyticsPropertyValue.SuuntoPlusItemTypeValue
import com.stt.android.analytics.AnalyticsPropertyValue.SuuntoPlusStoreFilteredFilterValue
import com.stt.android.analytics.EmarsysAnalyticsImpl
import com.stt.android.device.suuntoplusguide.putWatchDetailsProperties
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.suuntoplusstore.Item
import com.stt.android.suuntoplusstore.MultiItemTypeCategory
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SuuntoPlusStoreAnalyticsTracker @Inject constructor(
    private val emarsysAnalyticsImpl: EmarsysAnalyticsImpl,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    @SuuntoSharedPrefs private val suuntoSharedPreferences: SharedPreferences,
) : SuuntoPlusStoreAnalytics {
    override fun trackHomeScreen() {
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SUUNTO_PLUS_STORE_HOME_SCREEN)
        emarsysAnalyticsImpl.trackEvent(AnalyticsEvent.SUUNTO_PLUS_STORE_HOME_SCREEN)
    }

    override fun trackGuidesScreen() {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_PLUS_STORE_FILTERED_SCREEN,
            AnalyticsEventProperty.SUUNTO_PLUS_STORE_FILTERED_FILTER,
            SuuntoPlusStoreFilteredFilterValue.GUIDES
        )
    }

    override fun trackPartnersScreen() {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_PLUS_STORE_FILTERED_SCREEN,
            AnalyticsEventProperty.SUUNTO_PLUS_STORE_FILTERED_FILTER,
            SuuntoPlusStoreFilteredFilterValue.PARTNERS
        )
    }

    override fun trackFeaturesScreen() {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_PLUS_STORE_FILTERED_SCREEN,
            AnalyticsEventProperty.SUUNTO_PLUS_STORE_FILTERED_FILTER,
            SuuntoPlusStoreFilteredFilterValue.FEATURES
        )
    }

    override fun trackCategoryScreen(category: MultiItemTypeCategory) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_PLUS_STORE_FILTERED_SCREEN,
            AnalyticsEventProperty.SUUNTO_PLUS_STORE_FILTERED_FILTER,
            when {
                category.trainingPlan.isNotEmpty() -> AnalyticsPropertyValue.CategoryPropertyValue.TRAINING_PLAN
                category.guides.isNotEmpty() -> AnalyticsPropertyValue.CategoryPropertyValue.TRAINING_GUIDE
                else -> category.title
            }
        )
    }

    override fun trackGuideDetailScreen(guide: Item.Guide?, source: String) {
        val properties = AnalyticsProperties()
            .put(AnalyticsEventProperty.SUUNTO_PLUS_ITEM_TYPE, SuuntoPlusItemTypeValue.GUIDE)
            .put(AnalyticsEventProperty.SUUNTO_PLUS_DETAIL_SOURCE, source)

        guide?.let {
            properties
                .put(AnalyticsEventProperty.SUUNTO_PLUS_ITEM_NAME, it.name)
                .put(AnalyticsEventProperty.SUUNTO_PLUS_ITEM_CATEGORIES, it.categoryTitles)
        }

        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_PLUS_DETAIL_SCREEN,
            properties
        )
    }

    override fun trackFeatureDetailScreen(feature: Item.Feature) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_PLUS_DETAIL_SCREEN,
            AnalyticsProperties()
                .put(
                    AnalyticsEventProperty.SUUNTO_PLUS_ITEM_NAME,
                    feature.name
                )
                .put(
                    AnalyticsEventProperty.SUUNTO_PLUS_ITEM_TYPE,
                    SuuntoPlusItemTypeValue.FEATURE
                )
                .put(
                    AnalyticsEventProperty.SUUNTO_PLUS_ITEM_CATEGORIES,
                    feature.categoryTitles
                )
                .put(
                    AnalyticsEventProperty.SUUNTO_PLUS_DETAIL_SOURCE,
                    SuuntoPlusDetailSourceValue.FEATURES_SCREEN
                )
        )
    }

    override fun trackItemAddedToLibrary(item: Item, addToWatch: Boolean) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_PLUS_STORE_ADD_TO_MY_LIBRARY,
            AnalyticsProperties()
                .put(AnalyticsEventProperty.SUUNTO_PLUS_ITEM_NAME, item.name)
                .put(AnalyticsEventProperty.SUUNTO_PLUS_ITEM_ID, item.id)
                .put(AnalyticsEventProperty.SUUNTO_PLUS_ITEM_TYPE, item.toAnalyticsTypeValue())
                .put(AnalyticsEventProperty.SUUNTO_PLUS_ITEM_CATEGORIES, item.categoryTitles)
                .putYesNo(AnalyticsEventProperty.SUUNTO_PLUS_ITEM_ADD_TO_WATCH, addToWatch)
                .putWatchDetailsProperties(suuntoSharedPreferences)
        )
    }

    override fun trackItemRemovedFromLibrary(item: Item) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.SUUNTO_PLUS_STORE_REMOVE_FROM_MY_LIBRARY,
            AnalyticsProperties()
                .put(AnalyticsEventProperty.SUUNTO_PLUS_ITEM_NAME, item.name)
                .put(AnalyticsEventProperty.SUUNTO_PLUS_ITEM_ID, item.id)
                .put(AnalyticsEventProperty.SUUNTO_PLUS_ITEM_TYPE, item.toAnalyticsTypeValue())
                .put(AnalyticsEventProperty.SUUNTO_PLUS_ITEM_CATEGORIES, item.categoryTitles)
                .putWatchDetailsProperties(suuntoSharedPreferences)
        )
    }

    private fun Item.toAnalyticsTypeValue(): String = when (this) {
        is Item.Guide -> SuuntoPlusItemTypeValue.GUIDE
        is Item.Feature -> SuuntoPlusItemTypeValue.FEATURE
    }
}
