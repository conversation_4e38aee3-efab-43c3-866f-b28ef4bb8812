package com.stt.android.suuntoplusstore

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.runtime.Composable
import androidx.core.graphics.drawable.toDrawable
import androidx.core.net.toUri
import androidx.fragment.app.FragmentActivity
import com.google.accompanist.navigation.animation.rememberAnimatedNavController
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.device.domain.ShareSuuntoPlusLinkUseCase
import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusStoreItemType
import com.stt.android.suuntoplusstore.analytics.SuuntoPlusStoreAnalytics
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreFeatureId
import com.stt.android.suuntoplusstore.remote.SuuntoPlusStoreGuideId
import com.stt.android.suuntoplusstore.remote.SuuntoPlusTrainingPlanId
import com.stt.android.ui.activities.SimpleAlertDialog
import com.stt.android.ui.utils.WindowInfo
import com.stt.android.ui.utils.rememberWindowInfo
import com.stt.android.watch.SuuntoPlusNavigator
import com.stt.android.window.setFlagsAndColors
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class SuuntoPlusStoreActivity : FragmentActivity() {
    @Inject
    lateinit var suuntoPlusNavigator: SuuntoPlusNavigator

    @Inject
    lateinit var analyticsTracker: SuuntoPlusStoreAnalytics

    @Inject
    lateinit var shareSuuntoPlusLinkUseCase: dagger.Lazy<ShareSuuntoPlusLinkUseCase>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window?.setFlagsAndColors()
        // Window background is briefly visible during the Activity launch. Force it a light color
        // so that default black windowBackground color from BaseDarkTheme is never used.
        window.setBackgroundDrawable(resources.getColor(R.color.light_grey, theme).toDrawable())

        // Get details from deep link for cases we should open directly to item details screen
        val sportsAppIdExtra = intent.getStringExtra(EXTRA_SPORTS_APP_ID)
        val guideIdExtra = intent.getStringExtra(EXTRA_GUIDE_ID)
        val linkToGuidesPage = intent.hasExtra(EXTRA_LINK_TO_GUIDES_PAGE)
        val linkToPartnersPage = intent.hasExtra(EXTRA_LINK_TO_PARTNERS_PAGE)
        val trainPlanId = intent.getStringExtra(EXTRA_TRAINING_PLAN_ID)
        val noBackStack = intent.getBooleanExtra(EXTRA_NO_BACK_STACK, false)
        intent.removeExtra(EXTRA_SPORTS_APP_ID)
        intent.removeExtra(EXTRA_GUIDE_ID)
        intent.removeExtra(EXTRA_LINK_TO_GUIDES_PAGE)
        intent.removeExtra(EXTRA_LINK_TO_PARTNERS_PAGE)
        intent.removeExtra(EXTRA_TRAINING_PLAN_ID)
        intent.removeExtra(EXTRA_NO_BACK_STACK)

        val deepLinkDestination = when {
            linkToGuidesPage ->
                SuuntoPlusStoreDestinations.GUIDES_ROUTE
            linkToPartnersPage ->
                SuuntoPlusStoreDestinations.PARTNERS_ROUTE
            sportsAppIdExtra != null ->
                "${SuuntoPlusStoreDestinations.FEATURE_DETAIL_ROUTE}/$sportsAppIdExtra"
            guideIdExtra != null ->
                "${SuuntoPlusStoreDestinations.GUIDE_DETAIL_ROUTE}/$guideIdExtra"
            trainPlanId != null ->
                "${SuuntoPlusStoreDestinations.PLAN_DETAIL_ROUTE}/$trainPlanId"
            else -> null
        }

        val startDestination = when {
            intent.hasExtra(EXTRA_START_AT_GUIDES_PAGE) ->
                SuuntoPlusStoreDestinations.GUIDES_ROUTE
            intent.hasExtra(EXTRA_START_AT_PARTNERS_PAGE) ->
                SuuntoPlusStoreDestinations.PARTNERS_ROUTE
            else -> null
        }

        val deepLinkOptions = SuuntoPlusStoreDeepLinkOptions(
            deepLinkDestination = deepLinkDestination,
            startDestination = startDestination
        )


        setContent {
            val windowInfo = rememberWindowInfo()
            SuuntoPlusStore(
                deepLinkOptions = deepLinkOptions,
                finishActivity = ::finish,
                openUrl = ::openUrl,
                shareLink = ::shareLink,
                suuntoPlusNavigator = suuntoPlusNavigator,
                windowInfo = windowInfo,
                analyticsTracker = analyticsTracker,
                noBackStack = noBackStack,
            )
        }
    }

    private fun shareLink(type: SuuntoPlusStoreItemType, id: String) {
        when (type) {
            SuuntoPlusStoreItemType.FEATURE -> shareSuuntoPlusLinkUseCase.get()
                .shareSportsAppLink(this, id)

            SuuntoPlusStoreItemType.GUIDE -> shareSuuntoPlusLinkUseCase.get()
                .shareGuideLink(this, id)

            else -> throw IllegalArgumentException("Cannot share Store link to $type")
        }
    }

    private fun openUrl(url: String) {
        try {
            val uri = url.toUri()
            @Suppress("UnsafeImplicitIntentLaunch")
            startActivity(Intent(Intent.ACTION_VIEW).setData(uri))
        } catch (e: ActivityNotFoundException) {
            SimpleAlertDialog.showOkDialog(
                this,
                "Error",
                getString(R.string.error_0)
            )
        }
    }

    companion object {
        private const val EXTRA_SPORTS_APP_ID = "com.stt.android.suuntoplusstore.EXTRA_SPORTS_APP_ID"
        private const val EXTRA_GUIDE_ID = "com.stt.android.suuntoplusstore.EXTRA_GUIDE_ID"
        private const val EXTRA_LINK_TO_GUIDES_PAGE = "com.stt.android.suuntoplusstore.EXTRA_LINK_TO_GUIDES_PAGE"
        private const val EXTRA_START_AT_GUIDES_PAGE = "com.stt.android.suuntoplusstore.EXTRA_START_AT_GUIDES_PAGE"
        private const val EXTRA_LINK_TO_PARTNERS_PAGE = "com.stt.android.suuntoplusstore.EXTRA_LINK_TO_PARTNERS_PAGE"
        private const val EXTRA_START_AT_PARTNERS_PAGE = "com.stt.android.suuntoplusstore.EXTRA_START_AT_PARTNERS_PAGE"
        private const val EXTRA_TRAINING_PLAN_ID = "com.stt.android.suuntoplusstore.EXTRA_TRAINING_PLAN_ID"
        private const val EXTRA_NO_BACK_STACK = "com.stt.android.suuntoplusstore.EXTRA_NO_BACK_STACK"

        fun newStoreHomeIntent(context: Context) =
            Intent(context, SuuntoPlusStoreActivity::class.java)

        fun newSportsAppDetailsIntent(context: Context, sportsAppId: SuuntoPlusStoreFeatureId) =
            newStoreHomeIntent(context).apply {
                putExtra(EXTRA_SPORTS_APP_ID, sportsAppId.id)
            }

        fun newGuidesPageIntent(
            context: Context,
            noBackStack: Boolean = false
        ) =
            newStoreHomeIntent(context).apply {
                if (noBackStack) {
                    putExtra(EXTRA_START_AT_GUIDES_PAGE, true)
                } else {
                    putExtra(EXTRA_LINK_TO_GUIDES_PAGE, true)
                }
            }

        fun newGuideDetailsIntent(
            context: Context,
            guideId: SuuntoPlusStoreGuideId,
            noBackStack: Boolean = false
        ) =
            newStoreHomeIntent(context).apply {
                putExtra(EXTRA_GUIDE_ID, guideId.id)
                putExtra(EXTRA_NO_BACK_STACK, noBackStack)
            }

        fun newTrainingPlanDetailsIntent(
            context: Context,
            planId: SuuntoPlusTrainingPlanId,
            noBackStack: Boolean = false
        ) =
            newStoreHomeIntent(context).apply {
                putExtra(EXTRA_TRAINING_PLAN_ID, planId.id)
                putExtra(EXTRA_NO_BACK_STACK, noBackStack)
            }

        fun newPartnersPageIntent(
            context: Context,
            noBackStack: Boolean = false
        ) =
            newStoreHomeIntent(context).apply {
                if (noBackStack) {
                    putExtra(EXTRA_START_AT_PARTNERS_PAGE, true)
                } else {
                    putExtra(EXTRA_LINK_TO_PARTNERS_PAGE, true)
                }
            }
    }
}

@OptIn(ExperimentalAnimationApi::class)
@Composable
private fun SuuntoPlusStore(
    deepLinkOptions: SuuntoPlusStoreDeepLinkOptions?,
    finishActivity: () -> Unit,
    openUrl: (String) -> Unit,
    shareLink: (type: SuuntoPlusStoreItemType, id: String) -> Unit,
    suuntoPlusNavigator: SuuntoPlusNavigator,
    windowInfo: WindowInfo,
    analyticsTracker: SuuntoPlusStoreAnalytics,
    noBackStack: Boolean = false,
) {
    AppTheme {
        val navController = rememberAnimatedNavController()

        SuuntoPlusStoreNavGraph(
            navController = navController,
            deepLinkOptions = deepLinkOptions,
            finishActivity = finishActivity,
            navigateToUrl = openUrl,
            shareLink = shareLink,
            suuntoPlusNavigator = suuntoPlusNavigator,
            windowInfo = windowInfo,
            analyticsTracker = analyticsTracker,
            noBackStack = noBackStack,
        )
    }
}
