<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <string name="recording">Grabando</string>
    <string name="paused">Pausado</string>
    <string name="auto_paused">Auto pausado</string>

    <string name="workout_values_headline_duration">Duración</string>
    <string name="workout_values_headline_total_time">Tiempo total</string>
    <string name="workout_values_headline_pause_time">Tiempo en pausa</string>
    <string name="workout_values_headline_moving_time">Tiempo en movimiento</string>
    <string name="workout_values_headline_rest_time">Tiempo de descanso</string>
    <string name="workout_values_headline_ascent_duration">Duración de ascenso</string>
    <string name="workout_values_headline_descent_duration">Duración de descenso</string>
    <string name="workout_values_headline_avg_temperature">Temperatura</string>
    <string name="workout_values_headline_max_temperature">Temperatura máx.</string>
    <string name="workout_values_headline_high_altitude">Punto más alto</string>
    <string name="workout_values_headline_low_altitude">Punto más bajo</string>
    <string name="workout_values_headline_peak_vertical_speed_30s">Velocidad vertical máxima 30 s</string>
    <string name="workout_values_headline_peak_vertical_speed_1m">Velocidad vertical máxima 1 min</string>
    <string name="workout_values_headline_peak_vertical_speed_3m">Velocidad vertical máxima 3 min</string>
    <string name="workout_values_headline_peak_vertical_speed_5m">Velocidad vertical máxima 5 min</string>
    <string name="average">Promedio</string>
    <string name="average_with_value_and_unit">Promedio %1$d %2$s</string>
    <string name="average_with_decimal_value_and_unit">Promedio %.2f %2$s</string>
    <string name="duration_capital">DURACIÓN</string>
    <string name="estimated_duration">Duración estimada</string>
    <string name="workout_values_headline_distance">Distancia</string>
    <string name="workout_values_headline_nautical_distance">Distancia náutica</string>
    <string name="distance_capital">DISTANCIA</string>
    <string name="energy_capital">CALORÍAS</string>
    <string name="energy_normal">Energía</string>
    <string name="workout_values_headline_energy">Calorías</string>
    <string name="workout_values_headline_pte">PTE</string>
    <string name="workout_values_headline_feeling">Sensación</string>
    <string name="workout_values_headline_avg_power">Potencia med.</string>
    <string name="workout_values_headline_avg_power_with_zero">Potencia con ceros</string>
    <string name="workout_values_headline_peak_power_30s">Potencia máxima 30 s</string>
    <string name="workout_values_headline_peak_power_1m">Potencia máxima 1 min</string>
    <string name="workout_values_headline_peak_power_3m">Potencia máxima 3 min</string>
    <string name="workout_values_headline_peak_power_5m">Potencia máxima 5 min</string>
    <string name="workout_values_headline_recoveryTime">Tiempo de recuperación</string>
    <string name="workout_values_headline_peak_epoc">EPOC</string>
    <string name="workout_values_headline_vo2_max">VO₂ máx. est.</string>
    <string name="workout_values_headline_performance">Rendimiento</string>
    <string name="workout_values_headline_swolf">SWOLF</string>
    <string name="workout_values_headline_swim_stroke_rate">Frecuencia de brazada</string>
    <string name="workout_values_headline_shot_count">Recuento de disparos</string>
    <string name="workout_values_headline_catch_count">Recuento de capturas</string>
    <string name="workout_values_headline_aerobic_hr_threshold">Umbral FC aeróbica</string>
    <string name="workout_values_headline_anaerobic_hr_threshold">Umbral FC anaeróbica</string>

    <string name="workout_values_headline_aerobic_power_threshold">Umbral potencia aeróbica</string>
    <string name="workout_values_headline_anaerobic_power_threshold">Umbral potencia anaeróbica</string>
    <string name="workout_values_headline_aerobic_pace_threshold">Umbral ritmo aeróbico</string>
    <string name="workout_values_headline_anaerobic_pace_threshold">Umbral ritmo anaeróbico</string>
    <string name="workout_values_headline_aerobic_duration">Duración aeróbica</string>
    <string name="workout_values_headline_anaerobic_duration">Duración anaeróbica</string>
    <string name="workout_values_headline_vo2_max_duration">Duración VO₂máx</string>
    <string name="workout_values_headline_breathing_rate">Tasa de respiración</string>
    <string name="workout_values_headline_breaststroke_duration">Duración, brazada</string>
    <string name="workout_values_headline_breaststroke_percent">Porcentaje de brazada</string>
    <string name="workout_values_headline_breaststroke_glide_time">Duración deslizamiento, brazada</string>
    <string name="workout_values_headline_breaststroke_max_breath_angle">Ángulo máx. de respiración, brazada</string>
    <string name="workout_values_headline_breaststroke_avg_breath_angle">Ángulo medio de respiración, brazada</string>
    <string name="workout_values_headline_freestyle_duration">Duración, estilo libre</string>
    <string name="workout_values_headline_freestyle_percent">Porcentaje de estilo libre</string>
    <string name="workout_values_headline_freestyle_avg_breath_angle">Ángulo medio de respiración, estilo libre</string>
    <string name="workout_values_headline_freestyle_max_breath_angle">Ángulo máx. de respiración, estilo libre</string>
    <string name="workout_values_headline_freestyle_head_angel">Ángulo de cabeza, estilo libre</string>
    <string name="workout_values_headline_breaststroke_head_angel">Ángulo de cabeza, brazada</string>
    <string name="workout_values_headline_stride_length">Longitud de zancada med.</string>
    <string name="workout_values_headline_step_length">Longitud de pasos med.</string>
    <string name="workout_values_headline_climbs">Subidas</string>
    <string name="workout_values_headline_climbs_category_1">Subidas (categoría 1)</string>
    <string name="workout_values_headline_climbs_category_2">Subidas (categoría 2)</string>
    <string name="workout_values_headline_climbs_category_3">Subidas (categoría 3)</string>
    <string name="workout_values_headline_climbs_category_4">Subidas (categoría 4)</string>
    <string name="workout_values_headline_climbs_category_hc">Subidas (fuera de categoría)</string>
    <string name="workout_values_headline_climb_ascent_category_1">Ascenso (categoría 1)</string>
    <string name="workout_values_headline_climb_ascent_category_2">Ascenso (categoría 2)</string>
    <string name="workout_values_headline_climb_ascent_category_3">Ascenso (categoría 3)</string>
    <string name="workout_values_headline_climb_ascent_category_4">Ascenso (categoría 4)</string>
    <string name="workout_values_headline_climb_ascent_category_hc">Ascenso (fuera de categoría)</string>
    <string name="workout_values_headline_climb_distance_category_1">Distancia (categoría 1)</string>
    <string name="workout_values_headline_climb_distance_category_2">Distancia (categoría 2)</string>
    <string name="workout_values_headline_climb_distance_category_3">Distancia (categoría 3)</string>
    <string name="workout_values_headline_climb_distance_category_4">Distancia (categoría 4)</string>
    <string name="workout_values_headline_climb_distance_category_hc">Distancia (fuera de categoría)</string>
    <string name="workout_values_headline_climb_duration_category_1">Duración (categoría 1)</string>
    <string name="workout_values_headline_climb_duration_category_2">Duración (categoría 2)</string>
    <string name="workout_values_headline_climb_duration_category_3">Duración (categoría 3)</string>
    <string name="workout_values_headline_climb_duration_category_4">Duración (categoría 4)</string>
    <string name="workout_values_headline_climb_duration_category_hc">Distancia (fuera de categoría)</string>
    <string name="workout_values_headline_ascent_speed">Velocidad de ascenso</string>
    <string name="workout_values_headline_descent_speed">Velocidad de descenso</string>
    <string name="workout_values_headline_max_ascent_speed">Velocidad máx. de ascenso</string>
    <string name="workout_values_headline_max_descent_speed">Velocidad máx. de descenso</string>
    <string name="workout_values_headline_avg_distance_per_stroke">Distancia media por brazada</string>
    <string name="workout_values_headline_fatConsumption">Consumo de grasa</string>
    <string name="workout_values_headline_carbohydrateConsumption">Consumo de hidratos de carbono</string>
    <string name="workout_values_headline_avgGroundContactTime">Tiempo de contacto con el suelo med.</string>
    <string name="workout_values_headline_avgVerticalOscillation">Oscilación vertical med.</string>
    <string name="workout_values_headline_avgGroundContactBalance">Tiempo medio de contacto con el suelo</string>
    <string name="workout_values_headline_skips_per_min">Saltos por minuto</string>
    <string name="workout_values_headline_max_skips_per_min">Máx. saltos por minuto</string>
    <string name="workout_values_headline_rounds">Rondas</string>
    <string name="workout_values_headline_avg_skip_per_round">Media de saltos por ronda</string>
    <string name="workout_values_headline_skip_per_round">Saltos por ronda</string>
    <string name="workout_values_headline_max_consecutive_skips">Máx. saltos consecutivos</string>
    <string name="workout_values_headline_avg_rowing_pace">Ritmo</string>
    <string name="speed_capital">VELOCIDAD</string>
    <string name="speed_knots_capital">NUDOS DE VELOCIDAD</string>
    <string name="speed">Velocidad</string>
    <string name="pace_capital">RITMO</string>
    <string name="heart_rate_capital">FC</string>
    <string name="cadence_capital">CADENCIA</string>
    <string name="max_depth_with_value_and_unit">Profundidad máx. %1$s %2$s</string>
    <string name="max_bottom_temperature_with_value_and_unit">Temp. fondo %1$d %2$s</string>
    <plurals name="tank_pressure_delta_with_gas_values">
        <item quantity="one">Presión utilizada %1$s</item>
        <item quantity="many">Presión utilizada: %1$s</item>
        <item quantity="other">Presión utilizada: %1$s</item>
    </plurals>
    <string name="gas_pressure_with_value_unit_and_gas_name">%1$d %2$s (%3$s)</string>

    <string name="all_altitude">Altitud</string>
    <string name="all_cadence">Cadencia</string>
    <string name="all_epoc">EPOC</string>
    <string name="all_temperature">Temperatura</string>
    <string name="all_power">Potencia</string>
    <string name="all_sea_level_pressure">Presión a nivel del mar</string>
    <string name="all_bike_cadence">Cadencia de bicicleta</string>
    <string name="swim_stroke_rate">Frecuencia de brazada</string>
    <string name="all_swim_pace">Ritmo de natación</string>
    <string name="all_swolf">SWOLF</string>
    <string name="all_speed_knots">Nudos de velocidad</string>
    <string name="all_depth">Profundidad</string>
    <string name="all_heart_rate">Frecuencia cardíaca</string>
    <string name="all_vertical_speed">Velocidad vertical</string>
    <string name="all_ascent">Ascenso</string>
    <string name="all_descent">Bajada</string>
    <string name="all_gas_consumption">Consumo de gas</string>
    <string name="all_tank_pressure">Presión de la botella</string>

    <string name="aerobic_hr_thresholds">Umbrales FC aeróbica</string>
    <string name="aerobic_power_thresholds">Umbrales potencia aeróbica</string>
    <string name="hr_list_in_three_mins">FC recuperación</string>
    <string name="all_vertical_oscillation">Oscilación vertical</string>
    <string name="all_ground_contact_time">Tiempo de contacto con el suelo</string>
    <string name="duration_lap">Duración, por vuelta</string>
    <string name="all_none">Ninguna</string>

    <string name="lap_capital">VUELTA</string>
    <string name="lap_distance_capital">DISTANCIA DE VUELTA</string>
    <string name="lap_duration_capital">TIEMPO DE VUELTA</string>

    <string name="bpm_capital">LPM</string>
    <string name="avg_bpm_capital">MEDIA DE LPM</string>
    <string name="rpm_capital">RPM</string>
    <string name="avg_capital">MED.</string>
    <string name="max_capital">MÁX.</string>

    <!--
        Names of activities should be according to the SIM
        https://bitbucket.org/suunto/suunto-information-model/src/master/Specifications/Activities/Activities.json
        Exception: Crossfit is Cross training
    -->
    <string name="running">Correr</string>
    <string name="walking">Caminar</string>
    <string name="cycling">Ciclismo</string>
    <string name="mountain_biking">Ciclismo de montaña</string>
    <string name="hiking">Excursionismo</string>
    <string name="roller_skating">Patinaje sobre ruedas</string>
    <string name="cross_country_skiing">Esquí de fondo</string>
    <string name="downhill_skiing">Esquí alpino</string>
    <string name="paddling">Pádel</string>
    <string name="rowing">Remo</string>
    <string name="golf">Golf</string>
    <string name="swimming">Natación (piscina)</string>
    <string name="open_water_swimming">Natación en aguas abiertas</string>
    <string name="trailrunning">Trail running</string>
    <string name="ballgames">Juegos de pelota</string>
    <string name="gym">Entrenamiento con pesas</string>
    <string name="nordicwalking">Marcha nórdica</string>
    <string name="horsebackriding">Equitación</string>
    <string name="motorsports">Deportes de motor</string>
    <string name="skateboarding">Skateboarding</string>
    <string name="watersports">Deportes acuáticos</string>
    <string name="climbing">Escalada</string>
    <string name="snowboarding">Snowboard</string>
    <string name="skitouring">Esquí de travesía</string>
    <string name="fitnessclass">Clase de fitness</string>
    <string name="soccer">Fútbol</string>
    <string name="indoor">Entrenamiento en interior</string>
    <string name="other1">Otros 1</string>
    <string name="other2">Otros 2</string>
    <string name="other3">Otros 3</string>
    <string name="other4">Otros 4</string>
    <string name="other5">Otros 5</string>
    <string name="other6">Deporte no especificado</string>
    <string name="dancing">Baile</string>
    <string name="snow_shoeing">raquetas de nieve</string>
    <string name="frisbee_golf">Frisbee</string>
    <string name="futsal">Fútbol sala</string>
    <string name="american_football">Fútbol americano</string>
    <string name="badminton">Bádminton</string>
    <string name="baseball">Béisbol</string>
    <string name="basketball">Baloncesto</string>
    <string name="bowling">Bolos</string>
    <string name="cricket">Críquet</string>
    <string name="floorball">Floorball</string>
    <string name="handball">Balonmano</string>
    <string name="outdoor_gym">Gimnasio outdoor</string>
    <string name="parkour">Parkour</string>
    <string name="racquet_ball">Racketball</string>
    <string name="rugby">Rugby</string>
    <string name="softball">Sóftbol</string>
    <string name="squash">Squash</string>
    <string name="table_tennis">Tenis de mesa</string>
    <string name="tennis">Tenis</string>
    <string name="volleyball">Voleibol</string>
    <string name="ice_skating">Patinaje sobre hielo</string>
    <string name="ice_hockey">Hockey sobre hielo</string>
    <string name="yoga">Yoga / pilates</string>
    <string name="indoor_cycling">Bicicleta estática</string>
    <string name="treadmill">Máquina de cinta</string>
    <string name="crossfit">Entrenamiento combinado</string>
    <string name="cross_trainer">Máquina elíptica</string>
    <string name="roller_skiing">Esquí con patines</string>
    <string name="indoor_rowing">Máquina de remo</string>
    <string name="strecthing">Estiramientos</string>
    <string name="orienteering">Orientación</string>
    <string name="combat_sport">Artes marciales</string>
    <string name="kettlebell">Pesas rusas</string>
    <string name="sup">Paddle-surf</string>
    <string name="track_and_field">Atletismo</string>
    <string name="multisport">Multideporte</string>
    <string name="aerobics">Aeróbic</string>
    <string name="trekking">Senderismo</string>
    <string name="sailing">Navegación a vela</string>
    <string name="kayaking">Kayak</string>
    <string name="circuit_training">Entrenamiento en circuito</string>
    <string name="triathlon">Triatlón</string>
    <string name="padel">Pádel</string>
    <string name="cheerleading">Animadoras</string>
    <string name="boxing">Boxeo</string>
    <string name="scuba_diving">Buceo</string>
    <string name="free_diving">Buceo en apnea</string>
    <string name="obstacle_race">Carrera de obstáculos</string>
    <string name="adventure_racing">Carrera de aventura</string>
    <string name="gymnastics">Gimnasia</string>
    <string name="canoeing">Piragüismo</string>
    <string name="mountaineering">Montañismo</string>
    <string name="telemark">Esquí telemark</string>
    <string name="windsurfing">Windsurf</string>
    <string name="surfing">Surf</string>
    <string name="kitesurfing_kiting">Kitesurf / cometas</string>
    <string name="paragliding">Parapente</string>
    <string name="fishing">Pesca</string>
    <string name="hunting">Caza</string>
    <string name="snorkeling">Buceo con tubo</string>
    <string name="aquathlon">Acuatlón</string>
    <string name="swimrun">Swimrun</string>
    <string name="duathlon">Duatlón</string>
    <string name="gravel_cycling">Ciclismo de gravel</string>
    <string name="mermaiding">Buceo de sirena</string>
    <string name="jump_rope">Saltar a la comba</string>
    <string name="trackrunning">Pista de atletismo</string>
    <string name="calisthenics">Calistenia</string>
    <string name="e_biking">E-BIKE</string>
    <string name="e_mtb">E-MTB</string>
    <string name="backcountry_skiing">Esquí de travesía</string>
    <string name="wheelchairing">Deporte con silla de ruedas</string>
    <string name="handcycling">Ciclismo de mano</string>
    <string name="splitboarding">Splitboarding</string>
    <string name="biathlon">Biatlón</string>
    <string name="meditation">Meditación</string>
    <string name="field_hockey">Hockey hierba</string>
    <string name="cyclocross">Ciclocrós</string>
    <string name="vertical_running">KM Vertical</string>
    <string name="ski_mountaineering">Esquí de montaña</string>
    <string name="skate_skiing">Esquí de skating</string>
    <string name="classic_skiing">Esquí clásico</string>
    <string name="pilates">Pilates</string>
    <string name="chores">Quehaceres</string>
    <string name="new_yoga">Yoga</string>

    <string name="all_trails">Todo para pistas</string>
    <string name="all_walking">Todo para caminar</string>
    <string name="all_downhill">Descenso</string>
    <string name="all_swimming">Natación</string>
    <string name="all_paddling">Todo para remo</string>
    <string name="all_roller_sports">Rollerski y patinaje</string>
    <string name="all_surf_and_beach">Surf y playa</string>

    <!-- Activity groups -->
    <string name="martial_arts">Todo para artes marciales</string>
    <string name="racket_sports">Todo para deportes de raqueta</string>
    <string name="ride">Todo para ciclismo</string>
    <string name="rowing_group">Todo para remo</string>
    <string name="run_group">Todo para correr</string>
    <string name="strength">Todo para entrenamientos de fuerza</string>
    <string name="swim_group">Todo para natación</string>
    <string name="team_sports">Todo para deportes de equipo</string>
    <string name="walking_group">Todo para caminar</string>

    <!-- Measurement Units -->
    <string name="km">km</string>
    <string name="mile">mi</string>
    <string name="km_h">km/h</string>
    <string name="m_min">m/min</string>
    <string name="mph">mph</string>
    <string name="ft_min">pies/min</string>
    <string name="per_km">/km</string>
    <string name="per_mi">/mi</string>
    <string name="per_100_m">/100 m</string>
    <string name="per_100_yard">/100 yardas</string>
    <string name="seconds">s</string>
    <string name="minute">min</string>
    <string name="hour">h</string>
    <string name="hours">Horas</string>
    <string name="days">días</string>
    <string name="meters">m</string>
    <string name="feet">pies</string>
    <string name="per_minute">/min</string>
    <string name="minute_km">min/km</string>
    <string name="pounds">lb</string>
    <string name="kilograms">kg</string>
    <string name="bpm">lpm</string>
    <string name="kcal">kcal</string>
    <string name="watt">W</string>
    <string name="vo2maxUnit">ml/kg/min</string>
    <string name="bar">bares</string>
    <string name="psi">psi</string>
    <string name="sec">seg</string>
    <string name="liters_per_minute">l/min</string>
    <string name="cubic_feet_per_minute">pies³/min</string>
    <string name="cubic_meter_per_second">m³/s</string>
    <string name="kilometers">Kilómetros</string>
    <string name="miles">Millas</string>
    <string name="knots">nudos</string>
    <string name="nautical_mile">nmi</string>
    <string name="meters_long">metros</string>
    <string name="feet_long">pies</string>
    <string name="ms">ms</string>
    <string name="liters">l</string>
    <string name="cubic_meter">m³</string>
    <string name="cm">cm</string>
    <string name="m_s">m/s</string>
    <string name="inch">pulgadas</string>
    <string name="ft_s">pies/s</string>
    <string name="grams">gramos</string>
    <string name="ounce">onzas</string>
    <string name="round">/ronda</string>
    <string name="min_sec">min\'s</string>
    <!-- Measurement Units -->

    <!-- Suunto Information Model units -->
    <string name="TXT_M">m</string>
    <string name="TXT_M_SEC">m/s</string>
    <string name="TXT_KM">km</string>
    <string name="TXT_FEET">pies</string>
    <string name="TXT_KFT_POSTFIX">k/pies</string>
    <string name="TXT_MI">mi</string>
    <string name="TXT_NMI">nmi</string>
    <string name="TXT_KMH">km/h</string>
    <string name="TXT_MPH">mph</string>
    <string name="TXT_KN">kn</string>
    <string name="TXT_W">W</string>
    <string name="TXT_J_JOULE">J</string>
    <string name="TXT_BPM">lpm</string>
    <string name="TXT_KCAL">kcal</string>
    <string name="TXT_EPOC_ML_KG">ml/kg</string>
    <string name="TXT_ML_KG_MIN">ml/kg/min</string>
    <string name="TXT_RPM">rpm</string>
    <string name="TXT_PER_MIN">/min</string>
    <string name="TXT_M_MIN">m/min</string>
    <string name="TXT_FT_MIN">pies/min</string>
    <string name="TXT_HPA_POSTFIX">hPa</string>
    <string name="TXT_KPA_POSTFIX">kPa</string>
    <string name="TXT_PA_POSTFIX">Pa</string>
    <string name="TXT_INHG_POSTFIX">inHg</string>
    <string name="TXT_CELSIUS">°C</string>
    <string name="TXT_FAHRENHEIT">°F</string>
    <string name="TXT_H">h</string>
    <string name="TXT_S_SECONDS">s</string>
    <string name="TXT_MS">ms</string>
    <string name="TXT_YD">yardas</string>
    <string name="TXT_PER_100M">/100 m</string>
    <string name="TXT_PER_100YD">/100 yardas</string>
    <string name="TXT_PER_500M">/500 m</string>
    <string name="TXT_PER_KM">/km</string>
    <string name="TXT_PER_MILE">/mi</string>
    <string name="TXT_DEGREE_SYMBOL">°</string>
    <string name="TXT_KG_POSTFIX">kg</string>
    <string name="TXT_LB_POSTFIX">lb</string>
    <string name="TXT_CM">cm</string>
    <string name="TXT_IN">pulgadas</string>
    <string name="TXT_M_HOUR">m/hora</string>
    <string name="TXT_FT_HOUR">pies/h</string>
    <string name="TXT_KNM">kN/m</string>
    <string name="TXT_MILS">Milésimas</string>
    <string name="TXT_K">K</string>
    <string name="TXT_HZ">Hz</string>
    <string name="TXT_RAD">rad</string>
    <string name="TXT_G_POSTFIX">g</string>
    <string name="TXT_OZ_POSTFIX">oz</string>

    <!-- Suunto Information Model units -->

    <!-- Offline region units -->
    <string name="square_kilometers">km²</string>
    <string name="square_miles">mi²</string>
    <!-- END Offline region units -->

    <!-- Laps data categories -->
    <string name="summary_item_category_cadence">Cadencia</string>
    <string name="summary_item_category_distance">Distancia</string>
    <string name="summary_item_category_dive">Buceo</string>
    <string name="summary_item_category_duration">Duración</string>
    <string name="summary_item_category_heartRate">Frecuencia cardíaca</string>
    <string name="summary_item_category_physiology">Fisiología</string>
    <string name="summary_item_category_power">Potencia</string>
    <string name="summary_item_category_speed_and_pace">Velocidad y ritmo</string>
    <string name="summary_item_category_vertical">Vertical</string>
    <string name="summary_item_category_breath">Respiración</string>
    <string name="summary_item_category_head_angle">Ángulo de cabeza</string>
    <string name="summary_item_category_other">Otro</string>
    <string name="suunto_plus_category">SuuntoPlus™</string>
    <!-- Laps data categories -->

    <!-- Laps data items -->
    <string name="summary_item_title_max_altitude">Altitud (MÁX.)</string>
    <string name="summary_item_title_min_altitude">Altitud (MÍN.)</string>
    <string name="summary_item_title_ascent">Ascenso</string>
    <string name="summary_item_title_avg_cadence">Cadencia (MED.)</string>
    <string name="summary_item_title_cumulated_distance">Distancia acumulada</string>
    <string name="summary_item_title_cumulated_duration">Duración acumulada</string>
    <string name="summary_item_title_descent">Bajada</string>
    <string name="summary_item_title_descent_downhill">Pendiente descenso</string>
    <string name="summary_item_title_distance">Distancia</string>
    <string name="summary_item_title_nautical_distance">Distancia náutica</string>
    <string name="summary_item_title_distance_downhill">Distancia descenso</string>
    <string name="summary_item_title_duration">Duración</string>
    <string name="summary_item_title_duration_downhill">Duración descenso</string>
    <string name="summary_item_title_energy">Energía</string>
    <string name="summary_item_title_avg_hr">Frecuencia cardíaca (MED.)</string>
    <string name="summary_item_title_min_hr">Ritmo cardíaco (MÍN.)</string>
    <string name="summary_item_title_max_hr">Ritmo cardíaco (MÁX.)</string>
    <string name="summary_item_title_avg_pace">Ritmo (MED.)</string>
    <string name="summary_item_title_max_pace">Ritmo (MÁX.)</string>
    <string name="summary_item_title_avg_power">Potencia (MED.)</string>
    <string name="summary_item_title_max_power">Potencia (MÁX.)</string>
    <string name="summary_item_title_avg_speed">Velocidad (MED.)</string>
    <string name="summary_item_title_avg_speed_downhill">Velocidad descenso (MED.)</string>
    <string name="summary_item_title_max_speed">Velocidad (MÁX.)</string>
    <string name="summary_item_title_max_speed_downhill">Velocidad descenso (MÁX.)</string>
    <string name="summary_item_title_nautical_avg_speed">Velocidad náutica (MED)</string>
    <string name="summary_item_title_nautical_max_speed">Velocidad náutica (MÁX)</string>
    <string name="summary_item_title_avg_swim_stroke_rate">Frecuencia de brazada (MED.)</string>
    <string name="summary_item_title_avg_swim_pace">Ritmo de nado (MED.)</string>
    <string name="summary_item_title_swim_style">Estilo de natación</string>
    <string name="summary_item_title_avg_temperature">Temperatura (MED.)</string>
    <string name="summary_item_title_max_temperature">Temperatura (MÁX.)</string>
    <string name="summary_item_title_interval_type">Tipo de intervalo</string>
    <string name="summary_item_title_avg_vertical_speed">Velocidad vertical (MED.)</string>
    <string name="summary_item_title_avg_sea_level_pressure">Presión a nivel del mar (MED.)</string>
    <string name="summary_item_title_avg_swolf">Swolf (MED.)</string>
    <string name="summary_item_title_max_depth">Profundidad (MÁX.)</string>
    <string name="summary_item_title_min_depth">Profundidad (MÍN.)</string>
    <string name="summary_item_title_dive_time">Tiempo de inmersión</string>
    <string name="summary_item_title_dive_time_max">Tiempo máx. de inmersión</string>
    <string name="summary_item_title_dive_surface_time">Tiempo en superficie</string>
    <string name="summary_item_title_dive_recovery_time">Tiempo de recuperación entre inmersiones</string>
    <string name="summary_item_title_skip_count">Saltos</string>
    <string name="summary_item_title_rowing_stroke_count">Brazadas</string>
    <string name="summary_item_title_revolution_count">Repeticiones</string>
    <string name="summary_item_title_avg_stride">Zancada (MED.)</string>
    <string name="summary_item_title_fat_consumption">Consumo de grasa</string>
    <string name="summary_item_title_carbohydrate_consumption">Consumo de hidratos de carbono</string>
    <string name="summary_item_title_avg_ground_contact_time">Tiempo de contacto con el suelo (MED.)</string>
    <string name="summary_item_title_avg_vertical_oscillation">Oscilación vertical (MED.)</string>
    <string name="summary_item_title_avg_ground_contact_balance">Equilibrio de contacto con el suelo (MED.)</string>
    <string name="suuntoplus_item_title_avg">%s (MED.)</string>

    <!-- todo fix TSS labels with actual ones -->
    <string name="summary_item_np">Normalized Power®</string>
    <string name="summary_item_ngp">Normalized Graded Pace™</string>
    <string name="summary_item_agap">Average Grade Adjusted Pace</string>
    <string name="summary_item_if">Intensity Factor</string>
    <string name="summary_item_tss">Training Stress Score®</string>
    <string name="summary_item_tss_title">PROGRESO</string>
    <!-- Laps data items -->

    <!-- Laps tables -->
    <string name="laps_show_markers_on_map">Mostrar marcadores de vuelta en la ruta</string>
    <string name="laps_table_type_manual">Manual</string>
    <string name="laps_table_type_downhill">Descenso</string>
    <string name="laps_table_type_interval">Intervalo</string>
    <string name="laps_table_type_dive">Buceo</string>
    <string name="laps_table_type_autolap_distance_format">%s %s</string>
    <string name="laps_table_type_autolap_duration">%s</string>
    <string name="laps_table_type_autolap_fallback">Autolap</string>

    <!-- Laps tables -->

    <!-- Swim styles -->
    <string name="swimstyle_other">Otro</string>
    <string name="swimstyle_freestyle">Libre</string>
    <string name="swimstyle_butterfly">Mariposa</string>
    <string name="swimstyle_breaststroke">Braza</string>
    <string name="swimstyle_backstroke">Atrás</string>
    <string name="swimstyle_drill">Ejercicio</string>
    <!-- Swim styles -->

    <!-- Dive event localizations -->
    <!-- Dive -->
    <string name="event_bookmark">Marcador guardado por usuario</string>

    <!-- Dive -->
    <string name="event_notify_depth">Alarma de profundidad</string>
    <!-- Dive -->
    <string name="event_notify_depth_description">Se llegó a la profundidad configurada.</string>
    <!-- Dive -->
    <string name="event_notify_surface_time">En superficie / Inmersión finalizada</string>
    <!-- Dive -->
    <string name="event_notify_surface_time_description">Se detuvo el cálculo del tiempo de inmersión.</string>
    <!-- Dive -->
    <string name="event_notify_deco">Inmersión con descompresión</string>
    <!-- Dive -->
    <string name="event_notify_deco_description">Inmersión que se convirtió en una inmersión con descompresión, en la que es obligatorio realizar paradas de descompresión durante el ascenso.</string>
    <!-- Dive -->
    <string name="event_notify_deco_window">Parada de descompresión iniciada</string>
    <!-- Dive -->
    <string name="event_notify_deco_window_description">Se inició el cálculo de la parada de descompresión.</string>
    <!-- Dive -->
    <string name="event_notify_setpoint_switch">Se cambió el ajuste</string>
    <!-- Dive -->
    <string name="event_notify_setpoint_switch_description">Cambio de ajuste automático.</string>
    <!-- Dive -->
    <string name="event_notify_safety_stop_broken">Notificación de parada de seguridad voluntaria</string>
    <!-- Dive -->
    <string name="event_notify_safety_stop_broken_description">Parada de seguridad voluntaria omitida.</string>
    <!-- Dive -->
    <string name="event_notify_safety_stop">Parada de seguridad iniciada</string>
    <!-- Dive -->
    <string name="event_notify_safety_stop_description">Entró en la ventana de la parada de seguridad y se inició el temporizador.</string>
    <!-- Dive -->
    <string name="event_notify_deep_stop">Parada profunda iniciada</string>
    <!-- Dive -->
    <string name="event_notify_deep_stop_description">Ingresó en la ventada de la parada profunda y se inició el temporizador.</string>
    <!-- Dive -->
    <string name="event_notify_deep_stop_ahead">Paradas profundas activadas</string>
    <!-- Dive -->
    <string name="event_notify_deep_stop_ahead_description">La función de paradas profundas durante el ascenso está activada.</string>
    <!-- Dive -->
    <string name="event_notify_diluent_hypoxia">pO₂ baja del diluyente</string>
    <!-- Dive -->
    <string name="event_notify_diluent_hypoxia_description">La presión parcial de oxígeno del diluyente está por debajo del nivel de seguridad de &lt; 0,18 bares.</string>
    <!-- Dive -->
    <string name="event_notify_air_time">Alarma de tiempo de gas</string>
    <!-- Dive -->
    <string name="event_notify_air_time_description">Alarma de tiempo de gas configurable.</string>
    <!-- Dive -->
    <string name="event_notify_tank_pressure">Alarma de presión de la botella</string>
    <!-- Dive -->
    <string name="event_notify_tank_pressure_description">Alarma de presión de botella configurable %s.</string>
    <!-- Dive -->
    <string name="event_notify_missed_deco_ack">Omisión de parada de descompresión autorizada por el usuario</string>
    <!-- Dive -->
    <string name="event_notify_missed_deco_ack_description">El usuario ha autorizado que se omita la parada de descompresión y ha decidido continuar la inmersión.</string>
    <!-- Dive -->
    <string name="event_notify_dive_time">Alarma de tiempo de inmersión</string>
    <!-- Dive -->
    <string name="event_notify_dive_time_description">Alarma de tiempo de inmersión configurable</string>
    <!-- Dive -->
    <string name="event_notify_user_ndl">Alarma del NDL</string>
    <!-- Dive -->
    <string name="event_notify_user_ndl_description">Alarma del NDL configurada por el usuario alcanzada.</string>

    <!-- Dive -->
    <string name="event_warning_icd_penalty">Advertencia de contradifusión isobárica (CDI)</string>
    <!-- Dive -->
    <string name="event_warning_icd_penalty_description">La contradifusión isobárica (CDI) tiene lugar cuando diferentes gases inertes (como el nitrógeno y el helio) se difunden en direcciones distintas durante una inmersión.</string>
    <!-- Dive -->
    <string name="event_warning_deep_stop_penalty">Parada profunda omitida</string>
    <!-- Dive -->
    <string name="event_warning_deep_stop_penalty_description">Parada profunda recomendada omitida.</string>
    <!-- Dive -->
    <string name="event_warning_mandatory_safety_stop">Advertencia de parada de seguridad obligatoria</string>
    <!-- Dive -->
    <string name="event_warning_mandatory_safety_stop_description">La parada de seguridad se volvió obligatoria por haber superado la velocidad de ascenso de 10 m/min (32,8 pies/min) por más de 5 segundos.</string>
    <!-- Dive -->
    <string name="event_warning_cns80" formatted="false">Advertencia de SNC al 80%</string>
    <!-- Dive -->
    <string name="event_warning_cns80_description" formatted="false">El nivel de toxicidad de oxígeno en el sistema nervioso central ha llegado al 80% del límite recomendado.</string>
    <!-- Dive -->
    <string name="event_warning_cns100" formatted="false">Advertencia de SNC al 100%</string>
    <!-- Dive -->
    <string name="event_warning_cns100_description" formatted="false">El nivel de toxicidad de oxígeno en el sistema nervioso central ha llegado al 100% del límite recomendado.</string>
    <!-- Dive -->
    <string name="event_warning_otu250">Advertencia de UTO 250</string>
    <!-- Dive -->
    <string name="event_warning_otu250_description" formatted="false">Se llegó a aproximadamente el 80% del límite diario recomendado de unidades de tolerancia al oxígeno.</string>
    <!-- Dive -->
    <string name="event_warning_otu300">Advertencia de UTO 300</string>
    <!-- Dive -->
    <string name="event_warning_otu300_description" formatted="false">Se llegó a aproximadamente el 100% del límite diario recomendado de unidades de tolerancia al oxígeno.</string>
    <!-- Dive -->
    <string name="event_warning_air_time">Alarma de tiempo de gas</string>
    <!-- Dive -->
    <string name="event_warning_air_time_description">Presión de botella por debajo de 35 bares / ~510 psi que equivale a un tiempo de gas de 0 min.</string>
    <!-- Dive -->
    <string name="event_warning_max_depth">Alarma de profundidad máxima</string>
    <!-- Dive -->
    <string name="event_warning_max_depth_description">Se alcanzó el límite de profundidad máxima configurado.</string>
    <!-- Dive -->
    <string name="event_warning_tank_pressure">Alarma de presión de botella:</string>
    <!-- Dive -->
    <string name="event_warning_tank_pressure_description">Es una advertencia obligatoria de presión de botella a 50 bares / ~725 psi.</string>
    <!-- Dive -->
    <string name="event_warning_po2_high">Advertencia de pO₂ elevada</string>
    <!-- Dive -->
    <string name="event_warning_po2_high_description">La presión parcial de oxígeno ha superado el límite definido.</string>
    <!-- Dive -->
    <string name="event_warning_deco_broken">Parada de descompresión omitida</string>
    <!-- Dive -->
    <string name="event_warning_deco_broken_description">Tras omitirse la parada de descompresión, la inmersión está fuera del perfil de algoritmo recomendado.</string>
    <!-- Dive -->
    <string name="event_warning_mini_lock">Parada descompresión omitida en inmersión anterior</string>
    <!-- Dive -->
    <string name="event_warning_mini_lock_description">El cálculo del algoritmo de descompresión está fuera del perfil de algoritmo recomendado.</string>
    <!-- Dive -->
    <string name="event_warning_no_deco_time">NDL bajo</string>
    <!-- Dive -->
    <string name="event_warning_no_deco_time_description">El NDL restante es de 5 minutos.</string>

    <!-- Dive -->
    <string name="event_alarm_mandatory_safety_stop_broken">Alarma de parada de seguridad obligatoria</string>
    <!-- Dive -->
    <string name="event_alarm_mandatory_safety_stop_broken_description">Parada de seguridad obligatoria omitida.</string>
    <!-- Dive -->
    <string name="event_alarm_ascent_speed">Alarma de velocidad de ascenso</string>
    <!-- Dive -->
    <string name="event_alarm_ascent_speed_description">Velocidad de ascenso por encima del límite de seguridad de 10 m/min (32,8 pies/min).</string>
    <!-- Dive -->
    <string name="event_alarm_diluent_hyperoxia">Alarma de pO₂ alta del diluyente</string>
    <!-- Dive -->
    <string name="event_alarm_diluent_hyperoxia_description">La presión parcial de oxígeno del diluyente supera el nivel de seguridad de &gt; 1,6 bares.</string>
    <!-- Dive -->
    <string name="event_alarm_ceiling_broken">Alarma de techo de descompresión superado</string>
    <!-- Dive -->
    <string name="event_alarm_ceiling_broken_description">Se superó el techo de descompresión de &gt; 0,6 m / &gt; 2 pies y el cálculo descompresión se pausó.</string>
    <!-- Dive -->
    <string name="event_alarm_po2_high">Alarma de pO₂ alta</string>
    <!-- Dive -->
    <string name="event_alarm_po2_high_description">La presión parcial de oxígeno supera el nivel de seguridad de &gt; 1,6 bares.</string>
    <!-- Dive -->
    <string name="event_alarm_po2_low">Alarma de pO₂ baja</string>
    <!-- Dive -->
    <string name="event_alarm_po2_low_description">Presión parcial de oxígeno por debajo del límite de seguridad de &lt; 0,18 bares.</string>

    <!-- Dive -->
    <string name="event_error_ceiling_broken">Algoritmo de descompresión bloqueado</string>
    <!-- Dive -->
    <string name="event_error_ceiling_broken_description">El algoritmo de descompresión se bloquea por haber omitido paradas de descompresión más de 3 minutos.</string>

    <!-- Dive -->
    <string name="event_dive_active">Inmersión iniciada</string>
    <!-- Dive -->
    <string name="event_dive_active_no_water_contact_description">El cálculo de tiempo inmersión se inició sin el contacto con agua.</string>
    <!-- Dive -->
    <string name="event_dive_active_water_contact_description">Se inició el cálculo de tiempo de inmersión.</string>

    <!-- Dive -->
    <string name="event_gas_switch">Cambio de gas</string>
    <!-- Dive -->
    <string name="event_gas_switch_to_gas">Gas cambiado por usuario\n→ %s</string>
    <!-- Dive -->
    <string name="event_gas_switch_from_to_gas">Gas cambiado por usuario\n%s → %s</string>

    <!-- Dive -->
    <string name="event_setpoint_switch">Se cambió el ajuste</string>
    <!-- Dive -->
    <string name="event_setpoint_switch_description_manual">Cambio de ajuste manual.</string>
    <!-- Dive -->
    <string name="event_setpoint_switch_description_custom">Ajuste cambiado a personalizado.</string>
    <!-- Dive -->
    <string name="event_setpoint_switch_high">Límite superior</string>
    <!-- Dive -->
    <string name="event_setpoint_switch_low">Límite inferio</string>
    <!-- Dive -->
    <string name="event_setpoint_switch_custom">Ajuste personalizado</string>

    <!-- Dive -->
    <string name="event_gas_edit_insert">Se ha agregado el gas %s</string>
    <!-- Dive -->
    <string name="event_gas_edit_remove">Se ha eliminado el gas %s</string>

    <!-- Dive -->
    <string name="event_dive_timer_started">Cronómetro iniciado</string>
    <!-- Dive -->
    <string name="event_dive_timer_started_description">Temporizador iniciado por usuario.</string>
    <!-- Dive -->
    <string name="event_dive_timer_stopped">Cronómetro detenido</string>
    <!-- Dive -->
    <string name="event_dive_timer_stopped_description">Temporizador detenido por usuario.</string>

    <!-- Dive -->
    <string name="tissue_reset">Se restauraron los tejidos antes de la inmersión</string>
    <!-- Dive -->
    <string name="tissue_reset_description">El usuario restauró los tejidos antes de la inmersión.</string>

    <!-- Dive -->
    <string name="event_closed_circuit_mode">Modo de circuito cerrado</string>
    <!-- Dive -->
    <string name="event_closed_circuit_mode_activated">Activado</string>
    <!-- Dive -->
    <string name="event_closed_circuit_mode_deactivated">Desactivado</string>

    <!-- Dive -->
    <string name="event_out_of_algorithm_model_generic_title">Desviación</string>
    <string name="event_out_of_algorithm_model_generic_description">El cálculo del algoritmo de descompresión está fuera del perfil de algoritmo recomendado.</string>
    <string name="event_out_of_algorithm_model_ceiling_broken_description">Tras omitirse la parada de descompresión, la inmersión está fuera del perfil de algoritmo recomendado.</string>
    <!-- END dive event localizations -->

    <!-- Notification channel names -->
    <string name="notification_channel_activity_recording">Registro de actividades</string>
    <string name="all_filter_tag">Todo</string>
    <string name="me_filter_tag">Yo</string>
    <string name="suunto_filter_tag">Suunto</string>
    <string name="following_filter_tag">Siguiendo</string>

    <string name="no_search_results">No se han encontrado resultados</string>

    <string name="not_sure">No lo sé</string>
    <string name="cycle_length">Duración del ciclo</string>
    <string name="period_length">Duración del periodo</string>
    <plurals name="value_days">
        <item quantity="one">%d día</item>
        <item quantity="few">%d de días</item>
        <item quantity="many">%d de días</item>
        <item quantity="other">%d días</item>
    </plurals>
    <plurals name="unit_days">
        <item quantity="one">día</item>
        <item quantity="few">de días</item>
        <item quantity="many">de días</item>
        <item quantity="other">días</item>
    </plurals>

    <string name="training_hub_impact_recovery">Recuperación</string>
    <string name="training_hub_impact_aerobic">Aeróbico</string>
    <string name="training_hub_impact_cardio_long_aerobic">Aeróbico largo</string>
    <string name="training_hub_impact_cardio_vo2_max">VO₂max</string>
    <string name="training_hub_impact_cardio_anaerobic_hard">Anaeróbico - intenso</string>
    <string name="training_hub_impact_cardio_aerobic_anaerobic">Aeróbico/anaeróbico</string>
    <string name="training_hub_impact_cardio_anaerobic">Anaeróbico</string>
    <string name="training_hub_impact_cardio_heavy_aerobic">Aeróbico pesado</string>
    <string name="training_hub_impact_muscular_speed_and_agility">Velocidad y agilidad</string>
    <string name="training_hub_impact_muscular_speed_and_strength">Velocidad y fuerza</string>
    <string name="training_hub_impact_muscular_flexibility">Flexibilidad</string>
    <string name="training_hub_impact_muscular_strength">Fuerza</string>
</resources>
