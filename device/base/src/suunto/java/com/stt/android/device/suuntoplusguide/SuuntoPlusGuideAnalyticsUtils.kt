package com.stt.android.device.suuntoplusguide

import android.content.SharedPreferences
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsUserProperty
import com.stt.android.analytics.toOnOff
import com.stt.android.device.domain.suuntoplusfeature.ListFeaturesUseCase
import com.stt.android.device.domain.suuntoplusguide.ListSuuntoPlusGuidesUseCase
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginStatus
import com.stt.android.device.domain.suuntoplusguide.TrainingPlanUseCase
import com.stt.android.device.domain.suuntoplusguide.isWorkoutPlan
import com.stt.android.di.SuuntoSharedPrefs
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.first
import timber.log.Timber
import javax.inject.Inject

@OptIn(FlowPreview::class)
class SuuntoPlusGuideAnalyticsUtils @Inject constructor(
    private val listSuuntoPlusGuidesUseCase: ListSuuntoPlusGuidesUseCase,
    private val trainingPlanUseCase: TrainingPlanUseCase,
    private val listSuuntoPlusFeaturesUseCase: ListFeaturesUseCase,
    @SuuntoSharedPrefs private val suuntoSharedPreferences: SharedPreferences,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) {
    suspend fun sendGuideSelectionScreenEvent() {
        try {
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.SUUNTO_PLUS_GUIDES_SELECTION_SCREEN,
                AnalyticsProperties()
                    .putWatchDetailsProperties(suuntoSharedPreferences)
                    .putGuideStatisticsProperties()
            )
        } catch (e: Exception) {
            Timber.w(e, "Failed to send SuuntoPlus™ Guide selection screen event")
        }
    }

    suspend fun sendFeaturesSelectionScreenEvent() {
        try {
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.SUUNTO_PLUS_MY_SPORTS_APPS_SCREEN,
                AnalyticsProperties()
                    .putWatchDetailsProperties(suuntoSharedPreferences)
                    .putMySportsAppsProperties()
            )
        } catch (e: Exception) {
            Timber.w(e, "Failed to send SuuntoPlus™ Feature selection screen event")
        }
    }

    fun sendMySportsAppsUseInWatchToggledEvent(name: String, enabled: Boolean) {
        try {
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.SUUNTO_PLUS_MY_SPORTS_APPS_USE_IN_WATCH_TOGGLED,
                AnalyticsProperties()
                    .put(AnalyticsEventProperty.SUUNTO_PLUS_MY_SPORTS_APPS_NAME, name)
                    .put(
                        AnalyticsEventProperty.SUUNTO_PLUS_MY_SPORTS_APPS_NEW_SETTING,
                        enabled.toOnOff()
                    )
                    .putWatchDetailsProperties(suuntoSharedPreferences)
            )
        } catch (e: Exception) {
            Timber.w(e, "Failed to send my sports apps use in watch toggled event")
        }
    }

    suspend fun sendUserMadeChangesToGuidesEvent(userDeletedCount: Int) {
        try {
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.SUUNTO_PLUS_GUIDES_SELECTION_CHANGED,
                AnalyticsProperties()
                    .putWatchDetailsProperties(suuntoSharedPreferences)
                    .putGuideStatisticsProperties(userDeletedCount = userDeletedCount)
            )
        } catch (e: Exception) {
            Timber.w(e, "Failed to send SuuntoPlus™ Guides selection changed event")
        }
    }

    suspend fun sendUserMadeChangesToFeaturesEvent() {
        try {
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.SUUNTO_PLUS_FEATURES_SELECTION_CHANGED,
                AnalyticsProperties()
                    .putWatchDetailsProperties(suuntoSharedPreferences)
                    .putFeatureStatisticsProperties()
            )
        } catch (e: Exception) {
            Timber.w(e, "Failed to send SuuntoPlus™ Features selection changed event")
        }
    }

    fun sendBottomSheetOpenedEvent() {
        try {
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.SUUNTO_PLUS_GUIDES_LINK_TAPPED,
                AnalyticsProperties().apply {
                    putWatchDetailsProperties(suuntoSharedPreferences)
                    put(
                        AnalyticsEventProperty.LINK_TYPE,
                        AnalyticsPropertyValue.SuuntoPlusGuideLinkType.BOTTOM_LINK
                    )
                }
            )
        } catch (e: Exception) {
            Timber.w(e, "Failed to send bottom sheet event")
        }
    }

    suspend fun updatePlannedWorkoutsUserProperty() {
        try {
            val guideContainer = listSuuntoPlusGuidesUseCase.listGuides().first()
            val plansCount = guideContainer.guidesInWatch
                .plus(guideContainer.guidesNotInWatch)
                .plus(guideContainer.guidesNotInWatch)
                .count { it.guide.isWorkoutPlan }

            amplitudeAnalyticsTracker.trackUserProperty(
                AnalyticsUserProperty.NUM_PLANNED_WORKOUTS,
                plansCount
            )
        } catch (e: Exception) {
            Timber.w(e, "Failed to update NumberOfWorkoutPlansCreated property")
        }
    }

    private suspend fun AnalyticsProperties.putMySportsAppsProperties(): AnalyticsProperties {
        val featureContainer = listSuuntoPlusFeaturesUseCase.listFeaturesAsFlow().first()

        val sportsAppsInLibraryCount = featureContainer.features.size + featureContainer.expiredAndNonCompatibleFeatures.size
        val sportsAppsInWatchCount = featureContainer.features.size

        put(AnalyticsEventProperty.SUUNTO_PLUS_MY_SPORTS_APPS_IN_LIBRARY, sportsAppsInLibraryCount)
        put(AnalyticsEventProperty.SUUNTO_PLUS_MY_SPORTS_APPS_IN_WATCH, sportsAppsInWatchCount)

        return this
    }

    private suspend fun AnalyticsProperties.putGuideStatisticsProperties(
        userDeletedCount: Int? = null
    ): AnalyticsProperties {
        val guideContainer = listSuuntoPlusGuidesUseCase.listGuides().first()
        val planContainer = trainingPlanUseCase.listPlans().first()
        val allGuides = guideContainer.guidesInWatch.map { it.guide } + guideContainer.guidesNotInWatch.map { it.guide }
        val allPlans = planContainer.plansInWatch.map { it.plan } + planContainer.plansNotInWatch.map { it.plan } + planContainer.plansNotSupported.map { it.plan }
        val totalCount = allGuides.size
        val pinnedCount = allGuides.count { it.pinned }
        val nonPinnedInWatchCount = guideContainer.guidesInWatch.count { !it.guide.pinned }
        val notInWatchCount = guideContainer.guidesNotInWatch.size
        val sourcesPinned = allGuides.filter { it.pinned }.map { it.owner }.distinct()
        val sourcesNonPinnedInWatch = allGuides.filter { !it.pinned }.map { it.owner }.distinct()
        val notCompatibleCount = guideContainer.guidesNotInWatch.count { it.status == SuuntoPlusPluginStatus.NOT_SUPPORTED }
        put(AnalyticsEventProperty.SUUNTO_PLUS_GUIDES_NAME, allGuides.joinToString { it.name })
        put(AnalyticsEventProperty.SUUNTO_PLUS_PLANS_NAME, allPlans.joinToString { it.name })
        put(AnalyticsEventProperty.SUUNTO_PLUS_GUIDES_TOTAL_COUNT, totalCount)
        put(AnalyticsEventProperty.SUUNTO_PLUS_GUIDES_PINNED_COUNT, pinnedCount)
        put(
            AnalyticsEventProperty.SUUNTO_PLUS_GUIDES_NON_PINNED_IN_WATCH_COUNT,
            nonPinnedInWatchCount
        )
        put(
            AnalyticsEventProperty.SUUNTO_PLUS_GUIDES_NOT_IN_WATCH_COUNT,
            notInWatchCount
        )
        put(
            AnalyticsEventProperty.SUUNTO_PLUS_GUIDE_NOT_COMPATIBLE_COUNT,
            notCompatibleCount
        )
        put(AnalyticsEventProperty.SUUNTO_PLUS_GUIDE_SOURCES_PINNED, sourcesPinned)
        put(
            AnalyticsEventProperty.SUUNTO_PLUS_GUIDE_SOURCES_NON_PINNED_IN_WATCH,
            sourcesNonPinnedInWatch
        )

        if (userDeletedCount != null) {
            put(AnalyticsEventProperty.SUUNTO_PLUS_GUIDE_GUIDES_DELETED, userDeletedCount)
        }
        return this
    }

    private suspend fun AnalyticsProperties.putFeatureStatisticsProperties(): AnalyticsProperties {
        val featureContainer = listSuuntoPlusFeaturesUseCase.listFeaturesAsFlow().first()

        val featuresInUse = featureContainer.features
            .map { it.feature }
            .filter { it.enabled }
            .map { "${it.name} ${it.id}" }
        val featuresNotInUse = featureContainer.features
            .map { it.feature }
            .filter { !it.enabled }
            .map { "${it.name} ${it.id}" }

        put(AnalyticsEventProperty.SUUNTO_PLUS_FEATURES_IN_USE, featuresInUse)
        put(AnalyticsEventProperty.SUUNTO_PLUS_FEATURES_NOT_IN_USE, featuresNotInUse)

        return this
    }
}
