package com.stt.android.device.suuntoplusguide.details

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.soy.algorithms.planner.GuideMessagesFormatter
import com.stt.android.R
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodySmallBold
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.suuntoplus.ui.CurveDirection
import com.stt.android.suuntoplus.ui.CurvedLine
import com.stt.android.suuntoplus.ui.EndRepeatItem
import com.stt.android.suuntoplus.ui.ExerciseStepInfoRow
import com.stt.android.utils.color
import com.stt.android.utils.displayNameResource
import com.stt.android.utils.durationIconRes
import com.stt.android.utils.formatDurationText
import com.stt.android.utils.formatTargetText
import com.stt.android.utils.targetIconRes

@Composable
fun WorkoutDetails(
    workoutItem: WorkoutItem,
    expanded: Boolean,
    formatter: GuideMessagesFormatter,
    modifier: Modifier = Modifier
) {
   Column(modifier = modifier.fillMaxWidth()) {
       val duration = workoutItem.duration
       val distance = workoutItem.distance
           .takeIf { it.first != null && it.second != null }
           ?.let { "${it.first} ${stringResource(it.second!!)}" }
       val tss = workoutItem.tss?.toString()

       // Workout stats
       Row(
           modifier = Modifier.fillMaxWidth(),
       ) {
           val statItems = listOfNotNull(
               duration?.let { it to stringResource(R.string.duration) },
               distance?.let { it to stringResource(R.string.distance) },
               tss?.let { it to stringResource(R.string.item_title_tss) }
           )

           val alignments = listOf(
               Alignment.CenterStart,
               Alignment.Center,
               Alignment.CenterEnd
           )

           repeat(3) { index ->
               Box(
                   modifier = Modifier
                       .weight(1f)
                       .fillMaxWidth(),
                   contentAlignment = alignments[index]
               ) {
                   statItems.getOrNull(index)?.let { (value, label) ->
                       WorkoutStat(value, label)
                   }
               }
           }
       }

       Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))

       if (expanded) {
           workoutItem.phases.forEach { phase ->
               when (phase) {
                   is WorkoutExercise -> WorkoutPhaseItem(phase, formatter, false)
                   is WorkoutRepetition -> {
                       RepeatBlock(phase, formatter)
                   }
               }
               Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
           }
       }
   }
}

@Composable
fun RepeatBlock(repetition: WorkoutRepetition, formatter: GuideMessagesFormatter, modifier: Modifier = Modifier) {
    Column(
        modifier = modifier
    ) {
        StartRepeatItem(repetition.count)
        repetition.phases.forEachIndexed { index, item ->
            if (index != 0) {
                WorkoutPhaseLineItem()
            }
            WorkoutPhaseItem(item, formatter, true)
            if (index != repetition.phases.lastIndex) {
                WorkoutPhaseLineItem()
            }
        }
        EndRepeatItem(paddingStart = 1.dp, paddingEnd = 1.6.dp)
    }
}

@Composable
fun StartRepeatItem(
    count: Int,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(40.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        CurvedLine(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight()
                .padding(start = 1.dp),
            direction = CurveDirection.LEFT_DOWN
        )

        Box(
            modifier = Modifier
                .background(
                    MaterialTheme.colorScheme.nearWhite, RoundedCornerShape(4.dp)
                )
                .padding(
                    horizontal = MaterialTheme.spacing.small, vertical = MaterialTheme.spacing.small
                )
        ) {
            Text(
                text = context.resources.getQuantityString(R.plurals.workout_details_repeat_count, count, count),
                style = MaterialTheme.typography.bodySmallBold,
                color = MaterialTheme.colorScheme.nearBlack
            )
        }

        CurvedLine(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight()
                .padding(end = 1.6.dp),
            direction = CurveDirection.RIGHT_DOWN
        )
    }
}

@Composable
fun WorkoutStat(value: String, label: String, modifier: Modifier = Modifier) {
    Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = modifier) {
        Text(text = value, style = MaterialTheme.typography.bodyLargeBold)
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.darkGrey
        )

    }
}

@Composable
fun WorkoutPhaseLineItem(modifier: Modifier = Modifier) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(6.dp)
            .padding(end = MaterialTheme.spacing.xxxsmall),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Box(
            modifier = Modifier
                .width(1.dp)
                .fillMaxHeight()
                .background(MaterialTheme.colorScheme.lightGrey)
        )

        Spacer(
            modifier = Modifier.padding(
                vertical = MaterialTheme.spacing.medium,
                horizontal = MaterialTheme.spacing.medium
            )
        )

        Box(
            modifier = Modifier
                .width(1.dp)
                .fillMaxHeight()
                .background(MaterialTheme.colorScheme.lightGrey)
        )
    }
}

@Composable
fun WorkoutPhaseItem(
    phaseItem: WorkoutExercise,
    formatter: GuideMessagesFormatter,
    fromRepetition: Boolean,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val phaseLabel = stringResource(phaseItem.phase.displayNameResource)
    val formattedDuration = phaseItem.duration.formatDurationText(context.resources, formatter)
    val formattedTarget = phaseItem.target?.formatTargetText(formatter)

    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(end = 0.9.dp)
            .height(IntrinsicSize.Min),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Row {
            Box(
                modifier = Modifier
                    .width(4.dp)
                    .fillMaxHeight()
                    .background(phaseItem.phase.color)
            )

            Column(
                modifier = Modifier.padding(
                    vertical = MaterialTheme.spacing.small,
                    horizontal = MaterialTheme.spacing.medium
                ), verticalArrangement = Arrangement.SpaceAround
            ) {
                Text(
                    text = phaseItem.name ?: phaseLabel,
                    style = androidx.compose.material.MaterialTheme.typography.bodyLargeBold
                )

                ExerciseStepInfoRow(
                    durationIconResource = phaseItem.duration.durationIconRes,
                    duration = formattedDuration,
                    targetIconResource = phaseItem.target?.targetIconRes,
                    target = formattedTarget
                )
            }
        }

        if (fromRepetition) {
            Box(
                modifier = Modifier
                    .width(1.dp)
                    .fillMaxHeight()
                    .background(MaterialTheme.colorScheme.lightGrey)
            )
        }
    }
}
