package com.stt.android.device.suuntoplusguide.details

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.material.Surface
import androidx.compose.material.rememberScaffoldState
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.stt.android.common.viewstate.ViewState
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.AppTheme
import com.stt.android.device.suuntoplusguide.trainingplan.TrainingDayDetails
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class TrainingDayDetailsFragment : Fragment() {
    private val viewModel: TrainingDayDetailsViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        viewModel.loadTrainingDayDetails()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )
        setContent {
            AppTheme {
                val scaffoldState = rememberScaffoldState()
                    Scaffold(
                        scaffoldState = scaffoldState
                    ) { internalPadding ->
                        ContentCenteringColumn(Modifier.padding(internalPadding)) {
                            Surface {
                                val state by viewModel.viewState.observeAsState()
                                // Show error messages
                                val error = (state as? ViewState.Error)?.errorEvent
                                if (error != null && error.shouldHandle) {
                                    LaunchedEffect(error) {
                                        scaffoldState.snackbarHostState.showSnackbar(
                                            getString(error.errorStringRes)
                                        )
                                    }
                                }
                                state?.data?.let { day ->
                                    TrainingDayDetails(day.workoutDay, formatter = viewModel.getSimGuideMessagesFormatter() )
                                }
                            }
                        }
                    }
            }
        }
    }
}
