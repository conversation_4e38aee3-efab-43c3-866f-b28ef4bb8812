package com.stt.android.device.remote.suuntoplusguide

import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncAlreadyRunningException
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncLogEventDao
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncLogUtil
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusGuideSyncLogUtilImpl
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncState
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncStateRepository
import com.stt.android.device.datasource.WatchPluginStatusDataSource
import com.stt.android.device.datasource.suuntoplusfeature.SuuntoPlusFeaturesLocalDataSource
import com.stt.android.device.datasource.suuntoplusguide.GuideZAPPFileStorage
import com.stt.android.device.datasource.suuntoplusguide.SuuntoPlusGuidesLocalDataSource
import com.stt.android.device.datasource.suuntoplusguide.SuuntoWatchCapabilityStore
import com.stt.android.device.datasource.suuntoplusguide.TrainingPlansLocalDataSource
import com.stt.android.device.domain.suuntoplusfeature.SuuntoPlusFeature
import com.stt.android.device.domain.suuntoplusfeature.settings.SportsAppSettingsStateDataSource
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuide
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideId
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginStatus
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginType
import com.stt.android.device.domain.suuntoplusguide.TrainingPlan
import com.stt.android.device.domain.suuntoplusguide.TrainingPlanId
import com.stt.android.device.domain.suuntoplusguide.pluginId
import com.stt.android.device.remote.suuntoplusfeature.SuuntoPlusFeatureManifestExtractor
import com.stt.android.device.remote.suuntoplusfeature.SuuntoPlusFeaturesRemoteDataSource
import com.stt.android.domain.device.DeviceConnectionStateUseCase
import com.stt.android.exceptions.device.WatchPluginNotSupportedByCapabilities
import com.stt.android.exceptions.remote.ClientError
import com.stt.android.utils.FlavorUtils
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import com.suunto.connectivity.suuntoplusguide.SuuntoPlusGuideSyncLogicResult
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.reactive.awaitFirstOrNull
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.time.Clock
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject

class SuuntoPlusGuideRemoteSyncLogic
@Inject constructor(
    private val remoteGuideDataSource: SuuntoPlusGuideRemoteDataSource,
    private val trainingPlanRemoteDataSource: TrainingPlanRemoteDataSource,
    private val localGuideDataSource: SuuntoPlusGuidesLocalDataSource,
    private val localTrainingPlanDataSource: TrainingPlansLocalDataSource,
    private val pluginStatusDataSource: WatchPluginStatusDataSource,
    private val remoteFeaturesDataSource: SuuntoPlusFeaturesRemoteDataSource,
    private val localFeaturesDataSource: SuuntoPlusFeaturesLocalDataSource,
    private val syncLogEventDao: SuuntoPlusGuideSyncLogEventDao,
    private val syncStateRepository: SuuntoPlusSyncStateRepository,
    private val zappStorage: GuideZAPPFileStorage,
    private val capabilityStore: SuuntoWatchCapabilityStore,
    private val settingsStateDataSource: SportsAppSettingsStateDataSource,
    private val clock: Clock,
    private val deviceConnectionStateUseCase: DeviceConnectionStateUseCase,
) : SuuntoPlusGuideSyncLogUtil by SuuntoPlusGuideSyncLogUtilImpl(syncLogEventDao) {

    private val trainingPlanEnabled
        get() = FlavorUtils.isSuuntoAppChina

    inner class InternalSyncState(
        val serial: String,
        val capabilities: SuuntoWatchCapabilities,
        val errorsLogs: MutableList<String> = mutableListOf(),
        val triggerWatchSync: AtomicBoolean = AtomicBoolean(false),
        var remoteGuides: List<SuuntoPlusGuide> = emptyList(),
        var remoteTrainingPlans: List<TrainingPlan> = emptyList(),
        var remoteFeatures: List<SuuntoPlusFeature> = emptyList(),
        var localGuides: List<SuuntoPlusGuide> = emptyList(),
        var localTrainingPlans: List<TrainingPlan> = emptyList(),
        var localFeatures: List<SuuntoPlusFeature> = emptyList(),
        val guidesPendingDownload: MutableList<SuuntoPlusGuide> = mutableListOf(),
        val guidesFromPlanPendingDownload: MutableMap<TrainingPlan, List<SuuntoPlusGuideId>> = mutableMapOf(),
        val featuresPendingDownload: MutableList<SuuntoPlusFeature> = mutableListOf(),
    ) {
        val remoteGuideIds: List<SuuntoPlusGuideId>
            get() = remoteGuides.map { it.id }
        val localGuideIds: List<SuuntoPlusGuideId>
            get() = localGuides.map { it.id }
        val localGuidesById: Map<SuuntoPlusGuideId, SuuntoPlusGuide>
            get() = localGuides.associateBy { it.id }

        val remoteTrainingPlanIds: List<TrainingPlanId>
            get() = remoteTrainingPlans.map { it.id }
        val localTrainingPlansIds: List<TrainingPlanId>
            get() = localTrainingPlans.map { it.id }
        val localTrainingPlansById: Map<TrainingPlanId, TrainingPlan>
            get() = localTrainingPlans.associateBy { it.id }


        val remoteFeatureIds: List<String>
            get() = remoteFeatures.map { it.id }
        val localFeatureIds: List<String>
            get() = localFeatures.map { it.id }
        val localFeaturesById: Map<String, SuuntoPlusFeature>
            get() = localFeatures.associateBy { it.id }
    }

    suspend fun syncWithBackend(serial: String): SuuntoPlusGuideSyncLogicResult {
        val capabilities = capabilityStore.loadCapabilitiesWithAugmentedScreenSize(serial)
        return if (capabilities != null) {
            runSuspendCatching {
                try {
                    syncWithBackend(serial, capabilities)
                } finally {
                    withContext(NonCancellable) {
                        runSuspendCatching {
                            syncStateRepository.ensureIdle(serial)
                        }.onFailure { e ->
                            Timber.w(e, "Failed to set SuuntoPlus guide sync status to idle")
                        }
                    }
                }
            }.getOrElse { e ->
                Timber.w(e, "Failed to sync with backend.")
                processSyncErrorsAndStopSync(listOf(e.message.orEmpty()))
            }
        } else {
            processSyncErrorsAndStopSync(listOf("Unable to load capabilities for serial $serial"))
        }
    }

    private suspend fun processSyncErrorsAndStopSync(errorsLogs: List<String>) =
        processSyncErrorsAndStopSync(
            hasNewData = false,
            errorsLogs = errorsLogs,
            isWatchSync = false,
            triggerBackendSync = false,
            triggerWatchSync = false,
        )

    private suspend fun syncWithBackend(
        serial: String,
        capabilities: SuuntoWatchCapabilities,
    ): SuuntoPlusGuideSyncLogicResult = with(InternalSyncState(serial, capabilities)) {
        // Keep internal sync state using a helper class to avoid large amounts of local
        // variables and passing lots of arguments

        // Sync features if watch has 'feat_plugins_1' capability
        val syncFeatureSelection = capabilities.areSuuntoPlusFeaturesSupported

        // Sync guides if watch has both 'feat_plugins_1' and 'feat_guides_v1' capabilities
        val syncGuides = capabilities.areSuuntoPlusGuidesSupported

        if (!syncFeatureSelection && !syncGuides) {
            Timber.d("No watch support. Skip sync.")
            return SuuntoPlusGuideSyncLogicResult.NoNewData
        }

        runSuspendCatching {
            syncStateRepository.ensureSyncStart(serial, SuuntoPlusSyncState.REMOTE_SYNC_ONGOING)
            syncLogEventDao.logSyncStart(isWatchSync = false)

            // Delete locally soft deleted guides from remote
            deleteLocallySoftDeletedGuides()
            if (trainingPlanEnabled) {
                deleteLocallySoftDeletedPlans()
            }

            // Fetch data from remote and local database
            remoteGuides = if (syncGuides) remoteGuideDataSource.fetchAll() else emptyList()
            localGuides = localGuideDataSource.listAllSuuntoPlusGuides().first()

            remoteTrainingPlans = if (trainingPlanEnabled) trainingPlanRemoteDataSource.fetchAllTrainingPlan() else emptyList()
            localTrainingPlans = localTrainingPlanDataSource.listAllTrainingPlans().first()

            val variantName = runSuspendCatching {
                deviceConnectionStateUseCase.connectedWatchState()
                    .awaitFirstOrNull()?.deviceInfo?.variantName
            }.getOrNull().orEmpty()
            remoteFeatures = remoteFeaturesDataSource.fetchUserLibrary(variantName)
            localFeatures = localFeaturesDataSource.listSuuntoPlusFeatures().first()

            // Locally delete guides and features removed from backend
            locallyDeleteGuidesRemovedFromBackend()

            locallyDeletePlansRemovedFromBackend()

            locallyDeleteFeaturesRemovedFromBackend()

            // Insert new and update modified guides to database
            insertAndUpdateGuides()
            insertAndUpdatePlans()
            insertAndUpdateFeatures()

            // Fetch and update priorityIndex values
            updateGuidesPriorityIndices()
            updatePlansPriorityIndices()

            // Get guides from database to get up-to-date information and fetch ZAPP files if needed
            localGuides = localGuideDataSource.listAllSuuntoPlusGuides().first()
            localTrainingPlans = localTrainingPlanDataSource.listAllTrainingPlans().first()
            localFeatures = localFeaturesDataSource.listSuuntoPlusFeatures().first()

            // Set status to DOWNLOADED or DOWNLOADING depending on if guide exists in cache
            // Add to InternalSyncState::guidesPendingDownload as needed.
            if (syncGuides) {
                updateDownloadPendingStateForGuides()
                updateDownloadPendingStateForPlans()
            }

            if (syncFeatureSelection) {
                updateDownloadPendingStateForFeatures()
            }

            // Sort downloads to priority order
            sortGuideDownloads()
            sortPlanDownloads()

            // Download ZAPP files and set status to DOWNLOADED on success
            if (syncGuides) {
                downloadGuideZappFiles()
            }

            if (trainingPlanEnabled) {
                downloadPlanZappFiles()
            }

            if (syncFeatureSelection) {
                downloadFeatureZappFiles()
            }

            // Remove old files from cache
            deleteUnusedZappFilesFromCache()

            // Check if watch sync is required due to 'enabled' flag changes for features
            if (!triggerWatchSync.get()) {
                triggerWatchSyncIfEnabledFlagsChangedForFeatures()
            }

            // Check for out-of-date watch plug-ins
            if (!triggerWatchSync.get()) {
                triggerWatchSyncIfWatchPluginOutOfDate()
            }
        }.onFailure { e ->
            if (e is SuuntoPlusGuideSyncAlreadyRunningException) {
                Timber.d("Sync already ongoing. Returning failure without updating sync log")
                return SuuntoPlusGuideSyncLogicResult.Failure("Sync already ongoing")
            }

            val message = "SuuntoPlus guides remote sync failed"
            Timber.w(e, message)
            errorsLogs.add("$message. ${e::class.java.name}: ${e.message}")
        }

        withContext(NonCancellable) {
            runSuspendCatching {
                if (errorsLogs.isEmpty()) {
                    syncStateRepository.updateRemoteSyncCapabilities(serial, capabilities)
                }
                syncStateRepository.ensureIdle(serial)
            }.onFailure { e ->
                val message = "Failed to update sync state to IDLE"
                Timber.w(e, message)
                errorsLogs.add("$message. ${e::class.java.name}: ${e.message}")
            }
        }

        // processSyncErrorsAndStopSync uses a NonCancellable context internally so it is safe to
        // be called even when this coroutine has already been cancelled
        return processSyncErrorsAndStopSync(
            hasNewData = true,
            errorsLogs = errorsLogs,
            isWatchSync = false,
            triggerBackendSync = false,
            triggerWatchSync = triggerWatchSync.get()
        )
    }

    private suspend fun deleteLocallySoftDeletedGuides() {
        for (guide in localGuideDataSource.findDeleted()) {
            runSuspendCatching {
                // Delete from remote
                remoteGuideDataSource.delete(guide.id)

                // Delete from local database
                localGuideDataSource.deleteByIds(listOf(guide.id))
            }.onFailure {
                if (it is ClientError.NotFound) {
                    Timber.i(it, "Remote responded with 404 when trying to delete guide")
                    // Guide is already removed from remote, fully delete also locally. If this
                    // ever throws, then the sync will fail anyway so no need to catch here.
                    localGuideDataSource.deleteByIds(listOf(guide.id))
                } else {
                    // Deleting from remote failed for any other reason. Keep local guide
                    // so we can retry at next sync.
                    Timber.w(it, "Unable to delete soft-deleted guide from remote")
                }
            }
        }
    }

    private suspend fun deleteLocallySoftDeletedPlans() {
        for (plan in localTrainingPlanDataSource.findDeleted()) {
            runSuspendCatching {
                // Delete from remote
                trainingPlanRemoteDataSource.deleteMyPlan(plan.id.id)

                // Delete from local database
                localTrainingPlanDataSource.delete(listOf(plan))
            }.onFailure { e ->
                if (e is ClientError.NotFound) {
                    Timber.i(e, "Remote responded with 404 when trying to delete plan")
                    // Guide is already removed from remote, fully delete also locally. If this
                    // ever throws, then the sync will fail anyway so no need to catch here.
                    localTrainingPlanDataSource.delete(listOf(plan))
                } else {
                    // Deleting from remote failed for any other reason. Keep local guide
                    // so we can retry at next sync.
                    Timber.w(e, "Unable to delete soft-deleted plan from remote")
                }
            }
        }
    }


    private suspend fun InternalSyncState.locallyDeleteGuidesRemovedFromBackend() {
        val deletedGuideIds = localGuideIds - remoteGuideIds.toSet()
        if (deletedGuideIds.any { getPluginWatchStatus(it.id) == SuuntoPlusPluginStatus.IN_WATCH }) {
            // If any deleted guide had IN_WATCH status, make sure to trigger watch sync
            triggerWatchSync.set(true)
        }
        localGuideDataSource.deleteByIds(deletedGuideIds)
    }

    private suspend fun InternalSyncState.locallyDeletePlansRemovedFromBackend() {
        val deletedPlanIds = localTrainingPlansIds - remoteTrainingPlanIds.toSet()
        deletedPlanIds.forEach { planId ->
            val plan = localTrainingPlansById[planId] ?: return@forEach

            val anyInWatch = localTrainingPlanDataSource.anyGuideFromPlanInWatch(serial, plan)

            if (anyInWatch) {
                triggerWatchSync.set(true)
            }
        }

        localTrainingPlanDataSource.delete(deletedPlanIds.mapNotNull { localTrainingPlansById[it] })
    }

    private suspend fun InternalSyncState.locallyDeleteFeaturesRemovedFromBackend() {
        val deletedFeatureIds = localFeatureIds - remoteFeatureIds.toSet()
        if (deletedFeatureIds.any { getPluginWatchStatus(it) == SuuntoPlusPluginStatus.IN_WATCH }) {
            // If any deleted Feature had IN_WATCH status, make sure to trigger watch sync
            triggerWatchSync.set(true)
        }
        localFeaturesDataSource.deleteByIds(deletedFeatureIds)
    }

    private suspend fun InternalSyncState.insertAndUpdateGuides() {
        for (remoteGuide in remoteGuides) {
            val localGuide = localGuidesById[remoteGuide.id]
            val isNewGuide = localGuide == null
            val isUpdated = localGuide != null && localGuide.modifiedMillis < remoteGuide.modifiedMillis

            if (isNewGuide || isUpdated) {
                // Keep local pinned status when updating. The remote listing API does not provide
                // priorityIndex values so keep old values.
                localGuideDataSource.upsert(
                    remoteGuide.copy(
                        pinned = localGuide?.pinned ?: remoteGuide.pinned,
                        priorityIndex = localGuide?.priorityIndex,
                    )
                )

                if (isUpdated) {
                    val watchStatus = pluginStatusDataSource.getWatchStatus(
                        watchSerial = serial,
                        pluginId = remoteGuide.pluginId,
                    )

                    // If a guide is updated on the backend and it has IN_WATCH status,
                    // reset status to UNKNOWN. This will be updated to DOWNLOADING
                    // and hopefully DOWNLOADED while the remote sync proceeds.
                    if (watchStatus == SuuntoPlusPluginStatus.IN_WATCH) {
                        pluginStatusDataSource.updateWatchStatus(
                            watchSerial = serial,
                            pluginId = remoteGuide.id.id,
                            status = SuuntoPlusPluginStatus.UNKNOWN,
                        )
                    }
                }

                triggerWatchSync.set(true)
            } else if (localGuide != null && localGuide != remoteGuide) {
                // Update guide metadata if changed even when modifiedMillis has not changed
                localGuideDataSource.upsert(
                    remoteGuide.copy(
                        pinned = localGuide.pinned,
                        priorityIndex = localGuide.priorityIndex
                    )
                )
            }

            // Push pinned flag to remote
            if (localGuide != null && localGuide.pinned != remoteGuide.pinned) {
                runSuspendCatching {
                    remoteGuideDataSource.updatePinnedStatus(localGuide.id, localGuide.pinned)
                }.onFailure { e ->
                    Timber.w(e, "Failed to update pinned flag to remote")
                    // Continue with sync even when updating pinned flag fails
                }
            }
        }
    }

    private suspend fun InternalSyncState.insertAndUpdatePlans() {
        for (remotePlan in remoteTrainingPlans) {
            val localPlan = localTrainingPlansById[remotePlan.id]
            val isNewPlan = localPlan == null
            val isUpdated = localPlan != null && localPlan.modifiedMillis < remotePlan.modifiedMillis

            if (isNewPlan || isUpdated) {
                // Keep local pinned status when updating. The remote listing API does not provide
                // priorityIndex values so keep old values.
                localTrainingPlanDataSource.upsert(
                    remotePlan.copy(
                        pinned = localPlan?.pinned ?: remotePlan.pinned,
                        priorityIndex = localPlan?.priorityIndex,
                    )
                )

                if (isUpdated) {
                    remotePlan.courses.orEmpty().forEach { (_, guideIds) ->
                        guideIds.forEach { guideId ->
                            val watchStatus = pluginStatusDataSource.getWatchStatus(
                                watchSerial = serial,
                                pluginId = guideId,
                            )

                            // If a guide is updated on the backend and it has IN_WATCH status,
                            // reset status to UNKNOWN. This will be updated to DOWNLOADING
                            // and hopefully DOWNLOADED while the remote sync proceeds.
                            if (watchStatus == SuuntoPlusPluginStatus.IN_WATCH) {
                                pluginStatusDataSource.updateWatchStatus(
                                    watchSerial = serial,
                                    pluginId = guideId,
                                    status = SuuntoPlusPluginStatus.UNKNOWN,
                                )
                            }
                        }
                    }

                }

                triggerWatchSync.set(true)
            } else if (localPlan != null && localPlan != remotePlan) {
                // Update guide metadata if changed even when modifiedMillis has not changed
                localTrainingPlanDataSource.upsert(
                    remotePlan.copy(
                        pinned = localPlan.pinned,
                        priorityIndex = localPlan.priorityIndex
                    )
                )
            }

            // Push pinned flag to remote
            if (localPlan != null && localPlan.pinned != remotePlan.pinned && trainingPlanEnabled) {
                runSuspendCatching {
                    trainingPlanRemoteDataSource.updatePinnedStatus(localPlan.id, localPlan.pinned)
                }.onFailure { e ->
                    Timber.w(e, "Failed to update pinned flag to remote")
                    // Continue with sync even when updating pinned flag fails
                }
            }
        }
    }

    private suspend fun InternalSyncState.insertAndUpdateFeatures() {
        for (remoteFeature in remoteFeatures) {
            val localFeature = localFeaturesById[remoteFeature.id]
            val metadataUpdated = localFeature == null || localFeature != remoteFeature
            val pluginUpdated =
                localFeature == null || localFeature.modifiedMillis < remoteFeature.modifiedMillis ||
                    localFeature.pluginId != remoteFeature.pluginId

            if (metadataUpdated) {
                // Keep local enabled status when updating
                localFeaturesDataSource.upsert(
                    remoteFeature.copy(
                        enabled = localFeature?.enabled ?: remoteFeature.enabled
                    )
                )

                if (pluginUpdated) {
                    val pluginId = remoteFeature.pluginId ?: remoteFeature.id
                    val watchStatus = pluginStatusDataSource.getWatchStatus(
                        watchSerial = serial,
                        pluginId = pluginId
                    )

                    // If a feature is updated on the backend and it has IN_WATCH status,
                    // reset status to UNKNOWN. This will be updated to DOWNLOADING
                    // and hopefully DOWNLOADED while the remote sync proceeds.
                    if (watchStatus == SuuntoPlusPluginStatus.IN_WATCH) {
                        pluginStatusDataSource.updateWatchStatus(
                            watchSerial = serial,
                            pluginId = pluginId,
                            modifiedMillis = remoteFeature.modifiedMillis,
                            capabilities = capabilities,
                            status = SuuntoPlusPluginStatus.UNKNOWN,
                            interestValue = null,
                            type = SuuntoPlusPluginType.FEATURE,
                        )
                    }

                    triggerWatchSync.set(true)
                }
            }

            // Push enabled flag to remote
            if (localFeature != null && localFeature.enabled != remoteFeature.enabled) {
                runSuspendCatching {
                    remoteFeaturesDataSource.updateEnabledState(localFeature.id, localFeature.enabled)
                }.onFailure { e ->
                    Timber.w(e, "Failed to update enabled flag to remote")
                    // Continue with sync even when updating enabled flag fails
                }
            }
        }
    }

    private suspend fun InternalSyncState.updateDownloadPendingStateForGuides() {
        for (guide in localGuides) {
            val watchStatus = pluginStatusDataSource.getWatchStatus(
                watchSerial = serial,
                pluginId = guide.id.id,
            )

            val previousCaps = getPreviousCapsIfNotSupported(watchStatus, guide.id.id)

            if (zappStorage.existsInCache(guide.id.id, guide.modifiedMillis, capabilities)) {
                if (watchStatus == null || watchStatus == SuuntoPlusPluginStatus.UNKNOWN || watchStatus == SuuntoPlusPluginStatus.DOWNLOADING) {
                    pluginStatusDataSource.updateWatchStatus(
                        watchSerial = serial,
                        pluginId = guide.pluginId,
                        modifiedMillis = guide.modifiedMillis,
                        capabilities = capabilities,
                        status = SuuntoPlusPluginStatus.DOWNLOADED,
                        interestValue = null,
                        type = SuuntoPlusPluginType.GUIDE
                    )
                }

                updatePluginFileSizeIfMissing(guide.id.id, guide.modifiedMillis)
            } else {
                // If guide is NOT_SUPPORTED and capabilities have not changed, do not try to download
                // again. In other cases, set state to DOWNLOADING and add to the list of pending downloads.
                if (watchStatus != SuuntoPlusPluginStatus.NOT_SUPPORTED || previousCaps != capabilities) {
                    // If the status is IN_WATCH but Zapp file is missing, the user either just
                    // paired the watch or cleared the app cache. In this case keep the state as
                    // IN_WATCH also while downloading the Zapp file in the background.
                    if (watchStatus != SuuntoPlusPluginStatus.IN_WATCH) {
                        pluginStatusDataSource.updateWatchStatus(
                            watchSerial = serial,
                            pluginId = guide.pluginId,
                            modifiedMillis = guide.modifiedMillis,
                            capabilities = capabilities,
                            status = SuuntoPlusPluginStatus.DOWNLOADING,
                            interestValue = null,
                            type = SuuntoPlusPluginType.GUIDE,
                        )
                    }

                    guidesPendingDownload.add(guide)
                }
            }
        }
    }

    private suspend fun InternalSyncState.updateDownloadPendingStateForPlans() {
        localTrainingPlans.forEach { plan ->
            plan.courses.orEmpty().forEach { (_, guideIds) ->
                guideIds.forEach { guideId ->
                    val watchStatus = pluginStatusDataSource.getWatchStatus(
                        watchSerial = serial,
                        pluginId = guideId,
                    )

                    val previousCaps = getPreviousCapsIfNotSupported(watchStatus, guideId)

                    if (zappStorage.existsInCache(guideId, plan.modifiedMillis, capabilities)) {
                        if (watchStatus == null || watchStatus == SuuntoPlusPluginStatus.UNKNOWN || watchStatus == SuuntoPlusPluginStatus.DOWNLOADING) {
                            pluginStatusDataSource.updateWatchStatus(
                                watchSerial = serial,
                                pluginId = guideId,
                                modifiedMillis = plan.modifiedMillis,
                                capabilities = capabilities,
                                status = SuuntoPlusPluginStatus.DOWNLOADED,
                                interestValue = null,
                                type = SuuntoPlusPluginType.GUIDE
                            )
                        }

                        updatePlanPluginFileSizeIfMissing(plan)
                    } else {
                        // If guide is NOT_SUPPORTED and capabilities have not changed, do not try to download
                        // again. In other cases, set state to DOWNLOADING and add to the list of pending downloads.
                        if (watchStatus != SuuntoPlusPluginStatus.NOT_SUPPORTED || previousCaps != capabilities) {
                            // If the status is IN_WATCH but Zapp file is missing, the user either just
                            // paired the watch or cleared the app cache. In this case keep the state as
                            // IN_WATCH also while downloading the Zapp file in the background.
                            if (watchStatus != SuuntoPlusPluginStatus.IN_WATCH) {
                                pluginStatusDataSource.updateWatchStatus(
                                    watchSerial = serial,
                                    pluginId = guideId,
                                    modifiedMillis = plan.modifiedMillis,
                                    capabilities = capabilities,
                                    status = SuuntoPlusPluginStatus.DOWNLOADING,
                                    interestValue = null,
                                    type = SuuntoPlusPluginType.GUIDE,
                                )
                            }

                            guidesFromPlanPendingDownload.compute(plan) { _, existingList ->
                                (existingList ?: emptyList()) + SuuntoPlusGuideId(guideId)
                            }
                        }
                    }
                }
            }
        }
    }

    private suspend fun InternalSyncState.updateDownloadPendingStateForFeatures() {
        for (feature in localFeatures) {
            val pluginId = feature.pluginId ?: continue
            val watchStatus = pluginStatusDataSource.getWatchStatus(
                watchSerial = serial,
                pluginId = pluginId,
            )

            val previousCaps = getPreviousCapsIfNotSupported(watchStatus, pluginId)

            if (zappStorage.existsInCache(pluginId, feature.modifiedMillis, capabilities)) {
                if (watchStatus == null || watchStatus == SuuntoPlusPluginStatus.UNKNOWN || watchStatus == SuuntoPlusPluginStatus.DOWNLOADING) {
                    pluginStatusDataSource.updateWatchStatus(
                        watchSerial = serial,
                        pluginId = pluginId,
                        modifiedMillis = feature.modifiedMillis,
                        capabilities = capabilities,
                        status = SuuntoPlusPluginStatus.DOWNLOADED,
                        interestValue = null,
                        type = SuuntoPlusPluginType.FEATURE,
                    )
                }

                if (feature.manifestJson == null) {
                    // Try to parse manifest JSON from locally cached ZAPP file
                    Timber.d("Feature ${feature.id} is missing manifest JSON from DB but has cached ZAPP")
                    runSuspendCatching {
                        val path = zappStorage.getAbsolutePath(pluginId, feature.modifiedMillis, capabilities, SuuntoPlusPluginType.FEATURE)
                        val data = File(path).inputStream().buffered().use { it.readBytes() }
                        parseAndStoreManifest(data, feature)
                    }.onFailure {
                        Timber.w(it, "Failed to parse manifest from cached ZAPP file")
                    }
                }

                updatePluginFileSizeIfMissing(pluginId, feature.modifiedMillis)
            } else if (feature.hasExpired(clock.millis())) {
                // Set expired features as NOT_SUPPORTED and don't try to download Zapp files
                pluginStatusDataSource.updateWatchStatus(
                    watchSerial = serial,
                    pluginId = pluginId,
                    modifiedMillis = feature.modifiedMillis,
                    capabilities = capabilities,
                    status = SuuntoPlusPluginStatus.NOT_SUPPORTED,
                    interestValue = null,
                    type = SuuntoPlusPluginType.FEATURE
                )
            } else if (watchStatus != SuuntoPlusPluginStatus.NOT_SUPPORTED || capabilities != previousCaps) {
                // If feature is NOT_SUPPORTED and capabilities have not changed, do not try to download
                // again. In other cases, set state to DOWNLOADING and add to the list of pending downloads.
                pluginStatusDataSource.updateWatchStatus(
                    watchSerial = serial,
                    pluginId = pluginId,
                    modifiedMillis = feature.modifiedMillis,
                    capabilities = capabilities,
                    status = SuuntoPlusPluginStatus.DOWNLOADING,
                    interestValue = null,
                    type = SuuntoPlusPluginType.FEATURE,
                )

                featuresPendingDownload.add(feature)
            }
        }
    }

    private suspend fun InternalSyncState.parseAndStoreManifest(
        data: ByteArray,
        feature: SuuntoPlusFeature
    ) {
        val manifest = SuuntoPlusFeatureManifestExtractor.extractManifestJson(data)
        if (manifest != null) {
            localFeaturesDataSource.updateManifest(feature.id, manifest)

            if (feature.pluginId != null) {
                val dataJson = SuuntoPlusFeatureManifestExtractor.extractDataJson(data)
                if (dataJson != null && settingsStateDataSource.findDataJson(serial, feature.pluginId) == null) {
                    // Store initial data.jsn from the Zapp file to settings state table. This will
                    // provide the default to show when opening the sports app details view for the
                    // first time. The data will be updated when settings are updated by the sports
                    // app on the watch or by the user using the settings UI in the app.
                    settingsStateDataSource.setDataJson(serial, feature.pluginId, dataJson)
                }
            }
        } else {
            Timber.w("Manifest extractor returned null for ${feature.id}")
        }
    }

    private suspend fun InternalSyncState.getPreviousCapsIfNotSupported(
        watchStatus: SuuntoPlusPluginStatus?,
        pluginId: String,
    ): SuuntoWatchCapabilities? =
        if (watchStatus == SuuntoPlusPluginStatus.NOT_SUPPORTED) {
            pluginStatusDataSource.getCapabilitiesFromWatchState(
                watchSerial = serial,
                pluginId = pluginId,
            )
        } else {
            null
        }

    private suspend fun InternalSyncState.updatePlanPluginFileSizeIfMissing(plan: TrainingPlan) {
        plan.courses?.forEach { (_, guideIds) ->
            guideIds.forEach { guideId ->
                updatePluginFileSizeIfMissing(guideId, plan.modifiedMillis)
            }
        }
    }

    private suspend fun InternalSyncState.updatePluginFileSizeIfMissing(
        pluginId: String,
        modifiedMillis: Long
    ) {
        val hasFileSize = pluginStatusDataSource.getFileSize(serial, pluginId) != null
        if (!hasFileSize) {
            // Update file size for plug-ins where the Zapp file is in cache if missing
            // from database. Typically this should only happen when migrating from an
            // older app version.
            runCatching {
                zappStorage.getFileSize(pluginId, modifiedMillis, capabilities)
            }.onSuccess {
                pluginStatusDataSource.updateFileSize(serial, pluginId, it)
            }
        }
    }

    private suspend fun updateGuidesPriorityIndices() {
        runSuspendCatching {
            remoteGuideDataSource.fetchPriorityIndices().forEach { (guideId, index) ->
                localGuideDataSource.findById(guideId)?.let { localGuide ->
                    localGuideDataSource.upsert(
                        localGuide.copy(priorityIndex = index)
                    )
                }
            }
        }.onFailure { e ->
            Timber.w(e, "Failed to update guide priority indices")
        }
    }

    private suspend fun updatePlansPriorityIndices() {
        runSuspendCatching {
            remoteGuideDataSource.fetchPriorityIndices().forEach { (guideId, index) -> // same with guide priority http points
                localTrainingPlanDataSource.findById(TrainingPlanId(guideId.id))?.let { localPlan ->
                    localTrainingPlanDataSource.upsert(
                        localPlan.copy(priorityIndex = index)
                    )
                }
            }
        }.onFailure { e ->
            Timber.w(e, "Failed to update guide priority indices")
        }
    }


    private fun InternalSyncState.sortGuideDownloads() {
        guidesPendingDownload.sortBy {
            it.priorityIndex
        }
    }

    private fun InternalSyncState.sortPlanDownloads() {
        guidesFromPlanPendingDownload.entries.sortedBy {
            it.key.priorityIndex
        }
    }

    private suspend fun InternalSyncState.downloadPlanZappFiles() {
        guidesFromPlanPendingDownload.forEach { (plan, guideIds) ->
            guideIds.forEach { guideId ->
                runSuspendCatching {
                    Timber.d("Downloading ZAPP plug-in for planId: ${plan.pluginId}, guideId: ${guideId}, capabilities=$capabilities")
                    val data = trainingPlanRemoteDataSource.fetchZAPPFile(plan.pluginId, guideId.id, capabilities)
                    zappStorage.store(guideId.id, plan.modifiedMillis, capabilities, data)
                    val watchStatus = pluginStatusDataSource.getWatchStatus(
                        watchSerial = serial,
                        pluginId = guideId.id,
                    )

                    // If the status is IN_WATCH already, the user either just paired the watch or
                    // cleared the app cache. In this case keep the state as IN_WATCH.
                    if (watchStatus != SuuntoPlusPluginStatus.IN_WATCH) {
                        pluginStatusDataSource.updateWatchStatus(
                            watchSerial = serial,
                            pluginId = guideId.id,
                            modifiedMillis = plan.modifiedMillis,
                            capabilities = capabilities,
                            status = SuuntoPlusPluginStatus.DOWNLOADED,
                            interestValue = null,
                            type = SuuntoPlusPluginType.GUIDE,
                            fileSize = data.size
                        )
                    }
                    triggerWatchSync.set(true)
                }.onFailure { e ->
                    handleZappDownloadError(
                        exception = e,
                        pluginType = SuuntoPlusPluginType.GUIDE,
                        pluginId = guideId.id,
                    )
                }
            }
        }
    }

    private suspend fun InternalSyncState.downloadGuideZappFiles() {
        for (guide in guidesPendingDownload) {
            runSuspendCatching {
                Timber.d("Downloading ZAPP plug-in for guide ${guide.id}, capabilities=$capabilities")
                val data = remoteGuideDataSource.fetchZAPPFile(guide.id, capabilities)
                zappStorage.store(guide.pluginId, guide.modifiedMillis, capabilities, data)

                val watchStatus = pluginStatusDataSource.getWatchStatus(
                    watchSerial = serial,
                    pluginId = guide.pluginId,
                )

                // If the status is IN_WATCH already, the user either just paired the watch or
                // cleared the app cache. In this case keep the state as IN_WATCH.
                if (watchStatus != SuuntoPlusPluginStatus.IN_WATCH) {
                    pluginStatusDataSource.updateWatchStatus(
                        watchSerial = serial,
                        pluginId = guide.id.id,
                        modifiedMillis = guide.modifiedMillis,
                        capabilities = capabilities,
                        status = SuuntoPlusPluginStatus.DOWNLOADED,
                        interestValue = null,
                        type = SuuntoPlusPluginType.GUIDE,
                        fileSize = data.size
                    )
                }
                triggerWatchSync.set(true)
            }.onFailure { e ->
                handleZappDownloadError(
                    exception = e,
                    pluginType = SuuntoPlusPluginType.GUIDE,
                    pluginId = guide.id.id,
                )
            }
        }
    }

    private suspend fun InternalSyncState.downloadFeatureZappFiles() {
        for (feature in featuresPendingDownload) {
            if (feature.pluginId == null) {
                continue
            }

            runSuspendCatching {
                Timber.d("Downloading ZAPP plug-in for feature=${feature.id}, plug-in=${feature.pluginId}, capabilities=$capabilities")
                // The Zapp file is fetched using the feature ID. It is stored to cache using
                // plug-in ID as the key. Watch status table also uses plug-in IDs.
                val data = remoteFeaturesDataSource.fetchZAPPFile(feature.id, capabilities)
                zappStorage.store(feature.pluginId, feature.modifiedMillis, capabilities, data)
                pluginStatusDataSource.updateWatchStatus(
                    watchSerial = serial,
                    pluginId = feature.pluginId,
                    modifiedMillis = feature.modifiedMillis,
                    capabilities = capabilities,
                    status = SuuntoPlusPluginStatus.DOWNLOADED,
                    interestValue = null,
                    fileSize = data.size,
                    type = SuuntoPlusPluginType.FEATURE,
                )

                runSuspendCatching {
                    parseAndStoreManifest(data, feature)
                }.onFailure {
                    Timber.w(it, "Failed to parse and update manifest JSON")
                }

                triggerWatchSync.set(true)
            }.onFailure { e ->
                handleZappDownloadError(
                    exception = e,
                    pluginType = SuuntoPlusPluginType.FEATURE,
                    pluginId = feature.pluginId,
                )
            }
        }
    }

    private suspend fun InternalSyncState.handleZappDownloadError(
        exception: Throwable,
        pluginType: SuuntoPlusPluginType,
        pluginId: String,
    ) {
        val notSupported = exception is WatchPluginNotSupportedByCapabilities
        if (notSupported) {
            Timber.d("Failed to download ZAPP plug-in for $pluginType $pluginId: Not supported by capabilities")
        } else {
            Timber.w(exception, "Failed to download ZAPP plug-in for $pluginType $pluginId")
        }

        pluginStatusDataSource.updateWatchStatus(
            watchSerial = serial,
            pluginId = pluginId,
            status = if (notSupported) SuuntoPlusPluginStatus.NOT_SUPPORTED else SuuntoPlusPluginStatus.UNKNOWN,
        )
    }

    private suspend fun InternalSyncState.deleteUnusedZappFilesFromCache() {
        val trainingPlanMap = localTrainingPlans
            .flatMap { plan ->
                plan.courses.orEmpty().flatMap { (_, guides) ->
                    guides.map { guideId ->
                        guideId to plan.modifiedMillis
                    }
                }
            }.toMap()

        val usedPlugins = localGuides.associate { it.pluginId to it.modifiedMillis } +
            localFeatures.associate { (it.pluginId ?: it.id) to it.modifiedMillis } + trainingPlanMap

        zappStorage.deleteUnusedFiles(
            usedPluginIdsToTimestamp = usedPlugins,
            allCapabilities = capabilityStore.loadAllWithAugmentedScreenSizeCapability()
                .map { it.capabilities }
        )
    }

    private suspend fun InternalSyncState.triggerWatchSyncIfEnabledFlagsChangedForFeatures() {
        suspend fun SuuntoPlusFeature.getWatchStatus(): SuuntoPlusPluginStatus? =
            pluginId?.let { getPluginWatchStatus(it) }

        if (localFeatures.any { it.enabled && it.getWatchStatus() == SuuntoPlusPluginStatus.DOWNLOADED }) {
            // Enabled feature missing from watch -> need to run watch sync
            triggerWatchSync.set(true)
        }

        if (localFeatures.any { !it.enabled && it.getWatchStatus() == SuuntoPlusPluginStatus.IN_WATCH }) {
            // Disabled feature exists on watch -> need to run watch sync
            triggerWatchSync.set(true)
        }
    }

    private suspend fun InternalSyncState.triggerWatchSyncIfWatchPluginOutOfDate() {
        val modificationTimesInSecondsByPluginId = localFeatures
            .filter { it.pluginId != null }
            .associate { it.pluginId!! to it.modifiedSeconds }
            .plus(localGuides.associate { it.pluginId to it.modifiedSeconds })

        for ((pluginId, remoteModifiedSeconds) in modificationTimesInSecondsByPluginId) {
            val watchState = getPluginWatchStatus(pluginId)
            if (watchState == SuuntoPlusPluginStatus.IN_WATCH) {
                val watchModifiedSeconds = pluginStatusDataSource.getModificationTime(serial, pluginId)?.div(1000L) ?: continue
                if (watchModifiedSeconds < remoteModifiedSeconds) {
                    Timber.d("Plug-in $pluginId is out of date ($watchModifiedSeconds < $remoteModifiedSeconds)")
                    triggerWatchSync.set(true)
                    break
                }
            }
        }
    }

    private suspend fun InternalSyncState.getPluginWatchStatus(pluginId: String) =
        pluginStatusDataSource.getWatchStatus(watchSerial = serial, pluginId = pluginId)
}

private val SuuntoPlusFeature.modifiedSeconds: Long
    get() = modifiedMillis / 1000L

private val SuuntoPlusGuide.modifiedSeconds: Long
    get() = modifiedMillis / 1000L
