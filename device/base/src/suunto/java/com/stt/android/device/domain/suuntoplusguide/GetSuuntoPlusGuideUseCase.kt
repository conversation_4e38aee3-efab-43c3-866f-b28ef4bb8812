package com.stt.android.device.domain.suuntoplusguide

import com.stt.android.device.datasource.WatchSerialDataSource
import com.stt.android.device.datasource.suuntoplusguide.SuuntoPlusGuidesLocalDataSource
import com.stt.android.device.datasource.suuntoplusguide.SuuntoWatchCapabilityStore
import com.stt.android.domain.suuntoplus.SuuntoPlusStoreSingleGuideInterface
import com.stt.android.domain.suuntoplus.TrainingExtendedData
import com.suunto.connectivity.capabilities.SuuntoWatchCapabilities
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

class GetSuuntoPlusGuideUseCase
@Inject constructor(
    private val suuntoPlusGuidesLocalDataSource: SuuntoPlusGuidesLocalDataSource,
    private val watchSerialDataSource: WatchSerialDataSource,
    private val capabilityStore: SuuntoWatchCapabilityStore,
    private val guideInterface: SuuntoPlusStoreSingleGuideInterface,
) {
    fun getGuideAndWatchStatusById(id: SuuntoPlusGuideId): Flow<GuideAndWatchStatus?> {
        val serial = watchSerialDataSource.getCurrentWatchSerial()
            ?: return flow { throw IllegalStateException("Cannot get guide: Missing watch serial") }

        val capabilityFlow: Flow<SuuntoWatchCapabilities?> =
            flow { emit(capabilityStore.loadCapabilitiesWithAugmentedScreenSize(serial)) }

        return capabilityFlow.flatMapLatest { capabilities ->
            if (capabilities == null || !capabilities.areSuuntoPlusGuidesSupported) {
                // SuuntoPlus™ guides not supported by watch. Return NOT_SUPPORTED for all guides.
                suuntoPlusGuidesLocalDataSource.findByIdAsFlow(id)
                    .map { guide ->
                        GuideAndWatchStatus(
                            guide = guide,
                            watchStatus = SuuntoPlusPluginStatus.NOT_SUPPORTED
                        )
                    }
            } else {
                suuntoPlusGuidesLocalDataSource.findSuuntoPlusGuideWithWatchStatus(serial, id)
                    .map {
                        GuideAndWatchStatus(
                            guide = it.first,
                            watchStatus = it.second ?: SuuntoPlusPluginStatus.UNKNOWN
                        )
                    }
            }
        }
    }

    suspend fun fetchSuuntoPlusGuide(
        suuntoPlusStoreGuideId: String,
        watchCapabilities: SuuntoWatchCapabilities
    ): Pair<String?, TrainingExtendedData?> =
        guideInterface.fetchSuuntoPlusGuideFromRemote(suuntoPlusStoreGuideId, watchCapabilities)
}

data class GuideAndWatchStatus(
    val guide: SuuntoPlusGuide?,
    val watchStatus: SuuntoPlusPluginStatus
)
