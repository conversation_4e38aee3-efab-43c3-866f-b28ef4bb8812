package com.stt.android.home

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.SuuntoSharedPrefs
import com.stt.android.di.TooltipPreferences
import com.stt.android.domain.android.DeviceFeatureStates
import com.stt.android.domain.marketingconsent.AcceptMarketingConsentParams
import com.stt.android.domain.marketingconsent.AcceptMarketingConsentUseCase
import com.stt.android.domain.session.FetchSessionStatusUseCase
import com.stt.android.domain.session.ResetPasswordUseCase
import com.stt.android.domain.session.SaveAndGetSessionStatusUseCase
import com.stt.android.domain.sportmodes.DownloadSportModeComponentUseCase
import com.stt.android.maps.MAP_TYPE_FINLAND_MAANMITTAUSLAITOS_TERRAIN
import com.stt.android.maps.SetDefaultMapProviderUseCase
import com.stt.android.models.MapSelectionModel
import com.stt.android.refreshable.Refreshables
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.BatteryOptimizationUtils
import com.stt.android.utils.STTConstants
import com.stt.android.utils.awaitFirstNonNull
import com.stt.android.watch.DeviceHolderViewModel
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.Spartan
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import com.suunto.connectivity.watch.WatchState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.Clock
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject internal constructor(
    coroutinesDispatchers: CoroutinesDispatchers,
    batteryOptimizationUtils: BatteryOptimizationUtils,
    downloadSportModeComponentUseCase: DownloadSportModeComponentUseCase,
    fetchSessionStatusUseCase: FetchSessionStatusUseCase,
    resetPasswordUseCase: ResetPasswordUseCase,
    saveAndGetSessionStatusUseCase: SaveAndGetSessionStatusUseCase,
    private val sharedPreferences: SharedPreferences,
    clock: Clock,
    refreshables: Refreshables,
    private val acceptMarketingConsentUseCase: AcceptMarketingConsentUseCase,
    private val smoothPairingHelper: SmoothPairingHelper,
    private val suuntoWatchModel: SuuntoWatchModel,
    @TooltipPreferences private val tooltipPreferences: SharedPreferences,
    private val mapSelectionModel: MapSelectionModel,
    private val setDefaultMapProviderUseCase: SetDefaultMapProviderUseCase,
    workoutHeaderController: WorkoutHeaderController,
    featureStates: DeviceFeatureStates,
    @SuuntoSharedPrefs private val suuntoSharedPrefs: SharedPreferences,
    private val shouldRequestBackgroundLocationPermissionForWeatherUseCase: ShouldRequestBackgroundLocationPermissionForWeatherUseCase,
) : BaseHomeViewModel(
    fetchSessionStatusUseCase,
    resetPasswordUseCase,
    saveAndGetSessionStatusUseCase,
    sharedPreferences,
    clock,
    refreshables,
    workoutHeaderController,
    coroutinesDispatchers,
    featureStates
) {
    private val checkForBatteryOptimization = MutableLiveData<Boolean>()
    private val _currentWatchAndState = MutableLiveData<Pair<Spartan, WatchState>>()
    private val _showPostPairingTooltip = SingleLiveEvent<SuuntoDeviceType>()

    /**
     * Returns a LiveData instance that emits true when the battery optimization
     * whitelist check needs to run or false otherwise
     */
    fun checkForBatteryOptimizationLiveData(): LiveData<Boolean> {
        return checkForBatteryOptimization
    }

    val currentWatchAndState: LiveData<Pair<Spartan, WatchState>>
        get() = _currentWatchAndState

    val deviceFoundForSmoothPairing: LiveData<Any>
        get() = smoothPairingHelper.deviceFoundForSmoothPairing

    val showPostPairingTooltip: LiveData<SuuntoDeviceType>
        get() = _showPostPairingTooltip

    private val _backgroundLocationPermissionForConnectedGpsNeeded = MutableStateFlow(false)
    val backgroundLocationPermissionForConnectedGpsNeeded: StateFlow<Boolean> =
        _backgroundLocationPermissionForConnectedGpsNeeded.asStateFlow()

    private val _backgroundLocationPermissionForWeatherNeeded = MutableStateFlow(false)
    val backgroundLocationPermissionForWeatherNeeded: StateFlow<Boolean> =
        _backgroundLocationPermissionForWeatherNeeded.asStateFlow()

    private val _needReloadDashboard = SingleLiveEvent<Unit>()
    val needReloadDashboard: LiveData<Unit> = _needReloadDashboard

    private val preferencesChangeListener =
        SharedPreferences.OnSharedPreferenceChangeListener { _, key ->
            if (key == "map_provider") {
                _needReloadDashboard.call()
            }
        }

    init {
        setDefaultMapsProvider()

        launch {
            runSuspendCatching {
                // Check if the user has a paired device
                val currentWatchAndState = withContext(io) {
                    val currentWatch = suuntoWatchModel.currentWatch.await()
                    val watchState = currentWatch.stateChangeObservable.awaitFirstNonNull()
                    currentWatch to watchState
                }

                // The user has paired a watch, so check if battery optimisation whitelisting
                // should be requested from user. First check whether the battery optimisation dialog has been
                // shown already.
                if (!batteryOptimizationUtils.batteryOptimisationDialogHasBeenShown()) {
                    checkForBatteryOptimization.postValue(batteryOptimizationUtils.requestUserToWhitelistApp())
                }
                _currentWatchAndState.postValue(currentWatchAndState)
            }.onFailure {
                if (it !is MissingCurrentWatchException) {
                    Timber.w(it, "Error during get current watch state")
                }
            }
        }

        // Download sport mode component silently on the launch of the app if possible
        launch {
            runSuspendCatching {
                downloadSportModeComponentUseCase.downloadSportModeComponentIfNeeded()
                Timber.d("Sport Mode component is successfully downloaded/updated")
            }.onFailure {
                if (it !is MissingCurrentWatchException) {
                    Timber.w(it)
                }
            }
        }

        sharedPreferences.registerOnSharedPreferenceChangeListener(preferencesChangeListener)
    }

    fun initiateSmoothPairingIfNeeded() {
        smoothPairingHelper.initiateSmoothPairingIfNeeded()
    }

    fun stopSmoothPairingScanning() {
        smoothPairingHelper.stopSmoothPairingScanning()
    }

    suspend fun initiateBackgroundLocationRequestIfNeeded(
        context: Context,
    ) {
        runSuspendCatching {
            val watchState = withContext(io) {
                suuntoWatchModel.currentWatch()
                    .stateChangeObservable
                    .awaitFirstNonNull()
            }

            _backgroundLocationPermissionForConnectedGpsNeeded.value = Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q &&
                watchState.isConnectedGpsInUse &&
                DeviceHolderViewModel.shouldRequestBackgroundLocationPermissionOnConnectedGps(
                    context = context,
                    suuntoPreferences = suuntoSharedPrefs
                )

            _backgroundLocationPermissionForWeatherNeeded.value = shouldRequestBackgroundLocationPermissionForWeatherUseCase()
        }.onFailure {
            if (it !is MissingCurrentWatchException) {
                Timber.w(it, "Error in initiateBackgroundLocationRequestIfNeeded")
            }
        }
    }

    suspend fun checkPostPairingTooltip() {
        val shouldShow = tooltipPreferences.getBoolean(
            STTConstants.TooltipPreferences.KEY_SUUNTO_SHOULD_SHOW_POST_PAIRING_TOOLTIP,
            false
        )

        if (shouldShow) {
            runSuspendCatching {
                val currentWatch = withContext(io) {
                    suuntoWatchModel.currentWatch.await()
                }
                delay(POST_PAIRING_TOOL_TIP_DELAY_MILLIS)
                _showPostPairingTooltip.postValue(currentWatch.suuntoBtDevice.deviceType)
            }.onFailure {
                if (it !is MissingCurrentWatchException) {
                    Timber.w(it)
                }
            }
        }
    }

    fun acceptMarketingConsent() {
        launch {
            runSuspendCatching {
                acceptMarketingConsentUseCase(AcceptMarketingConsentParams(true, ""))
            }.onFailure {
                Timber.w(it, "Failed to acceptMarketingConsent")
            }
        }
    }

    private fun setDefaultMapsProvider() {
        runCatching {
            setDefaultMapProviderUseCase.setDefaultMapProviderForChineseAppstoreReviewer()
        }.onFailure {
            Timber.w(it, "setDefaultMapsProvider fail.")
        }
    }

    override fun onCleared() {
        super.onCleared()
        smoothPairingHelper.stopSmoothPairingScanning()
        sharedPreferences.unregisterOnSharedPreferenceChangeListener(preferencesChangeListener)
    }

    override fun setMapStyleFromDeepLink(
        style: String?,
        enable3D: Boolean?,
        roadSurfaces: List<String>?,
        hideCyclingForbidden: Boolean?
    ) {
        if (style != null) {
            val types = mapSelectionModel.loadBuiltInMapTypes()

            val mapType = if (style == "mml_vector_contour") {
                types.firstOrNull { it.name == MAP_TYPE_FINLAND_MAANMITTAUSLAITOS_TERRAIN }
            } else {
                types.firstOrNull { it.name.equals(style, ignoreCase = true) }
            }

            mapType?.let {
                Timber.d("Switching to deep-linked map type ${mapType.name}")
                mapSelectionModel.selectedMapType = mapType
            } ?: Timber.w("Unable to find matching map type for deep link: $style")
        }

        if (enable3D != null) {
            mapSelectionModel.map3dEnabled = enable3D
        }

        if (roadSurfaces != null) {
            val roadSurfaceTypes = mapSelectionModel.loadRoadSurfaceTypes().filter { roadSurfaceType ->
                roadSurfaces.find { it.equals(roadSurfaceType.name, ignoreCase = true) } != null
            }
            mapSelectionModel.selectedRoadSurfaces = roadSurfaceTypes
        }

        if (hideCyclingForbidden != null) {
            mapSelectionModel.hideCyclingForbiddenRoads = hideCyclingForbidden
        }
    }

    companion object {
        private const val POST_PAIRING_TOOL_TIP_DELAY_MILLIS = 800L
    }
}
