<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:installLocation="auto">

    <!-- Ignore ScopedStorage Lint check since we intentionally don't use requestLegacyExternalStorage any more -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" tools:ignore="ScopedStorage" />

    <!-- Needed by RecordWorkoutService and PreventDozeService -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-permission android:name="android.permission.CAMERA" />

    <!--  https://developer.huawei.com/consumer/en/doc/20602  -->
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />

    <application
        android:allowBackup="false"
        android:fullBackupContent="@xml/stt_backup_rules"
        android:dataExtractionRules="@xml/stt_backup_rules_31"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:supportsRtl="false"
        android:theme="@style/ApplicationTheme"
        tools:replace="android:supportsRtl,android:allowBackup"
        tools:ignore="UnusedAttribute,LockedOrientationActivity,Instantiatable">

        <activity
            android:name="com.stt.android.workouts.details.analysis.LandscapeAnalysisGraphActivity"
            android:theme="@style/LandscapeGraph"
            android:launchMode="singleTop"
            android:parentActivityName="com.stt.android.workout.details.WorkoutDetailsActivityNew"
            android:screenOrientation="sensorLandscape">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.stt.android.workout.details.WorkoutDetailsActivityNew" />

        </activity>
        <activity
            android:name="com.stt.android.launcher.ProxyActivity"
            android:theme="@style/WhiteTheme.Launcher"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <data android:scheme="http" />
                <data android:scheme="https" />
                <data android:host="www.sports-tracker.com" />
                <data android:pathPrefix="/friends/manage" />
                <data android:pathPrefix="/workout" />
                <data android:pathPrefix="/view_profile" />
                <data android:pathPrefix="/diary" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <data
                    android:host="@string/release_helpshift_domain_name"
                    android:pathPrefix="/a/sports-tracker"
                    android:scheme="https" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>
        <activity
            android:alwaysRetainTaskState="true"
            android:launchMode="singleTop"
            android:name="com.stt.android.home.HomeActivity"
            android:theme="@style/WhiteTheme"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name="com.stt.android.ui.activities.AppUpdateActivity"
            android:alwaysRetainTaskState="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.TransparentActivityTheme" />
        <activity
            android:alwaysRetainTaskState="true"
            android:launchMode="singleTask"
            android:name="com.stt.android.ui.activities.WorkoutActivity"
            android:parentActivityName="com.stt.android.home.HomeActivity"
            android:theme="@style/WhiteTheme.ActionBar">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.stt.android.home.HomeActivity" />
        </activity>

        <activity
            android:name="com.stt.android.home.dashboardv2.edit.DashboardTabEditActivity"
            android:theme="@style/WhiteTheme" />

        <activity
            android:name="com.stt.android.ui.activities.map.OngoingWorkoutMapActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.ui.activities.map.OngoingAndFollowWorkoutMapActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.ui.activities.map.OngoingAndFollowRouteMapActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.ui.activities.map.OngoingAndGhostWorkoutMapActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.ui.activities.map.StaticWorkoutMapActivity"
            android:theme="@style/WhiteTheme" />

        <activity
            android:launchMode="singleTask"
            android:name="com.stt.android.workoutsettings.WorkoutSettingsActivity"
            android:theme="@style/WhiteTheme" />

        <service
            android:exported="false"
            android:foregroundServiceType="location"
            android:name="com.stt.android.workouts.RecordWorkoutService" />
        <service
            android:exported="false"
            android:name="com.stt.android.workouts.edit.SaveWorkoutService"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:exported="false"
            android:name="com.stt.android.workouts.remove.RemoveWorkoutService"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:exported="false"
            android:name="com.stt.android.workouts.edit.SaveWorkoutHeaderService"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service android:name="com.stt.android.workouts.wearable.WearableListener"
            android:exported="true">
            <intent-filter>
                <action android:name="com.google.android.gms.wearable.DATA_CHANGED" />

                <data
                    android:host="*"
                    android:pathPrefix="/"
                    android:scheme="wear" />
            </intent-filter>
        </service>
        <service
            android:name="com.stt.android.workouts.PreventDozeService"
            android:foregroundServiceType="location"
            android:process="${applicationId}.PreventDozeProcess" />

        <!-- Currently needs to be portrait-only or else the saving task will fail miserably -->
        <activity
            android:alwaysRetainTaskState="true"
            android:exported="false"
            android:label="@string/title_activity_save_workout"
            android:launchMode="singleTask"
            android:name="com.stt.android.ui.activities.SaveWorkoutActivity"
            android:parentActivityName="com.stt.android.home.HomeActivity"
            android:theme="@style/WhiteTheme"
            tools:ignore="AppLinkUrlError">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.stt.android.home.HomeActivity" />

            <intent-filter>

                <!-- This intent is needed for twitter login. See STTConstants.TWITTER_CALLBACK_URL -->
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="com.sports-tracker"
                    android:scheme="oauth" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.stt.android.ui.fragments.login.terms.TermsActivity"
            android:configChanges="orientation|screenSize|screenLayout|smallestScreenSize"
            android:launchMode="singleTask"
            android:theme="@style/WhiteTheme" />
        <activity
            android:label="@string/edit_workout"
            android:name="com.stt.android.ui.activities.WorkoutEditDetailsActivity"
            android:configChanges="orientation|screenSize|screenLayout|smallestScreenSize"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.ui.activities.SetupHeartRateBeltActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.ui.activities.DisplayHeartRateActivity" />
        <activity
            android:label="@string/notifications_title"
            android:name="com.stt.android.social.notifications.NotificationActivity"
            android:parentActivityName="com.stt.android.home.HomeActivity"
            android:theme="@style/WhiteTheme">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.stt.android.home.HomeActivity" />
        </activity>
        <activity
            android:name="com.stt.android.social.notifications.inbox.MarketingInboxActivity"
            android:theme="@style/WhiteTheme"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT"/>
                <data
                    android:scheme="xgscheme"
                    android:host="com.tpns.push"
                    android:path="/marketinginbox" />

            </intent-filter>
        </activity>
        <activity android:name="com.stt.android.social.userprofile.UserFullScreenProfilePictureActivity"
                  android:theme="@style/WhiteTheme"/>
        <activity
            android:label="@string/userProfile"
            android:name="com.stt.android.social.userprofile.UserProfileActivity"
            android:parentActivityName="com.stt.android.home.HomeActivity"
            android:theme="@style/WhiteTheme">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.stt.android.home.HomeActivity" />
        </activity>
        <activity
            android:name="com.stt.android.ui.activities.SimilarWorkoutsActivity"
            android:parentActivityName="com.stt.android.workout.details.WorkoutDetailsActivityNew"
            android:label="@string/similar_workouts"
            android:theme="@style/WhiteTheme"/>

        <activity
            android:name="com.stt.android.ui.activities.competition.WorkoutCompetitionActivity"
            android:parentActivityName="com.stt.android.workout.details.WorkoutDetailsActivityNew"
            android:theme="@style/WhiteTheme"/>

        <activity
            android:name="com.stt.android.ui.activities.SetupCadenceActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.ui.activities.DisplayCadenceActivity" />
        <activity
            android:label="@string/settings_push_notifications"
            android:name="com.stt.android.ui.activities.settings.NotificationSettingsActivity"
            android:theme="@style/WhiteTheme"/>
        <activity
            android:label="@string/settings_privacy"
            android:name="com.stt.android.ui.activities.settings.PrivacySettingsActivity"
            android:theme="@style/WhiteTheme"/>
        <activity
            android:label="@string/settings_marketing_permission"
            android:name="com.stt.android.ui.activities.settings.MarketingPermissionsActivity"
            android:theme="@style/WhiteTheme"/>
        <activity
            android:label="@string/privacy_edit_past_activity_title"
            android:name="com.stt.android.ui.activities.settings.EditPastActivityPrivacyActivity"
            android:theme="@style/WhiteTheme"/>
        <activity
            android:label="@string/settings_tags"
            android:name="com.stt.android.ui.activities.settings.tags.TagsSettingsActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.home.diary.diarycalendar.planner.TrainingPlannerFullscreenActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:label="@string/power_management_activity_title"
            android:name="com.stt.android.ui.activities.settings.PowerManagementSettingsActivity"
            android:parentActivityName="com.stt.android.home.HomeActivity"
            android:theme="@style/WhiteTheme"/>
        <activity
            android:name="com.facebook.FacebookActivity"
            android:theme="@style/ApplicationTheme.FullyTranslucent"
            tools:replace="android:theme" />
        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="@string/facebook_login_protocol_scheme" />
            </intent-filter>
        </activity>
        <!--suppress AndroidDomInspection -->
        <activity
            android:name="com.facebook.LoginActivity"
            android:theme="@style/ApplicationTheme.FullyTranslucent"
            tools:ignore="MissingClass" />

        <activity
            android:name="com.stt.android.workoutdetail.trend.RecentWorkoutTrendActivity"
            android:parentActivityName="com.stt.android.workout.details.WorkoutDetailsActivityNew"
            android:theme="@style/WhiteTheme">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.stt.android.workout.details.WorkoutDetailsActivityNew" />
        </activity>
        <activity
            android:name="com.stt.android.ui.activities.RecentWorkoutSummaryActivity"
            android:parentActivityName="com.stt.android.workout.details.WorkoutDetailsActivityNew"
            android:label=""
            android:theme="@style/WhiteTheme">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.stt.android.workout.details.WorkoutDetailsActivityNew" />
        </activity>
        <activity
            android:name="com.stt.android.ui.activities.WorkoutMediaActivity"
            android:parentActivityName="com.stt.android.workout.details.WorkoutDetailsActivityNew">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.stt.android.workout.details.WorkoutDetailsActivityNew" />
        </activity>
        <activity
            android:label="@string/settings"
            android:name="com.stt.android.home.settings.SettingsActivityWithLogWarning"
            android:theme="@style/WhiteTheme" />
        <activity
            android:label="@string/account_settings_reset_password_title"
            android:name="com.stt.android.home.settings.resetpassword.ResetPasswordActivity"
            android:theme="@style/WhiteTheme"/>
        <activity
            android:label="@string/account_settings_delete_account_title"
            android:name="com.stt.android.home.settings.deleteaccount.DeleteAccountActivity"
            android:theme="@style/WhiteTheme"/>
        <activity
            android:label="@string/find_facebook_friends"
            android:name="com.stt.android.home.people.FindFbFriendsActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.multimedia.video.trimming.VideoTrimmingActivity"
            android:theme="@style/DarkTheme" />
        <activity
            android:name="com.stt.android.workouts.sharepreview.WorkoutSharePreviewActivity"
            android:parentActivityName="com.stt.android.workout.details.WorkoutDetailsActivityNew"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.summaries.SummaryWorkoutsListActivity"
            android:theme="@style/WhiteTheme"
            android:configChanges="keyboardHidden|orientation|screenSize"
        />

        <service
            android:name="com.stt.android.notifications.PushNotificationService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <receiver android:name="com.stt.android.analytics.ShareBroadcastReceiver" />

        <service
            android:name="com.stt.android.notifications.PushNotificationHandler"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <receiver
            android:name="com.stt.android.notifications.NotificationActionReceiver" />
        <service
            android:name="com.stt.android.workouts.wearable.WearableCleanupService"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service android:name="com.stt.android.workouts.wearable.TTSStarter" />

        <provider
            android:authorities="${applicationId}.FileProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            android:name="androidx.core.content.FileProvider">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="com.google.android.maps.v2.API_KEY"
            android:value="@string/google_map_v2_key" />
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />
        <!--suppress AndroidDomInspection -->
        <activity
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:name="com.google.android.gms.ads.AdActivity"
            android:theme="@android:style/Theme.Translucent"
            tools:ignore="MissingClass" />
        <activity
            android:name="com.stt.android.workoutcomparison.WorkoutComparisonActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:label="@string/title_activity_media_gallery"
            android:launchMode="singleTask"
            android:name="com.stt.android.multimedia.gallery.MediaGalleryActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:excludeFromRecents="true"
            android:name="com.stt.android.ui.activities.SimpleAlertDialog"
            android:theme="@style/Theme.SimpleAlertDialogActivityTheme" />
        <activity
            android:name="com.stt.android.workouts.sharepreview.WorkoutValuesPickingActivity"
            android:parentActivityName="com.stt.android.workouts.sharepreview.WorkoutSharePreviewActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.workouts.sharepreview.WorkoutGraphPickingActivity"
            android:parentActivityName="com.stt.android.workouts.sharepreview.WorkoutSharePreviewActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.home.settings.accountsettings.AccountSettingsActivity"
            android:theme="@style/WhiteTheme"/>
        <activity
            android:name="com.stt.android.workoutdetail.location.select.WorkoutSelectLocationActivity"
            android:parentActivityName="com.stt.android.ui.activities.WorkoutEditDetailsActivity"
            android:theme="@style/WhiteTheme" />

        <activity
            android:name="com.stt.android.home.diary.diarycalendar.workoutlist.CalendarWorkoutListActivity"
            android:parentActivityName="com.stt.android.home.HomeActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.home.diary.diarycalendar.sharesummary.DiaryCalendarShareSummaryActivity"
            android:parentActivityName="com.stt.android.home.HomeActivity"
            android:theme="@style/WhiteTheme" />

        <activity
            android:name="com.stt.android.ui.activities.settings.countrysubdivision.CountrySubdivisionListActivity"
            android:theme="@style/WhiteTheme" />

        <activity
            android:name="com.google.android.gms.oss.licenses.OssLicensesMenuActivity"
            android:theme="@style/LicensesTheme"
            tools:ignore="MissingClass"/>

        <activity
            android:name="com.google.android.gms.oss.licenses.OssLicensesActivity"
            android:theme="@style/LicensesTheme"
            tools:ignore="MissingClass"/>

        <activity
            android:name="com.stt.android.featuretoggle.FeatureToggleActivity"
            android:parentActivityName="com.stt.android.social.userprofile.UserProfileActivity"
            android:theme="@style/WhiteTheme" />

        <activity
            android:name="com.stt.android.social.following.PeopleActivity"
            android:parentActivityName="com.stt.android.social.userprofile.UserProfileActivity"
            android:theme="@style/WhiteTheme" />

        <activity
            android:name="com.stt.android.social.userprofile.followlist.FollowListActivity"
            android:parentActivityName="com.stt.android.social.userprofile.UserProfileActivity"
            android:theme="@style/WhiteTheme" />

        <activity
            android:name="com.stt.android.social.personalrecord.PersonalRecordsActivity"
            android:parentActivityName="com.stt.android.social.userprofile.UserProfileActivity"
            android:theme="@style/WhiteTheme" />

        <meta-data
            android:name="firebase_crashlytics_collection_enabled"
            android:value="false" />

        <meta-data
            android:name="com.facebook.sdk.AutoInitEnabled"
            android:value="false" />

        <!-- Notification icon for emarsys -->
        <meta-data
            android:name="com.emarsys.mobileengage.small_notification_icon"
            android:resource="@drawable/icon_notification"/>
    </application>

</manifest>
