package com.stt.android.home.explore.routes.details

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.widget.Toast
import androidx.annotation.IdRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.text.HtmlCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.commit
import androidx.fragment.app.commitNow
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetBehavior.BottomSheetCallback
import com.soy.algorithms.ascent.VerticalDelta
import com.soy.algorithms.climbanalysis.entities.ClimbGuidance
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.controllers.UserSettingsController
import com.stt.android.core.utils.EventThrottler
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.Point
import com.stt.android.domain.diarycalendar.LocationWithActivityType
import com.stt.android.domain.routes.Route
import com.stt.android.domain.routes.WaypointTools
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.diary.diarycalendar.RouteAndActivityType
import com.stt.android.home.explore.R
import com.stt.android.home.explore.databinding.ActivityRouteDetailBinding
import com.stt.android.home.explore.databinding.IncludeBottomsheetRouteDetailBinding
import com.stt.android.home.explore.routes.MapPresenter
import com.stt.android.home.explore.routes.MapView
import com.stt.android.home.explore.routes.RouteAltitudeChartData
import com.stt.android.home.explore.routes.RouteDetailsNavigator
import com.stt.android.home.explore.routes.RouteUtils.activityIdsToActivityTypeList
import com.stt.android.home.explore.routes.RouteUtils.getRouteBounds
import com.stt.android.home.explore.routes.RouteUtils.routePointsToLatLngList
import com.stt.android.home.explore.routes.RouteValueFormatHelper
import com.stt.android.home.explore.routes.planner.BaseRoutePlannerActivity
import com.stt.android.home.explore.routes.planner.waypoints.PointsWithDistances
import com.stt.android.home.explore.routes.planner.waypoints.details.WaypointDetails
import com.stt.android.home.explore.routes.planner.waypoints.details.WaypointDetailsFragment
import com.stt.android.home.explore.routes.planner.waypoints.details.WaypointDetailsMode
import com.stt.android.home.explore.routes.planner.waypoints.details.WaypointType
import com.stt.android.home.explore.routes.ui.ClimbSegmentRenderer
import com.stt.android.home.explore.routes.ui.PartnerBacklink
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.maps.MapType
import com.stt.android.maps.OnMapReadyCallback
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoPolyline
import com.stt.android.maps.SuuntoSupportMapFragment
import com.stt.android.maps.SuuntoTileOverlayOptions
import com.stt.android.maps.newLatLngBounds
import com.stt.android.routes.toLatLng
import com.stt.android.routes.widget.ActivityTypeIconsAdapter
import com.stt.android.ui.map.RouteMarkerHelper
import com.stt.android.ui.map.selection.MapSelectionDialogFragment
import com.stt.android.ui.utils.DialogHelper
import com.stt.android.ui.utils.SpeedDialogFragment
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.FlavorUtils
import com.stt.android.utils.STTConstants
import com.stt.android.utils.STTConstants.RequestCodes
import dagger.hilt.android.scopes.ActivityScoped
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject
import com.stt.android.R as BaseR

abstract class BaseRouteDetailsActivity :
    AppCompatActivity(),
    RouteDetailsView,
    MapView,
    OnMapReadyCallback,
    SpeedDialogFragment.SpeedDialogListener {
    class Navigator : RouteDetailsNavigator

    protected lateinit var binding: ActivityRouteDetailBinding
    private lateinit var bottomSheetDetailBinding: IncludeBottomsheetRouteDetailBinding

    private lateinit var formatHelper: RouteValueFormatHelper

    @ActivityScoped
    @Inject
    lateinit var routeDetailsPresenter: RouteDetailsPresenter

    @Inject
    lateinit var mapPresenter: MapPresenter

    @Inject
    lateinit var userSettingsController: UserSettingsController

    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    @Inject
    lateinit var waypointTools: WaypointTools

    @Inject
    @FeatureTogglePreferences
    lateinit var featureTogglePreferences: SharedPreferences

    private var mapFragment: SuuntoSupportMapFragment? = null
    private var isRouteDrawn = false
    private val climbSegmentRenderer by lazy {
        ClimbSegmentRenderer(requireNotNull(mapFragment))
    }

    private val optionsClickThrottler = EventThrottler()

    protected var suuntoMarker: SuuntoMarker? = null
    protected var currentX: Int = 0
    protected var needRestore: Boolean = false
    private var onMarkerClickListener: SuuntoMap.OnMarkerClickListener? = null

    private val legacyPolylines = mutableListOf<SuuntoPolyline>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        savedInstanceState?.let {
            currentX = it.getInt(CURRENT_X, 0)
            needRestore = true
        }
        binding = ActivityRouteDetailBinding.inflate(layoutInflater)
        bottomSheetDetailBinding =
            IncludeBottomsheetRouteDetailBinding.bind(binding.routeDetailBottomSheet.root)
        setContentView(binding.root)
        createMapFragment()
        binding.shareWorkoutButton.setOnClickListenerThrottled {
            routeDetailsPresenter.shareRoute(this@BaseRouteDetailsActivity)
        }

        binding.routeDetailBottomSheet.editSpeedTouchArea.setOnClickListenerThrottled {
            routeDetailsPresenter.startEditingSpeed(infoModelFormatter)
        }

        setSupportActionBar(binding.routeDetailToolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        formatHelper = RouteValueFormatHelper(this, infoModelFormatter)

        routeDetailsPresenter.routeDetailsViewData
            .onEach(::onViewDataUpdated)
            .launchIn(lifecycleScope)
    }

    private fun createMapFragment() {
        binding.routeDetailMainContent.routeDetailMainContentMapContainer.visibility =
            View.INVISIBLE
        val fm = supportFragmentManager
        mapFragment = fm.findFragmentByTag(MAP_FRAGMENT_TAG) as SuuntoSupportMapFragment?
        if (mapFragment == null) {
            val options = SuuntoMapOptions()
                .compassEnabled(true)
                .mapType(mapPresenter.currentMapType.name)
                .rotateGesturesEnabled(true)
                .scrollGesturesEnabled(true)
                .tiltGesturesEnabled(true)
                .zoomControlsEnabled(false)
                .zoomGesturesEnabled(true)
                .logoEnabled(resources.getBoolean(BaseR.bool.maps_logo_enabled))
                .attributionEnabled(resources.getBoolean(BaseR.bool.maps_logo_enabled))
                .showMyLocationMarker(false)
            mapFragment = SuuntoSupportMapFragment.newInstance(options)
            fm.commitNow {
                add(
                    R.id.route_detail_main_content_map_container,
                    requireNotNull(mapFragment),
                    MAP_FRAGMENT_TAG
                )
            }
        }
    }

    private fun onViewDataUpdated(viewData: BaseRouteDetailsPresenter.RouteDetailsViewData) {
        binding.loadingSpinner.isVisible = false

        showRouteAltitudeChart(
            chartData = viewData.routeAltitudeChartData,
            avgSpeed = viewData.route.averageSpeed,
            climbGuidance = viewData.climbGuidance
        )

        showRouteDetails(viewData.route, viewData.watchRouteListFull, viewData.climbGuidance)
    }

    protected abstract fun showRouteAltitudeChart(
        chartData: RouteAltitudeChartData,
        avgSpeed: Double,
        climbGuidance: ClimbGuidance
    )

    @Deprecated("Deprecated in Java")
    override fun onAttachFragment(fragment: Fragment) {
        super.onAttachFragment(fragment)
        if (fragment is SuuntoSupportMapFragment) {
            mapFragment = fragment.also {
                it.getMapAsync(this)
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.route_details_actions, menu)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (!optionsClickThrottler.checkAcceptEvent()) {
            return true
        }

        return when (item.itemId) {
            R.id.mapOptions -> {
                showMapSelectionDialogFragment()
                true
            }
            R.id.delete -> {
                deleteClicked()
                true
            }
            R.id.edit -> {
                if (!routeDetailsPresenter.isSubscribedToPremium()) {
                    showRequestPremiumDialog(item.itemId)
                } else {
                    startActivityForResult(
                        BaseRoutePlannerActivity.newStartIntentEditRoute(this, routeId),
                        RequestCodes.EDIT_ROUTE
                    )
                }
                true
            }

            R.id.copy -> {
                if (!routeDetailsPresenter.isSubscribedToPremium()) {
                    showRequestPremiumDialog(item.itemId)
                } else {
                    // same operation as edit route
                    startActivityForResult(
                        BaseRoutePlannerActivity.newStartIntentCopyRoute(
                            this@BaseRouteDetailsActivity,
                            routeId,
                            routeDetailsPresenter.isWatchRouteListFull
                        ),
                        RequestCodes.EDIT_ROUTE
                    )
                }
                true
            }
            android.R.id.home -> {
                onBackPressed()
                return true
            }
            else -> {
                super.onOptionsItemSelected(item)
            }
        }
    }

    abstract fun showRequestPremiumDialog(@IdRes actionId: Int)

    private fun showMapSelectionDialogFragment() {
        mapFragment?.getMapAsync { map ->
            val mapCenter: LatLng? = map.getCameraPosition()?.target
            MapSelectionDialogFragment.newInstance(
                mapsProviderName = map.getProviderName(),
                showHeatmaps = false,
                showRoadSurface = false,
                showMyTracks = false,
                showMyPOIsGroup = false,
                mapCenter = mapCenter,
                analyticsSource = AnalyticsEvent.ROUTE_DETAILS_SCREEN
            )
                .show(supportFragmentManager, MapSelectionDialogFragment.FRAGMENT_TAG)
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == RequestCodes.EDIT_ROUTE) {
            if (resultCode == RESULT_OK) {
                finish()
            }
        } else {
            super.onActivityResult(requestCode, resultCode, data)
        }
    }

    override fun onMapReady(map: SuuntoMap) {
        onMarkerClickListener = SuuntoMap.OnMarkerClickListener { marker: SuuntoMarker ->
            routeDetailsPresenter.findWaypointsOnRoute(marker.getPosition()!!)
            true
        }
        onMarkerClickListener?.let {
            map.addOnMarkerClickListener(it)
        }
    }

    override fun onFindWaypointsCompleted(waypoints: PointsWithDistances) {
        if (waypoints.points.isNotEmpty()) {
            val point = waypoints.points.first()
            // should not ever be null at this point
            val type = point.type?.let { WaypointType.from(it) } ?: WaypointType.WAYPOINT
            val waypointDetails = WaypointDetails(
                latLng = point.toLatLng(),
                type = type,
                altitude = point.altitude,
                distance = waypoints.distances,
                name = point.name,
                description = point.description,
            )
            showWaypointDetails(waypointDetails)
        }
    }

    private fun showWaypointDetails(waypointDetails: WaypointDetails) {
        val fm = supportFragmentManager
        val fragment = fm.findFragmentByTag(WaypointDetailsFragment.TAG)
        if (fragment == null) {
            WaypointDetailsFragment.newInstance(waypointDetails, WaypointDetailsMode.VIEW)
                .show(fm, WaypointDetailsFragment.TAG)
        }
    }

    private fun deleteClicked() {
        DialogHelper.showDialog(
            this,
            BaseR.string.delete,
            BaseR.string.delete_route,
            { _, _ -> routeDetailsPresenter.deleteRoute() }
        ) { _, _ -> routeDetailsPresenter.cancelRouteDeletion() }
    }

    private val routeId: String
        get() = requireNotNull(intent.getStringExtra(STTConstants.ExtraKeys.ROUTE_ID))

    override fun onStart() {
        super.onStart()
        routeDetailsPresenter.takeView(this)
        mapPresenter.takeView(this)
        waitForLayoutAndLoadRoute()
    }

    private fun waitForLayoutAndLoadRoute() = with(binding.routeDetailBottomSheet.root) {
        val layoutDone = height > 0
        if (layoutDone) {
            routeDetailsPresenter.loadRouteDetails(routeId)
        } else {
            viewTreeObserver.addOnGlobalLayoutListener(object : OnGlobalLayoutListener {
                override fun onGlobalLayout() {
                    viewTreeObserver.removeOnGlobalLayoutListener(this)
                    routeDetailsPresenter.loadRouteDetails(routeId)
                }
            })
        }
    }

    override fun onStop() {
        super.onStop()
        routeDetailsPresenter.dropView()
        mapPresenter.dropView()
    }

    protected open fun showRouteDetails(
        route: Route?,
        watchRouteListFull: Boolean,
        climbGuidance: ClimbGuidance
    ) {
        if (route != null) {
            val unit = userSettingsController.settings.measurementUnit
            val distanceToCurrentPosition = intent.getDoubleExtra(
                KEY_DISTANCE_TO_CURRENT_POSITION,
                0.0
            )
            binding.routeDetailMainContent.routeHeader.setRouteHeaderData(
                route,
                distanceToCurrentPosition,
                unit
            )
            setSummaryData(route)
            setActivityTypeIcons(activityIdsToActivityTypeList(route))

            with(binding.routeDetailBottomSheet) {
                route.producer?.let {
                    partnerBacklink.setContentWithTheme {
                        PartnerBacklink(
                            iconUrl = it.iconUrl,
                            name = it.name,
                            externalUrl = route.externalUrl
                        )
                    }

                    partnerBacklink.isVisible = true
                } ?: run {
                    partnerBacklink.isVisible = false
                }
            }

            expandBottomSheetAndShowRoute(route, climbGuidance)
        }
    }

    private fun expandBottomSheetAndShowRoute(route: Route, climbGuidance: ClimbGuidance) {
        val behavior = BottomSheetBehavior.from(binding.routeDetailBottomSheet.root)
        if (behavior.state == BottomSheetBehavior.STATE_EXPANDED) {
            // Bottom sheet behavior and map padding have already been set up
            // Show route immediately
            setMapPaddingAndShowRoute(route, climbGuidance)
        }
        behavior.state = BottomSheetBehavior.STATE_EXPANDED
        behavior.setBottomSheetCallback(object : BottomSheetCallback() {
            override fun onStateChanged(bottomSheet: View, newState: Int) {
                if (newState == BottomSheetBehavior.STATE_EXPANDED) {
                    // showRouteOnMap needs to be called after the bottom sheet is in expanded state
                    // to get the correct padding for CameraUpdateFactory.newLatLngBounds.
                    // Had issues with mapbox to dynamically update the padding
                    // with how CameraUpdateFactory.newLatLngBounds works without persisting
                    // given padding on mapbox 8.4.0. Don't call setMapPadding after this.
                    setMapPaddingAndShowRoute(route, climbGuidance)
                }
            }

            override fun onSlide(bottomSheet: View, slideOffset: Float) {
                setMapPadding()
            }
        })
    }

    private fun setMapPaddingAndShowRoute(route: Route, climbGuidance: ClimbGuidance) {
        setMapPadding()
        showRouteOnMap(route, getRouteBounds(route.segments), climbGuidance)
    }

    fun setMapPadding() {
        var bottomHeight = binding.routeDetailMainContent.root.bottom -
            binding.routeDetailBottomSheet.root.top
        mapPresenter.resetPadding()
        if (binding.credit.isVisible) {
            bottomHeight += binding.credit.height
        }
        mapPresenter.addPadding(0, 0, 0, bottomHeight)
    }

    override fun updateAllDataDisplayed(distanceAndDuration: Pair<Double, Long>, verticalDelta: VerticalDelta?) {
        with(binding.routeDetailBottomSheet) {
            totalDistanceValue.text = formatHelper.formatValueAndUnitAsSpannedString(
                SummaryItem.DISTANCE,
                distanceAndDuration.first
            )
            durationValue.text = infoModelFormatter.formatEstimatedRouteDuration(distanceAndDuration.second)
            verticalDelta?.let {
                ascentValue.text = formatHelper.formatValueAndUnitAsSpannedString(
                    SummaryItem.ASCENTALTITUDE,
                    it.ascent
                )

                descentValue.text = formatHelper.formatValueAndUnitAsSpannedString(
                    SummaryItem.DESCENTALTITUDE,
                    it.descent
                )
            }
        }
    }

    private fun setSummaryData(route: Route) =
        with(binding.routeDetailBottomSheet) {
            totalDistanceValue.text = formatHelper.formatValueAndUnitAsSpannedString(
                SummaryItem.DISTANCE,
                route.totalDistance
            )

            ascentValue.text = formatHelper.formatValueAndUnitAsSpannedString(
                SummaryItem.ASCENTALTITUDE,
                route.ascent
            )

            descentValue.text = formatHelper.formatValueAndUnitAsSpannedString(
                SummaryItem.DESCENTALTITUDE,
                route.descent
            )

            durationValue.text = infoModelFormatter.formatEstimatedRouteDuration(
                route.getDurationEstimation()
            )

            speedLabel.text = formatHelper.formatValueAndUnitAsPlainString(
                SummaryItem.AVGSPEED,
                route.averageSpeed
            )
        }

    private fun setActivityTypeIcons(activities: List<ActivityType>) {
        with(bottomSheetDetailBinding.activityTypeIcons) {
            setHasFixedSize(true)
            layoutManager = LinearLayoutManager(
                this@BaseRouteDetailsActivity,
                LinearLayoutManager.HORIZONTAL,
                false
            )
            adapter =
                ActivityTypeIconsAdapter(activities, ActivityTypeIconsAdapter.Background.GRAY, 3)
        }
    }

    private fun showRouteOnMap(
        route: Route,
        bounds: LatLngBounds,
        climbGuidance: ClimbGuidance
    ) {
        val segments = route.segments
        if (isRouteDrawn || segments.isEmpty() || mapFragment == null) {
            return
        }
        // Climb guidance is not supported in ST
        if (FlavorUtils.isSportsTracker) {
            showLegacyRouteOnMap(route, bounds)
            return
        }

        lifecycleScope.launch {
            climbSegmentRenderer.render(climbGuidance.segments)

            val map = mapFragment?.getMap() ?: return@launch
            val mapView = mapFragment?.view
            if (mapView?.viewTreeObserver?.isAlive == true) {
                newLatLngBounds(bounds, map, mapView)
                binding.routeDetailMainContent.routeDetailMainContentMapContainer.isVisible = true
            }

            val startPoint = segments.first().routePoints.firstOrNull()
            if (startPoint != null) {
                RouteMarkerHelper.drawStartPoint(
                    this@BaseRouteDetailsActivity,
                    map,
                    LatLng(startPoint.latitude, startPoint.longitude),
                    isSinglePointRoute = false,
                    isWorkout = false
                )
            }
            val endPoint = segments.last().routePoints.lastOrNull()
            if (endPoint != null && !endPoint.isSamePosition(startPoint)) {
                RouteMarkerHelper.drawEndPoint(
                    this@BaseRouteDetailsActivity,
                    map,
                    LatLng(endPoint.latitude, endPoint.longitude),
                    false
                )
            }

            RouteMarkerHelper.drawWaypoints(this@BaseRouteDetailsActivity, map, route, waypointTools)

            isRouteDrawn = true
        }
    }

    private fun showLegacyRouteOnMap(route: Route, bounds: LatLngBounds) {
        clearLegacyPolylines()
        mapFragment?.getMapAsync { map: SuuntoMap ->
            map.batchUpdate {
                val mapView = mapFragment?.view
                if (mapView != null && mapView.viewTreeObserver.isAlive) {
                    newLatLngBounds(bounds, map, mapView)
                    binding.routeDetailMainContent.routeDetailMainContentMapContainer.visibility =
                        View.VISIBLE
                }
                val latLngs: MutableList<LatLng> = ArrayList()
                for (segment in route.segments) {
                    latLngs.addAll(routePointsToLatLngList(segment.routePoints))
                }
                val polyline = RouteMarkerHelper.drawRouteWithStartPoint(
                    this@BaseRouteDetailsActivity,
                    map,
                    latLngs,
                    false
                )
                legacyPolylines.add(polyline)
                if (latLngs.size > 1) {
                    RouteMarkerHelper.drawEndPoint(
                        this@BaseRouteDetailsActivity,
                        map,
                        latLngs.last(),
                        false
                    )
                }
                RouteMarkerHelper.drawWaypoints(this, map, route, waypointTools)

                isRouteDrawn = true
            }
        }
    }

    override fun highlightRouteByIndex(
        climbGuidance: ClimbGuidance,
        segmentIndex: Int,
        pointIndex: Int
    ) {
        lifecycleScope.launch {
            climbSegmentRenderer.render(climbGuidance.segments, segmentIndex, pointIndex)
        }
    }

    override fun highlightRouteByIndex(
        highlightPoints: List<Point>,
        translucentPoints: List<Point>,
    ) {
        clearLegacyPolylines()
        mapFragment?.getMapAsync { map ->
            val highlightPart = RouteMarkerHelper.drawRoute(
                this@BaseRouteDetailsActivity,
                map,
                routePointsToLatLngList(highlightPoints),
                false,
            )
            legacyPolylines.add(highlightPart)
            val translucentPart = RouteMarkerHelper.drawRoute(
                this@BaseRouteDetailsActivity,
                map,
                routePointsToLatLngList(translucentPoints),
                isWorkout = false,
                translucent = true,
            )
            legacyPolylines.add(translucentPart)
        }
    }

    private fun clearLegacyPolylines() {
        legacyPolylines.forEach { it.remove() }
        legacyPolylines.clear()
    }

    private fun newLatLngBounds(
        bounds: LatLngBounds,
        map: SuuntoMap,
        mapView: View
    ) {
        val cameraUpdate = newLatLngBounds(
            bounds,
            resources.getDimensionPixelSize(BaseR.dimen.size_spacing_xxlarge)
        )
        if (mapView.width > 0 && mapView.height > 0) {
            map.moveCamera(cameraUpdate)
        } else {
            mapView.viewTreeObserver.addOnGlobalLayoutListener(
                object : OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        mapView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                        map.moveCamera(cameraUpdate)
                    }
                }
            )
        }
    }

    override fun getLifecycleScope(): LifecycleCoroutineScope {
        return lifecycleScope
    }

    override fun onRouteDeleted(success: Boolean) {
        if (success) {
            Toast.makeText(applicationContext, BaseR.string.route_deleted, Toast.LENGTH_LONG).show()
            finish()
        } else {
            // This is very unlikely to happen
            Toast.makeText(applicationContext, BaseR.string.error_saving_data, Toast.LENGTH_LONG).show()
        }
    }

    override fun showEditSpeedDialog(initialValue: String, unitRes: Int) {
        SpeedDialogFragment.newAvgSpeedInstance(initialValue, getString(unitRes))
            .show(supportFragmentManager, EDIT_SPEED_FRAGMENT_TAG)
    }

    override fun showSpeedUpdatedFailed() {
        Toast.makeText(applicationContext, BaseR.string.error_generic_try_again, Toast.LENGTH_LONG).show()
    }

    override fun setMapType(type: MapType) {
        mapFragment?.getMapAsync { map ->
            map.setMapType(type.name)
        }
    }

    override fun setMapCreditLink(credit: String) = with(binding.credit) {
        movementMethod = LinkMovementMethod.getInstance()
        text = HtmlCompat.fromHtml(credit, HtmlCompat.FROM_HTML_MODE_LEGACY)
        visibility = View.VISIBLE
        setMapPadding()
    }

    override fun hideMapCreditLink() {
        binding.credit.visibility = View.GONE
        setMapPadding()
    }

    override fun setPadding(left: Int, top: Int, right: Int, bottom: Int) {
        mapFragment?.getMapAsync { map ->
            map.setPadding(left, top, right, bottom)
        }
    }

    override fun hasMapSelectionControls(): Boolean {
        return false
    }

    override fun heatmapsEnabled(): Boolean {
        return false
    }

    override fun myTracksEnabled(): Boolean = false

    override fun roadSurfaceEnabled(): Boolean {
        return false
    }

    override fun removeHeatmapOverlay() {
        // Not supported
    }

    override fun drawMyTracks(
        myTracks: List<RouteAndActivityType>,
        myTrackMarkers: List<LocationWithActivityType>,
    ) {
        // Not supported
    }

    override fun removeMyTracks() {
        // Not supported
    }

    override fun removeRoadSurfaceOverlay() {
        // Not supported
    }

    override fun removeStartingPointsOverlay() {
        // Not supported
    }

    override fun addHeatmapOverlay(options: SuuntoTileOverlayOptions) {
        // Not supported
    }

    override fun addRoadSurfaceOverlay(options: List<SuuntoTileOverlayOptions>) {
        // Not supported
    }

    override fun addStartingPointsOverlay(options: SuuntoTileOverlayOptions) {
        // Not supported
    }

    override fun onSpeedEntered(speedInMetersPerSecond: Double) {
        routeDetailsPresenter.saveSpeedToRoute(speedInMetersPerSecond)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putInt(CURRENT_X, currentX)
        super.onSaveInstanceState(outState)
    }

    override fun onDestroy() {
        mapFragment?.let {
            it.getMapAsync { map ->
                onMarkerClickListener?.let {
                    map.removeOnMarkerClickListener(it)
                }
            }
            if (isFinishing) {
                try {
                    supportFragmentManager.commit(allowStateLoss = false) {
                        remove(it)
                    }
                } catch (_: IllegalStateException) {
                }
            }
        }
        onMarkerClickListener = null
        lifecycleScope.cancel()
        super.onDestroy()
    }

    companion object {
        private const val CURRENT_X = "CURRENT_X"
        private const val EDIT_SPEED_FRAGMENT_TAG = "com.stt.android.EDIT_SPEED_FRAGMENT_TAG"
        private const val KEY_DISTANCE_TO_CURRENT_POSITION =
            "com.stt.android.KEY_DISTANCE_TO_CURRENT_POSITION"
        private const val MAP_FRAGMENT_TAG = "RouteDetailsMapFragment"

        fun newStartIntent(
            context: Context,
            routeId: String,
            distanceToCurrentPosition: Double
        ): Intent {
            require(routeId.isNotEmpty())
            return Intent(context, RouteDetailsActivity::class.java)
                .putExtra(STTConstants.ExtraKeys.ROUTE_ID, routeId)
                .putExtra(KEY_DISTANCE_TO_CURRENT_POSITION, distanceToCurrentPosition)
        }
    }
}
