package com.stt.android.home.explore.routes.list

import android.annotation.SuppressLint
import android.content.res.Resources
import android.util.Size
import androidx.annotation.StringRes
import androidx.databinding.ObservableField
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.amersports.formatter.Unit
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.Point
import com.stt.android.domain.android.FetchLocationEnabledUseCase
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.routes.GetRoutesUseCase
import com.stt.android.domain.routes.Route
import com.stt.android.domain.routes.ShareRouteUseCase
import com.stt.android.domain.routes.UpdateAverageSpeedForRouteUseCase
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.R
import com.stt.android.home.explore.routes.RouteUtils
import com.stt.android.home.explore.routes.ShareRouteEvent
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.mapping.InfoModelFormatter.Companion.getUnitResId
import com.stt.android.maps.MapSnapshotSpec
import com.stt.android.maps.MapSnapshotter
import com.stt.android.maps.location.LocationNotAvailableException
import com.stt.android.maps.location.SuuntoLocationSource
import com.stt.android.network.interfaces.ANetworkProvider
import com.stt.android.ui.utils.SingleLiveEvent
import io.reactivex.Scheduler
import io.reactivex.disposables.Disposable
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.rx2.rxSingle
import timber.log.Timber
import java.util.concurrent.TimeUnit
import kotlin.reflect.KClass

@SuppressLint("CheckResult")
abstract class BaseRouteListViewModel(
    val currentUserController: CurrentUserController,
    private val getRoutesUseCase: GetRoutesUseCase,
    private val locationSource: SuuntoLocationSource,
    private val shareRouteUseCase: ShareRouteUseCase,
    private val updateAverageSpeedForRouteUseCase: UpdateAverageSpeedForRouteUseCase,
    private val infoModelFormatter: InfoModelFormatter,
    private val mapSnapshotter: MapSnapshotter,
    private val isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    private val routeSortingRuleStore: RouteSortingRuleStore,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers,
    private val resources: Resources,
    userSettingsController: UserSettingsController,
    locationUseCase: FetchLocationEnabledUseCase
) : LoadingStateViewModel<RouteListContainer>(ioThread, mainThread, coroutinesDispatchers) {
    private var currentLocation: Point? = null

    // Events
    val routeClicked: LiveData<RouteListItem>
        get() = _routeClicked
    private val _routeClicked = SingleLiveEvent<RouteListItem>()

    val newRouteClicked: LiveData<Any>
        get() = _newRouteClicked
    private val _newRouteClicked = SingleLiveEvent<Any>()

    val handleFabSpeedDialMenu: LiveData<Int>
        get() = _handleFabSpeedDialMenu
    private val _handleFabSpeedDialMenu = SingleLiveEvent<Int>()

    // Editing average speed
    sealed class EditSpeedEvent {
        data class LaunchEditor(
            val routeId: String,
            val formattedSpeed: String,
            @StringRes val unitRes: Int,
        ) : EditSpeedEvent()

        object EditingFailed : EditSpeedEvent()
    }

    val editSpeedEvent: LiveData<EditSpeedEvent>
        get() = _editSpeedEvent
    private val _editSpeedEvent = SingleLiveEvent<EditSpeedEvent>()

    // Route map snapshot handling
    var mapViewSize: Size? = null

    private val visibleRouteIds = mutableSetOf<String>()

    val shareRouteEvent: LiveData<ShareRouteEvent>
        get() = _shareRouteEvent
    private val _shareRouteEvent = SingleLiveEvent<ShareRouteEvent>()

    val isSubscribedToPremium = isSubscribedToPremiumUseCase().asLiveData()

    private val unit = userSettingsController.settings.measurementUnit

    private var currentSortingRule = routeSortingRuleStore.getRouteSortingRule()

    val searchContent: ObservableField<String> = ObservableField()

    val sortingRuleLabel: ObservableField<String> =
        ObservableField(resources.getString(currentSortingRule.label))

    var distanceFilter: DistanceFilter = DistanceFilter()

    private var _locationDisabled = SingleLiveEvent<Boolean>()
    val locationDisabled: LiveData<Boolean>
        get() = _locationDisabled

    private var locationEnabled = false
    private var locationDisposable: Disposable? = null

    private var loadJob: Job? = null

    /**
     * When data is refreshed, whether to attempt to sort based on the last data.
     */
    private var dependsOnLoadedData = false

    init {
        locationDisposable = locationUseCase.locationEnabled().subscribeBy(
            onNext = { locationEnabled = it },
            onError = { Timber.w(it, "Failed to get current enabled/disabled status.") }
        )
        val errors = mapOf<KClass<out Exception>, ErrorEvent>(
            LocationNotAvailableException::class to ErrorEvent(
                shouldHandle = true,
                errorStringRes = R.string.no_current_location,
                canRetry = false,
                showCloseButton = false
            )
        )
        ErrorEvent.registerErrorEvents(errors)
    }

    fun updateSortingRule(sortingRule: RouteSortingRule) {
        if (sortingRule == RouteSortingRule.NEAREST_TO_ME) {
            locationSource.getLastKnownLocation({ loc ->
                currentSortingRule = sortingRule
                sortingRuleLabel.set(resources.getString(sortingRule.label))
                loadJob?.cancel()
                notifyLoading()
                currentLocation = Point(longitude = loc.longitude, latitude = loc.latitude)
                findRoutes()
            }, {
                notifyError(LocationNotAvailableException(cause = it), viewState.value?.data)
                Timber.w(it, "Failed to get last known location.")
            })
        } else {
            currentSortingRule = sortingRule
            sortingRuleLabel.set(resources.getString(sortingRule.label))
            load()
        }
        routeSortingRuleStore.saveRouteSortingRule(sortingRule)
    }

    fun load() {
        loadJob?.cancel()
        notifyLoading()

        locationSource.getLastKnownLocation({ loc ->
            currentLocation = Point(longitude = loc.longitude, latitude = loc.latitude)
            findRoutes()
        }, {
            findRoutes()
            Timber.w(it, "Failed to get last known location.")
        })
    }

    fun dependsOnLoadedData(depends: Boolean) {
        dependsOnLoadedData = depends
    }

    private fun findRoutes() {
        loadJob?.cancel()
        dependsOnLoadedData(false)

        loadJob = viewModelScope.launch(io) {
            val getRoutesFlow = getRoutesUseCase.getRoutesRx(
                syncWithRemote = true,
                includeSegments = false
            ).publish {
                // Emit first immediately, and debounce rest
                it.take(1)
                    .concatWith(it.debounce(ROUTE_UPDATES_DEBOUNCE_INTERVAL, TimeUnit.MILLISECONDS))
            }

            val routeListItemsFlow =
                RouteUtils.findRoutesWithDistance(getRoutesFlow, currentLocation)
                    .switchMap { routes ->
                        rxSingle { getIsDeviceRouteListFull() }.toFlowable().map { routes to it }
                    }.map { (routes, watchRouteListFull) ->
                        routes.map { (route, distance) ->
                            route.toListItem(
                                isSyncable = areRoutesSyncableWithDevice,
                                distance = distance.takeIf { it > 0 }, // Filter out DISTANCE_NOT_SET
                                isWatchRouteListFull = watchRouteListFull
                            )
                        }
                    }.map {
                        it.filter { route ->
                            route.isPassingFilter(
                                resources,
                                searchContent.get(),
                                distanceFilter
                            )
                        }.sortedWith(currentSortingRule.comparator)
                    }.asFlow()

            routeListItemsFlow.combine(isSubscribedToPremiumUseCase()) { listItems, isSubscribedToPremium ->
                val handledList = handleDependsOnLoadedData(listItems)
                if (handledList.second) notifyLoading()
                RouteListContainer(
                    routes = handledList.first,
                    onRouteClicked = ::handleRouteClick,
                    onAddToWatchToggled = ::handleAddToWatchToggled,
                    onShareClicked = ::handleShare,
                    onEditSpeedClicked = ::handleEditSpeed,
                    onRouteItemVisibilityChanged = ::updateRouteItemVisibility,
                    onOpenPremiumPromotionClicked = ::onOpenPremiumPromotionClicked,
                    isSubscribedToPremium = isSubscribedToPremium,
                    emptyState = getEmptyState(handledList.first.isEmpty()),
                    newRouteClicked = { _newRouteClicked.call() }
                )
            }.catch { throwable ->
                Timber.d(throwable, "Loading route list failed")
                notifyError(throwable)
            }.onEach { container ->
                mapSnapshotter.setMapSnapshotSpecsForBackgroundGeneration(container.routes.mapNotNull { it.snapshotSpec }, viewModelScope)
            }.collect {
                notifyDataLoaded(it)
            }
        }
    }

    private fun getEmptyState(isEmpty: Boolean): RouteEmptyState? {
        val isAnyDistanceFilter = distanceFilter.isAnyDistanceFilter()
        val isSearchContentEmpty = searchContent.get().isNullOrEmpty()
        return if (isEmpty) {
            when {
                !isSearchContentEmpty -> RouteEmptyState.NoSearchResults
                !isAnyDistanceFilter -> RouteEmptyState.NoResultsInDistanceRange
                else -> RouteEmptyState.DefaultEmpty
            }
        } else {
            null
        }
    }

    /**
     * When [dependsOnLoadedData] is true, Sort route list data in the same order as last
     * @param routeList latest routes
     * @return When Pair.second is true, display the [routeList], else sorted route list data in the same order as last.
     */
    private fun handleDependsOnLoadedData(routeList: List<RouteListItem>): Pair<List<RouteListItem>, Boolean> {
        val lastList = viewState.value?.data?.routes
        if (dependsOnLoadedData && viewState.value?.isLoaded() == true && !lastList.isNullOrEmpty() && routeList.size <= lastList.size) {
            val sortedAsLastList = mutableListOf<RouteListItem>()
            for (lastItem in lastList) {
                val findRoute = routeList.firstOrNull { item -> lastItem.id == item.id }
                if (findRoute != null) {
                    sortedAsLastList.add(findRoute)
                } else {
                    return routeList to true
                }
            }
            return sortedAsLastList to false
        } else {
            return routeList to true
        }
    }

    private fun Route.toListItem(
        isSyncable: Boolean,
        distance: Double?,
        isWatchRouteListFull: Boolean
    ) = RouteListItem(
        name = name,
        activityIds = activityIds.map { ActivityType.valueOf(it) },
        totalDistance = totalDistance,
        ascent = ascent,
        descent = descent,
        averageSpeed = averageSpeed,
        watchEnabled = watchEnabled,
        distanceFromCurrentLocation = distance,
        estimatedDuration = getDurationEstimation(),
        key = key.takeUnless { it.isEmpty() },
        id = id,
        snapshotSpec = getSnapshotSpec(),
        segmentsModifiedDate = segmentsModifiedDate,
        isSyncable = isSyncable,
        watchSyncState = watchSyncState,
        watchSyncResponseCode = watchSyncResponseCode,
        isWatchRouteListFull = isWatchRouteListFull,
        producerName = producer?.name,
        modifiedDate = modifiedDate,
        createdDate = createdDate
    )

    protected abstract val areRoutesSyncableWithDevice: Boolean

    protected abstract suspend fun getIsDeviceRouteListFull(): Boolean

    protected abstract fun handleAddToWatchToggled(listItem: RouteListItem, watchEnabled: Boolean)

    protected abstract fun onOpenPremiumPromotionClicked()

    private fun handleShare(listItem: RouteListItem) {
        when {
            !ANetworkProvider.isOnline() -> {
                _shareRouteEvent.value = ShareRouteEvent.NoNetwork
            }
            listItem.key == null -> {
                _shareRouteEvent.value = ShareRouteEvent.NotSynced
            }
            else -> {
                launch {
                    runCatching {
                        shareRouteUseCase.shareRoute(listItem.key)
                    }.onSuccess {
                        _shareRouteEvent.value = ShareRouteEvent.RouteShared(it)
                    }.onFailure {
                        Timber.w(it, "Failed to share link")
                        _shareRouteEvent.value = ShareRouteEvent.SharingFailed
                    }
                }
            }
        }
    }

    private fun handleEditSpeed(listItem: RouteListItem) {
        val formattedValue = try {
            infoModelFormatter.formatValue(SummaryItem.AVGSPEED, listItem.averageSpeed).value
        } catch (e: Exception) {
            Timber.w(e, "Error formatting speed value")
            null
        } ?: listItem.averageSpeed.toString()

        _editSpeedEvent.value = EditSpeedEvent.LaunchEditor(
            routeId = listItem.id,
            formattedSpeed = formattedValue,
            unitRes = infoModelFormatter.unit.speedUnit
        )
    }

    override fun retryLoading() = load()

    fun updateSpeed(routeId: String, speedInMetersPerSecond: Double) = launch(io) {
        runSuspendCatching {
            dependsOnLoadedData(true)

            updateAverageSpeedForRouteUseCase.updateAverageSpeed(
                routeId,
                speedInMetersPerSecond
            )
            Timber.v("Succeed to update route speed")
        }.onFailure { e ->
            Timber.w(e, "Failed to update route speed")
            _editSpeedEvent.value = EditSpeedEvent.EditingFailed
        }
    }

    private fun updateRouteItemVisibility(routeId: String, visible: Boolean) {
        if (visible) {
            visibleRouteIds.add(routeId)
        } else {
            visibleRouteIds.remove(routeId)
        }
    }

    private fun Route.getSnapshotSpec(): MapSnapshotSpec.Route? {
        val size = mapViewSize ?: return null

        return MapSnapshotSpec.Route(
            routeId = id,
            segmentsModifiedDateMillis = segmentsModifiedDate,
            showTurnByTurnWaypoints = turnWaypointsEnabled,
            width = size.width,
            height = size.height,
        )
    }

    private fun handleRouteClick(listItem: RouteListItem) {
        _routeClicked.value = listItem
        dependsOnLoadedData(true)
    }

    fun filterDistanceLabel(): CharSequence {
        val unit = if (unit == MeasurementUnit.IMPERIAL) Unit.MI else Unit.KM
        val unitText = getUnitResId(unit)?.let { resources.getString(it) }
        val maxValue = FilterPoint.POINT_MORE_THAN_100KM.minKm
        return when {
            distanceFilter.leftValue == 0 && distanceFilter.rightValue == 0 -> resources.getString(R.string.any_distance)
            distanceFilter.rightValue != maxValue && distanceFilter.leftValue == distanceFilter.rightValue -> "${distanceFilter.leftValue} $unitText"
            distanceFilter.rightValue != maxValue -> "${distanceFilter.leftValue}–${distanceFilter.rightValue} $unitText"
            distanceFilter.leftValue != maxValue -> "${distanceFilter.leftValue}–${FilterPoint.POINT_MORE_THAN_100KM.digitalText} $unitText"
            else -> "${FilterPoint.POINT_MORE_THAN_100KM.digitalText} $unitText"
        }
    }

    override fun onCleared() {
        loadJob?.cancel()
        locationDisposable?.dispose()
        super.onCleared()
    }

    companion object {
        private const val ROUTE_UPDATES_DEBOUNCE_INTERVAL = 200L // milliseconds
    }

    private fun RouteListItem.isPassingFilter(
        resources: Resources,
        searchContent: String?,
        distanceFilter: DistanceFilter
    ): Boolean =
        isNameOrActivityTypePassingFilter(resources, searchContent) && isTotalDistancePassingFilter(
            distanceFilter
        )

    private fun RouteListItem.isNameOrActivityTypePassingFilter(
        resources: Resources,
        searchContent: String?
    ) = if (searchContent.isNullOrEmpty()) {
        true
    } else {
        name.contains(searchContent, true) || activityIds.any {
            it.getLocalizedName(resources).contains(searchContent, true)
        }
    }

    /**
     * The totalDistance will be rounded to show, such as 999.5 m will be display as 1.00 km, 0.995 mi will be display as 1 mi
     * If the distance is less than one unit, determine whether the minimum filter value is 0
     * Otherwise, filter according to the value that will be displayed
     */
    private fun RouteListItem.isTotalDistancePassingFilter(distanceFilter: DistanceFilter): Boolean {
        if (totalDistance + roundToOneUnitValue() < unit.fromDistanceUnit(1.0)) {
            // If the total distance is rounded to less than one unit, the display will be in meters or ft, but the minimum filter value is 0, routes will be filtered out
            return distanceFilter.leftValue == 0
        }
        val formattedTotalDistance = infoModelFormatter.formatDistance(totalDistance).getOrNull()?.value?.toDouble() ?: return true
        val maxValue = FilterPoint.POINT_MORE_THAN_100KM.minKm
        return when {
            distanceFilter.leftValue == maxValue -> formattedTotalDistance > FilterPoint.POINT_100KM.minKm
            distanceFilter.rightValue == maxValue -> formattedTotalDistance >= distanceFilter.minKM
            else -> formattedTotalDistance >= distanceFilter.minKM && formattedTotalDistance <= distanceFilter.maxKm
        }
    }

    /**
     * Determine whether m/ft will be rounded to km/mi when distance less than one unit
     */
    private fun roundToOneUnitValue(): Double {
        return when (unit) {
            // 0.005 mi
            MeasurementUnit.IMPERIAL -> MeasurementUnit.IMPERIAL.fromDistanceUnit(0.005)
            // 0.5 m
            else -> 0.5
        }
    }

    @SuppressLint("CheckResult")
    fun changeSortingRule(sortingRule: RouteSortingRule) {
        if (sortingRule == currentSortingRule) return

        if (sortingRule != RouteSortingRule.NEAREST_TO_ME) {
            updateSortingRule(sortingRule)
        } else {
            _locationDisabled.postValue(!locationEnabled)
        }
    }

    fun setHandleFabSpeedDialMenu(menuId: Int) {
        _handleFabSpeedDialMenu.value = menuId
    }
}
