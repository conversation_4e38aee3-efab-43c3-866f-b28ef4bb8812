package com.stt.android.home.explore.routesv2

import com.stt.android.domain.routes.RouteWatchSyncState
import com.stt.android.domain.routes.TopRoute
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.explore.R

sealed class RouteViewData {

    data object Loading : RouteViewData()

    data class Loaded(val routesContainer: LibraryRoutesContainer) : RouteViewData()

    data object Error : RouteViewData()
}

enum class RoutesPage(val resId: Int) {
    MINE(R.string.library_my_routes_label),
    POPULAR(R.string.popular_routes),
}

data class LibraryRoutesContainer(
    val routes: List<LibraryRouteItem>,
    val onRouteClicked: (LibraryRouteItem) -> Unit,
    val onShareClicked: (LibraryRouteItem) -> Unit,
    val onAddToWatchToggled: (LibraryRouteItem, Boolean) -> Unit,
    val onRoutePageChanged: (RoutesPage) -> Unit,
    val isSubscribedToPremium: Boolean,
    val onRouteFavoriteClicked: ((LibraryRouteItem) -> Unit)? = null,
)

sealed class LibraryRouteItem(
    open val name: String,
    open val activityIds: List<ActivityType>,
    open val totalDistance: Double,
    open val ascent: Double,
    open val descent: Double,
    open val estimatedDuration: Long,
    open val averageSpeed: Double,
    open val watchEnabled: Boolean,
    open val distanceFromCurrentLocation: Double?,
    open val key: String?,
    open val routerId: String,
    open val segmentsModifiedDate: Long,
    open val isSyncable: Boolean,
    open val watchSyncState: RouteWatchSyncState,
    open val watchSyncResponseCode: Int,
    open val isWatchRouteListFull: Boolean,
    open val modifiedDate: Long,
    open val createdDate: Long,
    open val turnWaypointsEnabled: Boolean,
)

data class PopularRouteItem(
    override val name: String,
    override val totalDistance: Double,
    override val ascent: Double,
    override val descent: Double,
    override val estimatedDuration: Long,
    override val averageSpeed: Double,
    override val watchEnabled: Boolean,
    override val distanceFromCurrentLocation: Double?,
    override val key: String?,
    override val routerId: String,
    override val segmentsModifiedDate: Long,
    override val isSyncable: Boolean,
    override val watchSyncState: RouteWatchSyncState,
    override val watchSyncResponseCode: Int,
    override val isWatchRouteListFull: Boolean,
    override val modifiedDate: Long,
    override val createdDate: Long,
    override val turnWaypointsEnabled: Boolean,
    val topRoute: TopRoute,
    val isFavoriteSaved: Boolean,
) : LibraryRouteItem(
    name,
    listOf(ActivityType.valueOf(topRoute.activityId)),
    totalDistance,
    ascent,
    descent,
    estimatedDuration,
    averageSpeed,
    watchEnabled,
    distanceFromCurrentLocation,
    key,
    routerId,
    segmentsModifiedDate,
    isSyncable,
    watchSyncState,
    watchSyncResponseCode,
    isWatchRouteListFull,
    modifiedDate,
    createdDate,
    turnWaypointsEnabled
) {
    var isLatestFavoriteSaved: Boolean = isFavoriteSaved
}

data class MyRouteItem(
    override val name: String,
    override val activityIds: List<ActivityType>,
    override val totalDistance: Double,
    override val ascent: Double,
    override val descent: Double,
    override val estimatedDuration: Long,
    override val averageSpeed: Double,
    override val watchEnabled: Boolean,
    override val distanceFromCurrentLocation: Double?,
    override val key: String?,
    override val routerId: String,
    override val segmentsModifiedDate: Long,
    override val isSyncable: Boolean,
    override val watchSyncState: RouteWatchSyncState,
    override val watchSyncResponseCode: Int,
    override val isWatchRouteListFull: Boolean,
    override val modifiedDate: Long,
    override val createdDate: Long,
    override val turnWaypointsEnabled: Boolean,
) : LibraryRouteItem(
    name,
    activityIds,
    totalDistance,
    ascent,
    descent,
    estimatedDuration,
    averageSpeed,
    watchEnabled,
    distanceFromCurrentLocation,
    key,
    routerId,
    segmentsModifiedDate,
    isSyncable,
    watchSyncState,
    watchSyncResponseCode,
    isWatchRouteListFull,
    modifiedDate,
    createdDate,
    turnWaypointsEnabled
)
