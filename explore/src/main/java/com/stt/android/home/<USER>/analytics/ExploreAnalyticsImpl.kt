package com.stt.android.home.explore.analytics

import androidx.lifecycle.LifecycleCoroutineScope
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_ROUTES_COUNT
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_ROUTES_IN_WATCH_COUNT
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.MapFeatureChanged.MAP_3D_MODE
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.explore.pois.GetAllPOIsUseCase
import com.stt.android.domain.routes.RouteRepository
import com.stt.android.home.explore.ExploreAnalytics
import com.stt.android.models.MapSelectionModel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

class ExploreAnalyticsImpl @Inject constructor(
    private val routeRepository: RouteRepository,
    private val mapSelectionModel: MapSelectionModel,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val getAllPOIsUseCase: GetAllPOIsUseCase
) : ExploreAnalytics {

    override fun trackMapScreen(source: String) {
        val properties = getMapOptionProperties()
            .put(AnalyticsEventProperty.SOURCE, source)

        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.MAP_SCREEN, properties)
        emarsysAnalytics.trackEventWithProperties(
            AnalyticsEvent.MAP_SCREEN,
            properties.map
        )
    }

    override fun trackCommunityScreen() {
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.MAP_EXPLORE_VIEW)
        emarsysAnalytics.trackEvent(AnalyticsEvent.MAP_EXPLORE_VIEW)
    }

    override fun trackMapChangeMode(
        source: String,
        mapFeatureChanged: String
    ) {
        val properties = getMapOptionProperties()
            .put(AnalyticsEventProperty.SOURCE, source)
            .put(AnalyticsEventProperty.MAP_FEATURE_CHANGED, mapFeatureChanged)

        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.MAP_CHANGE_MODE, properties)
        emarsysAnalytics.trackEventWithProperties(
            AnalyticsEvent.MAP_CHANGE_MODE,
            properties.map
        )
        firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.MAP_CHANGE_MODE, properties)
    }

    override fun trackMap3dModeChange(source: String, inputMethod: String) {
        val properties = getMapOptionProperties()
            .put(AnalyticsEventProperty.SOURCE, source)
            .put(AnalyticsEventProperty.MAP_FEATURE_CHANGED, MAP_3D_MODE)
            .put(AnalyticsEventProperty.MAP_3D_MODE_INPUT_METHOD, inputMethod)

        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.MAP_CHANGE_MODE, properties)
        emarsysAnalytics.trackEventWithProperties(
            AnalyticsEvent.MAP_CHANGE_MODE,
            properties.map
        )
        firebaseAnalyticsTracker.trackEvent(AnalyticsEvent.MAP_CHANGE_MODE, properties)
    }

    override fun trackPOILibraryScreen(lifecycleScope: LifecycleCoroutineScope) {
        lifecycleScope.launch {
            runCatching {
                val numberOfPOIs = getAllPOIsUseCase.countPOIs()
                amplitudeAnalyticsTracker.trackEvent(
                    AnalyticsEvent.POI_LIBRARY_SCREEN,
                    AnalyticsProperties().put(
                        AnalyticsEventProperty.NUMBER_OF_POIS,
                        numberOfPOIs
                    )
                )
            }.onFailure { Timber.w(it, "Failure counting POIs") }
        }
    }

    override fun trackRoutesLibraryScreen(lifecycleScope: LifecycleCoroutineScope) {
        lifecycleScope.launch {
            runSuspendCatching {
                with(AnalyticsProperties()) {
                    val routeCount = routeRepository.getRouteCount()
                    val watchEnabledRouteCount = routeRepository.getWatchEnabledRouteCount()
                    put(SUUNTO_ROUTES_COUNT, routeCount)
                    put(SUUNTO_ROUTES_IN_WATCH_COUNT, watchEnabledRouteCount)
                    amplitudeAnalyticsTracker.trackEvent(
                        AnalyticsEvent.ROUTE_MY_ROUTES_SCREEN,
                        this
                    )
                    emarsysAnalytics.trackEventWithProperties(
                        AnalyticsEvent.ROUTE_MY_ROUTES_SCREEN,
                        this.map
                    )
                }
            }.onFailure { Timber.w(it, "Failure counting POIs") }
        }
    }

    private fun getMapOptionProperties(): AnalyticsProperties {
        val mapType = mapSelectionModel.selectedMapType
        val map3dMode = mapSelectionModel.map3dEnabled
        val heatmapType = mapSelectionModel.selectedHeatmap
        val roadSurfaceTypes = mapSelectionModel.selectedRoadSurfaces
        val hideCyclingForbiddenRoads = mapSelectionModel.hideCyclingForbiddenRoads
        val myTracksGranularity = mapSelectionModel.selectedMyTracksGranularity

        return AnalyticsProperties()
            .put(AnalyticsEventProperty.MAP_MODE, mapType.analyticsName)
            .putOnOff(AnalyticsEventProperty.MAP_3D_MODE, map3dMode)
            .put(
                AnalyticsEventProperty.MAP_HEATMAP_TYPE,
                heatmapType?.analyticsName ?: AnalyticsPropertyValue.MAP_NO_HEATMAP
            )
            .put(
                AnalyticsEventProperty.MAP_ROAD_SURFACE_LAYER,
                roadSurfaceTypes.takeIf { it.isNotEmpty() }
                    ?.map { it.analyticsName }?.joinToString("+")
                    ?: AnalyticsPropertyValue.MAP_NO_ROAD_SURFACE
            )
            .putYesNo(
                AnalyticsEventProperty.MAP_HIDE_CYCLING_FORBIDDEN_ROADS,
                hideCyclingForbiddenRoads
            )
            .put(
                AnalyticsEventProperty.MAP_MYTRACKS,
                myTracksGranularity?.type?.analyticsPropertyValue ?: AnalyticsPropertyValue.OFF
            )
    }
}
