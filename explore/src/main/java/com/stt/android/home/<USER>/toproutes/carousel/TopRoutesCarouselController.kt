package com.stt.android.home.explore.toproutes.carousel

import android.content.SharedPreferences
import android.os.Handler
import android.os.Looper
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.coroutineScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.SCROLL_STATE_DRAGGING
import androidx.recyclerview.widget.RecyclerView.SCROLL_STATE_IDLE
import com.airbnb.epoxy.Carousel
import com.airbnb.epoxy.CarouselModelBuilder
import com.airbnb.epoxy.EpoxyModel
import com.airbnb.epoxy.VisibilityState
import com.airbnb.epoxy.carousel
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.di.FragmentLifecycle
import com.stt.android.domain.routes.GetTopRouteUseCase
import com.stt.android.domain.routes.TopRouteRepository
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.home.explore.R
import com.stt.android.home.explore.noTopRoutes
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.maps.mapbox.googleZoomToMapbox
import com.stt.android.ui.map.MapHelper.TOP_ROUTES_MIN_ZOOM
import com.stt.android.utils.STTConstants
import javax.inject.Inject

class TopRoutesCarouselController @Inject constructor(
    private val infoModelFormatter: InfoModelFormatter,
    private val getTopRouteUseCase: GetTopRouteUseCase,
    private val topRouteRepository: TopRouteRepository,
    private val unit: MeasurementUnit,
    @FeatureTogglePreferences private val featureTogglePreferences: SharedPreferences,
    @FragmentLifecycle private val fragmentLifecycle: Lifecycle
) : ViewStateEpoxyController<TopRoutesCarouselContainer?>() {

    private val topRoutesFeaturesEnabled: Boolean
        get() = featureTogglePreferences.getBoolean(
            STTConstants.FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES,
            STTConstants.FeatureTogglePreferences.KEY_ENABLE_TOP_ROUTE_FEATURES_DEFAULT
        )

    private var scrollListener: RecyclerView.OnScrollListener? = null

    override fun buildModels(viewState: ViewState<TopRoutesCarouselContainer?>) {
        viewState.data?.let { data ->
            if (data.features.isNotEmpty()) {
                addRouteCarousel(data)
            } else {
                addNoRoutesCard(googleZoomToMapbox(data.zoomLevel.toFloat()))
            }
        }
    }

    private fun addRouteCarousel(
        data: TopRoutesCarouselContainer
    ) {
        carousel {
            id("carousel")
            padding(Carousel.Padding.dp(16, 8, 16, 16, 16))
            hasFixedSize(true)
            withModelsFrom(data.features) { index, feature ->
                <EMAIL>(data, index, feature)
            }
            onBind { _, view, _ ->
                with(this@TopRoutesCarouselController) {
                    updateCarouselPosition(view, data)
                    // We cannot clear all listeners or it will break snap to item in carousel
                    scrollListener?.run { view.removeOnScrollListener(this) }
                    scrollListener = createScrollListener(data)
                    scrollListener?.run { view.addOnScrollListener(this) }
                }
            }
        }
    }

    private fun updateCarouselPosition(
        view: Carousel,
        data: TopRoutesCarouselContainer
    ) {
        Handler(Looper.getMainLooper()).postDelayed({
            val indexOfSelected = data.features.indexOfFirst { it.selected }
            val position = if (indexOfSelected == -1) 0 else indexOfSelected
            view.smoothScrollToPosition(position)
            data.onScrolled(
                CarouselEvent(
                    CarouselEventType.ON_SCROLLED,
                    data.features[position],
                    data.suuntoFeatures[position],
                    notifyMapMove = false,
                    routePosition = position
                )
            )
        }, 300)
    }

    private fun createScrollListener(
        data: TopRoutesCarouselContainer
    ): RecyclerView.OnScrollListener {
        return object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(
                recyclerView: RecyclerView,
                newState: Int
            ) {
                super.onScrollStateChanged(recyclerView, newState)
                when (newState) {
                    SCROLL_STATE_DRAGGING -> {
                        data.onScrolled(
                            CarouselEvent(CarouselEventType.ON_DRAGGED)
                        )
                    }
                    SCROLL_STATE_IDLE -> {
                        val index = recyclerView.currentPosition()
                        if (index >= 0) {
                            data.onScrolled(
                                CarouselEvent(
                                    CarouselEventType.ON_SCROLLED,
                                    data.features[index],
                                    data.suuntoFeatures[index],
                                    notifyMapMove = true,
                                    routePosition = index,
                                    awayYouDistance = data.features[index].awayYouDistance
                                )
                            )
                        }
                    }
                    else -> {
                        // do nothing
                    }
                }
            }
        }
    }

    private fun RecyclerView?.currentPosition(): Int {
        return (this?.layoutManager as? LinearLayoutManager)
            ?.findFirstCompletelyVisibleItemPosition()
            ?: -1
    }

    private fun createTopRouteModel(
        data: TopRoutesCarouselContainer,
        index: Int,
        feature: RouteFeature
    ): EpoxyModel<*> {
        return TopRouteModel_()
            .id(feature.routeId)
            .feature(feature)
            .infoModelFormatter(infoModelFormatter)
            .suuntoFeature(data.suuntoFeatures[index])
            .getTopRouteUseCase(getTopRouteUseCase)
            .topRouteRepository(topRouteRepository)
            .unit(unit)
            .lifecycleScope(fragmentLifecycle.coroutineScope)
            .onRouteLoaded(data.onRouteLoaded)
            .topRouteFeatureEnabled(topRoutesFeaturesEnabled)
            .clickListener { model, _, _, position ->
                data.onCardClicked(
                    CarouselEvent(
                        CarouselEventType.ON_CARD_CLICKED,
                        model.feature(),
                        model.suuntoFeature(),
                        notifyMapMove = true,
                        routePosition = position,
                        awayYouDistance = model.feature().awayYouDistance
                    )
                )
            }
            .onVisibilityStateChanged { model, _, visibilityState ->
                if (visibilityState == VisibilityState.FOCUSED_VISIBLE) {
                    data.onRouteVisible(model.feature().routeId)
                }
            }
    }

    private fun addNoRoutesCard(zoomLevel: Double) {
        val title =
            if (zoomLevel < TOP_ROUTES_MIN_ZOOM) R.string.zoom_level_low_card_label else R.string.no_popular_routes_card_label
        val message =
            if (zoomLevel < TOP_ROUTES_MIN_ZOOM) R.string.zoom_level_low_card_msg else R.string.no_popular_routes_card_msg
        noTopRoutes {
            id("no top routes")
            title(title)
            message(message)
        }
    }
}

inline fun <T> CarouselModelBuilder.withModelsFrom(
    items: List<T>,
    modelBuilder: (Int, T) -> EpoxyModel<*>
) {
    models(items.mapIndexed { index, feature -> modelBuilder(index, feature).show() })
}
