package com.stt.android.home.explore.toproutes.filter

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.BundleCompat
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.google.android.gms.maps.model.LatLng
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.controllers.UserSettingsController
import com.stt.android.home.explore.R
import com.stt.android.home.explore.databinding.FragmentTopRoutesFilterBinding
import com.stt.android.home.explore.routes.popular.BasePopularRouteDetailsActivity
import com.stt.android.home.explore.toproutes.TopRoutesSharedViewModel
import com.stt.android.home.explore.toproutes.carousel.RouteFeature
import com.stt.android.home.explore.weather.WeatherInfoFragment
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class TopRoutesFilterFragment : SmartBottomSheetDialogFragment() {

    private val topRouteFilterViewModel: TopRoutesFilterViewModel by viewModels()
    private val topRoutesSharedViewModel: TopRoutesSharedViewModel by activityViewModels()

    @Inject
    lateinit var userSettingController: UserSettingsController

    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val binding = FragmentTopRoutesFilterBinding.inflate(inflater, container, false)
        binding.composeView.setContentWithM3Theme {
            TopRoutesFilterBottomScreen(
                viewModel = topRouteFilterViewModel,
                sharedViewModel = topRoutesSharedViewModel,
                measurementUnit = userSettingController.settings.measurementUnit,
                infoModelFormatter = infoModelFormatter,
                onItemClick = ::handleItemClickEvent
            )
        }
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        val dialog = dialog as? BottomSheetDialog ?: return
        val bottomSheet =
            dialog.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
                ?: return

        val targetHeight = (resources.displayMetrics.heightPixels * 0.8).toInt()
        bottomSheet.layoutParams = bottomSheet.layoutParams.apply {
            height = targetHeight
        }
        bottomSheet.requestLayout()

        val behavior = BottomSheetBehavior.from(bottomSheet)
        behavior.state = BottomSheetBehavior.STATE_EXPANDED
        behavior.skipCollapsed = true
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        updateWeatherInfo()
    }

    private fun updateWeatherInfo() {
        arguments?.let {
            BundleCompat.getParcelable(it, EXTRA_KEY_CURRENT_CAMERA_LOCATION, LatLng::class.java)
                ?.let { latLng ->
                    getWeatherInfoFragment()?.setLocation(latLng)
                }
        }
    }

    private fun getWeatherInfoFragment(): WeatherInfoFragment? {
        return if (isAdded) {
            (childFragmentManager.findFragmentById(R.id.weatherInfoFragment) as WeatherInfoFragment?)
        } else {
            null
        }
    }

    private fun handleItemClickEvent(routeFeature: RouteFeature) {
        startActivity(
            BasePopularRouteDetailsActivity.newStartIntent(
                requireContext(),
                routeFeature.routeId,
                routeFeature.activityType.id,
                routeFeature.awayYouDistance
            )
        )
    }

    companion object {
        private const val EXTRA_KEY_CURRENT_CAMERA_LOCATION =
            "com.stt.android.home.explore.toproutes.filter.CAMERA_LOCATION"
        const val EXTRA_KEY_CURRENT_USER_LOCATION =
            "com.stt.android.home.explore.toproutes.filter.USER_LOCATION"

        fun newInstance(
            cameraPosition: LatLng?,
            currentPosition: LatLng?
        ): TopRoutesFilterFragment =
            TopRoutesFilterFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(EXTRA_KEY_CURRENT_CAMERA_LOCATION, cameraPosition)
                    putParcelable(EXTRA_KEY_CURRENT_USER_LOCATION, currentPosition)
                }
            }
    }
}
