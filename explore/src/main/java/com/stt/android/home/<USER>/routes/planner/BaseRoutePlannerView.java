package com.stt.android.home.explore.routes.planner;

import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;
import com.soy.algorithms.climbanalysis.entities.ClimbGuidance;
import com.stt.android.domain.explore.pois.POI;
import com.stt.android.domain.routes.Route;
import com.stt.android.domain.routes.RouteSegment;
import com.stt.android.domain.user.HeatmapType;
import com.stt.android.domain.user.RoadSurfaceType;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.home.explore.routes.RouteAltitudeChartData;
import com.stt.android.home.explore.routes.planner.actionresult.AddWaypointsActionResult;
import com.stt.android.home.explore.routes.planner.actionresult.EditWaypointActionResult;
import com.stt.android.maps.MapType;
import com.stt.android.maps.SuuntoCameraOptions;
import com.stt.android.views.MVPView;
import java.util.List;

interface BaseRoutePlannerView extends MVPView {
    void moveMapTo(double latitude, double longitude);

    void moveMapTo(double latitude, double longitude, float zoom);

    void moveMapTo(SuuntoCameraOptions cameraOptions, int delayMs);

    void moveMapTo(LatLngBounds latLngBounds, int delayMs);

    void myLocationNotAvailable();

    void errorSavingRoute();

    void errorImportingRoute();

    void showSegments(List<RouteSegment> segments, boolean updatePreviousSegment);

    void setRouteDistance(String formattedDistance, @StringRes int distanceUnitRes);

    void setRouteAscent(String ascent, @StringRes int altitudeUnitRes);

    void setRouteDescent(String descent, @StringRes int altitudeUnitRes);

    void updateAltitudeChart(@NonNull RouteAltitudeChartData chartData, @NonNull ClimbGuidance climbGuidance);

    void hideAscentDescentInfo();

    void setRouteDurationAndSpeed(String formattedDuration, String avgSpeed,
        @StringRes int speedUnitRes);

    void removeSegments(List<RouteSegment> removedSegments, boolean updateLatestSegment);

    void addWaypoint(@NonNull AddWaypointsActionResult result);

    void editWaypoint(@NonNull EditWaypointActionResult result);

    void removeWaypoint(@NonNull AddWaypointsActionResult result);

    void undoEditWaypoint(@NonNull EditWaypointActionResult result);

    void invalidRouteName();

    void savingRoute();

    void routeSaved(String routeName);

    void addPointFailed(boolean internetConnectionNeeded);

    void failedToAddInitialSegment(boolean internetConnectionNeeded);

    void movePointFailed();

    void onRoutingChanged(@NonNull RoutingMode routingMode);

    void onEditRoutePlanningStarted();

    void updateTurnByTurnMarkerVisibility(boolean visible);

    void onTurnByTurnWaypointsEnabledChanged(boolean enabled, boolean byTheUser);

    void highlightRouteByIndex(ClimbGuidance climbGuidance, int segmentIndex, int pointIndex);

    void highlightRouteByIndex(List<RouteSegment> routeSegments, int segmentIndex, int pointIndex);

    void resetRouteToDefault(List<RouteSegment> routeSegments);

    void showStartPoint(double latitude, double longitude);

    void showPendingWaypoint(LatLng latLng);

    void removeStartPoint();

    void activitiesChanged(List<ActivityType> newActivities);

    void resetState();

    void setRouteName(String name);

    void creatingNewRoute();

    void creatingCopyRoute();

    void editingRoute(String routeName);

    void moveStartPoint(LatLng point);

    void onFetchingRouteStarted();

    void onFetchingRouteCompleted();

    void showWaypointsIgnoredIfNeeded(int ignoredWaypoints);

    void onFindNearestPointsCompleted();

    void onFindWaypointsCompleted();

    void onMaxTurnByTurnWaypointCount();

    void onMaxWaypointCount();

    void onRouteUpdatedFully();

    void showPOIs(List<POI> pois);

    boolean hasAnyLocationPermission();

    boolean hasLocationPermissions();

    void toggleAddToWatch(Route route, boolean watchRouteListFull);

    MapType getCurrentMapType();

    HeatmapType getCurrentHeatmapType();

    List<RoadSurfaceType> getCurrentRoadSurfaceTypes();

    boolean getHideCyclingForbiddenRoads();
}
