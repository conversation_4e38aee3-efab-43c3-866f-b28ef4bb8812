<?xml version="1.0" encoding="utf-8"?>
<resources>


    <!-- TEXT INPUT FIELDS -->
    <style name="TextInputField" parent="BaseTextInputField">
        <item name="android:fontFamily">@font/proximanova_regular</item>
        <item name="android:colorControlActivated">@color/secondary_accent</item>
    </style>

    <style name="TextInputField.Largetext">
        <item name="android:textSize">@dimen/text_size_xxlarge</item>
    </style>

    <style name="TextInputField.SearchField" parent="TextInputField">
        <item name="android:backgroundTint">@android:color/transparent</item>
    </style>

    <style name="HelperTextAppearance" parent="BaseHelperTextAppearance">
        <item name="android:fontFamily">@font/proximanova_regular</item>
    </style>

    <!-- SPINNERS -->

    <style name="SpinnerTheme">
        <item name="android:spinnerItemStyle">@style/SpinnerItem</item>
        <item name="android:spinnerDropDownItemStyle">@style/SpinnerDropDownItem</item>
    </style>

    <style name="Spinner">
        <item name="android:theme">@style/SpinnerTheme</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="DefaultSpinnerItem" parent="BaseSpinnerItem">
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="SpinnerItem" parent="DefaultSpinnerItem"/>

    <style name="SpinnerDropDownItem" parent="BaseSpinnerDropDownItem">
        <item name="android:fontFamily">@font/proximanova_regular</item>
    </style>


    <!-- BOTTOM NAVIGATION BAR -->

    <style name="BottomNaviTheme" parent="BaseBottomNaviTheme">
        <item name="colorPrimary">@color/accent</item>
    </style>

    <style name="BottomNavi" parent="BaseBottomNavi">
        <item name="android:elevation">@dimen/elevation_navbar</item>
        <item name="android:theme">@style/BottomNaviTheme</item>
    </style>


    <!-- TAB BAR -->

    <style name="TabBarTheme">
        <item name="android:textColorSecondary">@color/accent</item>
    </style>

    <style name="DefaultTabBarTextStyle" parent="BaseTabBarTextStyle">
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="TabBarTextStyle" parent="DefaultTabBarTextStyle"/>

    <style name="TabBar" parent="BaseTabBar">
        <item name="tabTextAppearance">@style/TabBarTextStyle</item>
    </style>

    <style name="CalendarTabBarTextStyle" parent="DefaultTabBarTextStyle">
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">@dimen/text_size_larger</item>
        <item name="android:fontFamily">@font/proximanova_bold</item>
    </style>

    <style name="CalendarDateRangeTabBarTextStyle" parent="BaseTabBarTextStyle">
        <item name="android:fontFamily">@font/proximanova_regular</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">@color/item_tab_calendar_date_range</item>
    </style>

    <style name="CalendarTabBar" parent="BaseTabBar">
        <item name="tabTextAppearance">@style/CalendarTabBarTextStyle</item>
    </style>

    <!-- CUSTOM PROGRESS BAR -->

    <!-- This style is used only for progress bars that represent data, and is not intended to replace regular linear progress bars. -->
    <style name="ProgressBarHorizontal" parent="Base.Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:progressDrawable">@drawable/rounded_progress_bar</item>
        <!-- See v21 values -->
    </style>


    <!-- MISCELLANEOUS -->

    <!-- Checkbox & radiobutton & switch generic style -->
    <style name="RadioCheckSwitchStyle">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:fontFamily">@font/proximanova_regular</item>
        <item name="android:paddingTop">1dp</item>
        <item name="android:textSize">@dimen/text_size_medium</item>
    </style>

    <!-- Workout widget offset for ST variant -->
    <dimen name="workout_widget_offset">0dp</dimen>

    <style name="ChipTextAppearance" parent="TextAppearance.MaterialComponents.Chip">
        <item name="android:fontFamily">@font/proximanova_regular</item>
    </style>

</resources>
