package com.stt.android.home.diary;

import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;
import com.google.android.material.tabs.TabLayout;
import com.stt.android.analytics.AmplitudeAnalyticsTracker;
import com.stt.android.analytics.AnalyticsEvent;
import com.stt.android.analytics.AnalyticsEventProperty;
import com.stt.android.analytics.AnalyticsProperties;
import com.stt.android.analytics.EmarsysAnalytics;
import com.stt.android.common.ui.ViewPagerFragmentCreator;
import com.stt.android.databinding.DiaryFragmentBinding;
import com.stt.android.di.DiaryPagePreferences;
import com.stt.android.di.FeatureTogglePreferences;
import com.stt.android.home.HomeTab;
import static com.stt.android.home.diary.DiaryPagerAdapter.PAGE_LIST;
import static com.stt.android.home.diary.DiaryPagerAdapter.PAGE_SUMMARY;
import static com.stt.android.home.diary.DiaryPagerAdapter.PAGE_TSS_ANALYSIS;
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer;
import dagger.hilt.android.AndroidEntryPoint;
import java.io.Serializable;
import javax.inject.Inject;
import javax.inject.Named;

@AndroidEntryPoint
public class DiaryFragment extends Fragment implements ViewPager.OnPageChangeListener, HomeTab {
    public static final String FRAGMENT_TAG =
        "com.stt.android.home.diary.DiaryFragment.FRAGMENT_TAG";
    private static final String ARG_SHOW_TAB_TYPE =
        "com.stt.android.home.diary.DiaryFragment.ARG_SHOW_TAB_TYPE";
    private static final String STATE_INITIAL_PAGE_SET =
        "com.stt.android.home.diary.DiaryFragment.ARG_SHOW_CALENDAR";

    private DiaryFragmentBinding binding;

    @Inject
    EmarsysAnalytics emarsysAnalytics;

    @Inject
    AmplitudeAnalyticsTracker amplitudeAnalyticsTracker;

    @Inject
    @DiaryPagePreferences
    SharedPreferences diaryPagePreferences;

    @Inject
    @FeatureTogglePreferences
    SharedPreferences featureTogglePreferences;

    @Inject
    SharedPreferences defaultSharedPreferences;

    @Inject
    @Named("TSS_FRAGMENT")
    ViewPagerFragmentCreator tssAnalysisFragmentCreator;

    @Inject
    @Named("TRAINING_ZONE_SUMMARY_FRAGMENT_CREATOR")
    ViewPagerFragmentCreator trainingZoneSummaryFragmentCreator;

    private DiaryPagerAdapter adapter;
    private boolean initialPageSet = false;

    public static DiaryFragment newInstance(@Nullable Diary.TabType showTabType) {
        DiaryFragment fragment = new DiaryFragment();
        Bundle args = new Bundle();
        args.putSerializable(ARG_SHOW_TAB_TYPE, showTabType);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
        Bundle savedInstanceState) {
        binding = DiaryFragmentBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);

        if (savedInstanceState != null) {
            initialPageSet = savedInstanceState.getBoolean(STATE_INITIAL_PAGE_SET, false);
        }

        adapter = new DiaryPagerAdapter(
            getChildFragmentManager(),
            getResources(),
            tssAnalysisFragmentCreator,
            trainingZoneSummaryFragmentCreator
        );

        binding.viewPager.setAdapter(adapter);
        setupInitialPage();
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        binding.appbar.setExpanded(false);
        binding.viewPager.addOnPageChangeListener(this);
        binding.tabs.setupWithViewPager(binding.viewPager);
        binding.tabs.setTabMode(TabLayout.MODE_SCROLLABLE);
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putBoolean(STATE_INITIAL_PAGE_SET, initialPageSet);
    }

    @Override
    public void onStart() {
        super.onStart();

        saveLastUsedTab(binding.viewPager.getCurrentItem());
    }

    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        // do nothing
    }

    @Override
    public void onPageSelected(int position) {
        trackEvent(position);
        saveLastUsedTab(position);
    }

    public void saveLastUsedTab(int position) {
        SharedPreferences.Editor editor = diaryPagePreferences.edit();
        editor.putInt(DiaryPagePreferencesKeys.DIARY_LAST_USED_TAB, position);
        editor.apply();
    }

    public void trackEvent(int position) {
        switch (position) {
            case PAGE_LIST:
                emarsysAnalytics.trackEvent(AnalyticsEvent.DIARY_SCREEN);
                amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.DIARY_SCREEN);
                break;
            case DiaryPagerAdapter.PAGE_SUMMARY:
                amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.TRAINING_ZONE_SUMMARY_SCREEN);
                break;
        }
    }

    @Override
    public void onPageScrollStateChanged(int state) {
        // do nothing
    }

    @Override
    public void moveTo(int position) {
        Fragment fragment = adapter.getFragment(binding.viewPager.getCurrentItem());
        if (fragment instanceof FilterableExpandableListFragment) {
            ((FilterableExpandableListFragment) fragment).getExpandableListView().setSelection(0);
        }
    }

    private void setupInitialPage() {
        if (!initialPageSet) {
            Bundle args = getArguments();
            int position;

            Serializable showTabTypeArg = args != null
                ? args.getSerializable(ARG_SHOW_TAB_TYPE)
                : null;

            if (showTabTypeArg == Diary.TabType.WORKOUTS) {
                position = PAGE_LIST;
            } else if (showTabTypeArg == Diary.TabType.PROGRESS) {
                position = PAGE_TSS_ANALYSIS;
            } else if (showTabTypeArg == Diary.TabType.SUMMARY) {
                position = PAGE_SUMMARY;
            } else {
                position = diaryPagePreferences.getInt(
                    DiaryPagePreferencesKeys.DIARY_LAST_USED_TAB, -1);
            }

            if (position >= 0 && position < adapter.getCount()) {
                binding.viewPager.setCurrentItem(position, false);
            }

            initialPageSet = true;
        }
    }

    private void trackDiaryCalendarAnalytics() {
        String lastUsedGranularity = diaryPagePreferences.getString(
            DiaryPagePreferencesKeys.DIARY_CALENDAR_LAST_USED_GRANULARITY,
            null
        );

        DiaryCalendarListContainer.Granularity granularity =
            DiaryCalendarListContainer.Granularity.Companion
                .valueOfStringOrNull(lastUsedGranularity);

        if (granularity == null) {
            granularity = DiaryCalendarListContainer.Granularity.MONTH;
        }

        AnalyticsProperties properties = new AnalyticsProperties();
        properties.put(
            AnalyticsEventProperty.CALENDAR_LEVEL,
            granularity.getAnalyticsPropertyValue());

        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.DIARY_CALENDAR_SCREEN, properties);
    }
}
