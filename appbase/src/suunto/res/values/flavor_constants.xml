<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--This file contains flavor specific constants-->
    <string name="app_deeplink_url_scheme" translatable="false">com.sports-tracker.suunto</string>
    <item name="showFriendsToFollowPopup" type="bool">false</item>
    <item name="flavorSupportsSubscriptions" type="bool">false</item>
    <item name="hideAds" type="bool">true</item>
    <item name="suuntoFlavorSpecific" type="bool">true</item>
    <item name="sportsTrackerFlavorSpecific" type="bool">false</item>
    <item name="supportsCustomInbox" type="bool">false</item>
    <item name="gear_events_enabled" type="bool">true</item>
    <item name="location_search_enabled" type="bool">true</item>
    <item name="supports_invite_friends_in_app" type="bool">false</item>

    <!--
    If set to true, photos are shared using the Facebook SDK. Otherwise plain sharing intent
    is used. Using the SDK requires registering FacebookContentProvider in the manifest. Multiple
    app(flavor)s can not register the content provider using the same Facebook app ID and co-exist
    on the same device.
    -->
    <item name="sharePhotosToFacebookUsingSdk" type="bool">false</item>

    <string name="app_channel_name" translatable="false">Suunto</string>
    <string name="feedback_url" translatable="false">https://app.suunto.com/feedback/user-feedback?lang=%1$s</string>
    <string name="invite_friends_share_link" translatable="false">https://marketing.suunto.com/h5/invite-friends?username=%1$s</string>
</resources>
