<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/toWatchSwitch"
            android:layout_marginBottom="@dimen/size_spacing_medium"
            android:layout_marginEnd="@dimen/size_spacing_medium"
            android:layout_marginTop="@dimen/size_spacing_medium"
            android:checked="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            style="@style/RadioCheckSwitchStyle" />

        <TextView
            android:id="@+id/toWatchText"
            style="@style/Body.Medium"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/size_spacing_medium"
            android:drawablePadding="@dimen/size_spacing_medium"
            android:gravity="center_vertical"
            android:text="@string/add_route_to_watch"
            android:textColor="@color/near_black"
            app:layout_constraintBottom_toBottomOf="@+id/toWatchSwitch"
            app:layout_constraintEnd_toStartOf="@id/toWatchSwitch"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/toWatchSwitch" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
