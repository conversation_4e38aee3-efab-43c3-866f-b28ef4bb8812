package com.stt.android.ui.activities.settings

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.edit
import com.stt.android.R
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.widgets.LoadingDialog
import com.stt.android.utils.STTConstants
import com.stt.android.watch.manage.ManageConnectionFragment
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import dagger.hilt.android.AndroidEntryPoint
import com.stt.android.core.R as CR

@AndroidEntryPoint
class ManageConnectionActivity : AppCompatActivity() {
    private val viewModel: ManageConnectViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            AppTheme {
                Scaffold(
                    topBar = {
                        ManageConnectionTopAppBar(onBackClick = {
                            onBackPressedDispatcher.onBackPressed()
                        })
                    }
                ) {
                    ContentCenteringColumn(
                        Modifier
                            .padding(it)
                            .background(colorResource(id = CR.color.white))
                    ) {
                        Content(
                            type = viewModel.deviceTypeLD.observeAsState(SuuntoDeviceType.Unrecognized),
                            onForgetClick = {
                                viewModel.onForget()
                            }
                        )
                        val showLoadingState = viewModel.showLoading.observeAsState()
                        if (showLoadingState.value == true) {
                            LoadingDialog(
                                hintText = stringResource(id = R.string.please_wait),
                                progressIndicatorColor = colorResource(
                                    id = R.color.suunto_blue_accent
                                )
                            )
                        }
                    }
                }
            }
        }
        viewModel.init()
        viewModel.unpairSuccess.observe(this) {
            setWatchInfoSharedPreferencesToDefaultValues()
            setResult(
                Activity.RESULT_OK,
                Intent().putExtra(ManageConnectionFragment.EXTRA_DEVICE_UNPAIRED, it)
            )
            finish()
        }
    }

    private fun setWatchInfoSharedPreferencesToDefaultValues() {
        getSharedPreferences(
            STTConstants.SuuntoPreferences.PREFS_NAME,
            Context.MODE_PRIVATE
        ).edit {
            putString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_FW_VERSION,
                STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_INFO_DEFAULT_VALUE
            )
            putString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_MODEL,
                STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_INFO_DEFAULT_VALUE
            )
            putString(
                STTConstants.SuuntoPreferences.KEY_SUUNTO_PAIRED_WATCH_SERIAL_NUMBER,
                STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_INFO_DEFAULT_VALUE
            )
            remove(
                STTConstants.SuuntoPreferences.KEY_SMOOTH_PAIRING_TRANSITION_DONE_DEVICE_NAMES
            )
        }
    }

    companion object {

        fun newIntent(context: Context): Intent {
            return Intent(context, ManageConnectionActivity::class.java)
        }
    }
}

@Composable
private fun Content(
    type: State<SuuntoDeviceType>,
    onForgetClick: (Int) -> Unit,
    modifier: Modifier = Modifier
) = Column(modifier = modifier) {
    val (title, instructions) = when {
        type.value.isAmbit3 -> {
            stringResource(R.string.manage_connection_title_legacy) to
                stringResource(R.string.manage_connection_forget_watch_steps_ambit3)
        }

        type.value.isTraverse -> {
            stringResource(R.string.manage_connection_title_legacy) to
                stringResource(R.string.manage_connection_forget_watch_steps_traverse)
        }

        type.value.isSuunto7 -> {
            val warning =
                stringResource(R.string.manage_connection_forget_watch_warning_suunto_7)
            stringResource(R.string.manage_connection_title_suunto7) to
                stringResource(
                    R.string.manage_connection_forget_watch_steps_suunto_7,
                    warning
                )
        }

        else -> {
            stringResource(R.string.manage_connection_title) to
                stringResource(R.string.manage_connection_forget_watch_steps)
        }
    }

    val buttonLabel = stringResource(
        if (type.value.isAmbit3 || type.value.isTraverse) {
            R.string.manage_connection_disconnect_watch_btn_text
        } else {
            R.string.manage_connection_forget_watch_btn_text
        }
    )

    Text(
        modifier = Modifier
            .background(color = colorResource(id = R.color.suunto_light_gray))
            .fillMaxWidth()
            .padding(16.dp, 12.dp),
        text = title,
        fontSize = 12.sp,
        fontFamily = FontFamily(Font(CR.font.proximanova_bold)),
    )
    Text(
        text = instructions,
        fontSize = 14.sp,
        modifier = Modifier.padding(16.dp, 38.dp, 16.dp, 32.dp)
    )
    ClickableText(
        modifier = Modifier
            .fillMaxWidth()
            .padding(12.dp),
        text = AnnotatedString(buttonLabel),
        style = TextStyle(
            color = colorResource(id = CR.color.bright_red),
            fontSize = 16.sp,
            fontFamily = FontFamily.SansSerif,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center,
        ),
        onClick = onForgetClick
    )
}

@Composable
private fun ManageConnectionTopAppBar(onBackClick: () -> Unit) {
    SuuntoTopBar(
        title = stringResource(R.string.watch_menu_manage_connection),
        onNavigationClick = onBackClick,
    )
}
