package com.stt.android.home.diary

import android.content.Context
import android.os.Bundle
import android.view.View
import android.widget.AdapterView
import android.widget.AdapterView.OnItemSelectedListener
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.view.ContextThemeWrapper
import androidx.core.content.edit
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.viewpager.widget.ViewPager
import com.google.android.material.tabs.TabLayout
import com.stt.android.R
import com.stt.android.ThemeColors
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.SuuntoDiaryGraphToggleSource.DIARY_SCREEN
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.analytics.TrendsAnalytics
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.common.ui.ViewPagerFragmentCreator
import com.stt.android.common.ui.observeNotNull
import com.stt.android.databinding.FragmentDiaryNewBinding
import com.stt.android.domain.diary.models.DiaryPage
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.diary.models.GraphGranularity
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.extensions.toLocalizable
import com.stt.android.inappreview.InAppReviewSource
import com.stt.android.inappreview.InAppReviewTrigger
import com.stt.android.ui.fragments.summaries.CustomDropdownArrayAdapter
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.schedulers.Schedulers
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Named

@AndroidEntryPoint
class DiaryFragment : ViewModelFragment2(), Diary {

    companion object {
        internal const val ARG_SHOW_TAB_TYPE =
            "com.stt.android.home.diary.DiaryFragment.ARG_SHOW_TAB_TYPE"
        private const val ARG_SHOW_PRIMARY_GRAPH_TYPE =
            "com.stt.android.home.diary.DiaryFragment.ARG_SHOW_PRIMARY_GRAPH_TYPE"
        private const val ARG_SHOW_SECONDARY_GRAPH_TYPE =
            "com.stt.android.home.diary.DiaryFragment.ARG_SHOW_SECONDARY_GRAPH_TYPE"
        private const val STATE_INITIAL_PAGE_SET =
            "com.stt.android.home.diary.DiaryFragment.STATE_INITIAL_PAGE_SET"
        private const val STATE_PREVIOUS_PAGE =
            "com.stt.android.home.diary.DiaryFragment.STATE_PREVIOUS_PAGE"
        private const val ARG_ANALYTICS_SOURCE =
            "com.stt.android.home.diary.DiaryFragment.ANALYTICS_SOURCE"

        @JvmStatic
        fun newInstance(
            showTabType: Diary.TabType?,
            primaryGraphType: GraphDataType?,
            secondaryGraphType: GraphDataType?,
            source: String?,
        ) = DiaryFragment().apply {
            arguments = bundleOf(
                ARG_SHOW_TAB_TYPE to showTabType,
                ARG_SHOW_PRIMARY_GRAPH_TYPE to primaryGraphType,
                ARG_SHOW_SECONDARY_GRAPH_TYPE to secondaryGraphType,
                ARG_ANALYTICS_SOURCE to source
            )
        }
    }

    private val viewDataBinding: FragmentDiaryNewBinding get() = requireBinding()

    private val compositeDisposable = CompositeDisposable()

    private var initialPageSet = false

    /**
     * Indicates whether user has just arrived to diary screen and it is opening the initial tab
     */
    private var initialTabChange = true

    @Inject
    lateinit var emarsysAnalytics: EmarsysAnalytics

    @Inject
    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    @Inject
    lateinit var trendsAnalytics: TrendsAnalytics

    @Inject
    @Named("TSS_FRAGMENT")
    lateinit var tssAnalysisFragmentCreator: ViewPagerFragmentCreator

    @Inject
    @Named("TRAINING_FRAGMENT_CREATOR")
    lateinit var trainingFragmentCreator: ViewPagerFragmentCreator

    @Inject
    @Named("FREE_DIVE_FRAGMENT_CREATOR")
    lateinit var freeDiveFragmentCreator: ViewPagerFragmentCreator

    @Inject
    @Named("SCUBA_DIVE_FRAGMENT_CREATOR")
    lateinit var scubaDiveFragmentCreator: ViewPagerFragmentCreator

    @Inject
    @Named("ACTIVITY_FRAGMENT_CREATOR")
    lateinit var activityFragmentCreator: ViewPagerFragmentCreator

    @Inject
    @Named("RECOVERY_FRAGMENT_CREATOR")
    lateinit var recoveryFragmentCreator: ViewPagerFragmentCreator

    @Inject
    lateinit var inAppReviewTrigger: InAppReviewTrigger

    @Inject
    @Named("INSIGHTS_FRAGMENT_CREATOR")
    lateinit var insightsFragmentCreator: ViewPagerFragmentCreator

    @Inject
    @Named("TRAINING_ZONE_SUMMARY_FRAGMENT_CREATOR")
    lateinit var trainingZoneSummaryFragmentCreator: ViewPagerFragmentCreator

    @Inject
    lateinit var trainingHubInfoSheetFragmentCreator: TrainingHubInfoSheetFragmentCreator

    override val viewModel: DiaryViewModel by viewModels()

    private var tabChangeSource: String? = null

    private var previousPage: DiaryPage? = null

    // This will be used to know what ever the user changed anything in order to mark the screen has visited for the in-app review flow
    private var userChangedSomething: Boolean = false

    private var onItemSelectedListener: OnItemSelectedListener? = null

    private var pageChangeListener: ViewPager.OnPageChangeListener? = null

    override fun getLayoutResId(): Int {
        return R.layout.fragment_diary_new
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupToolbar()

        if (initialTabChange) {
            arguments?.getString(ARG_ANALYTICS_SOURCE)?.let {
                tabChangeSource = it
            }
        }
        // We show fitness tab if user has ever enabled it
        compositeDisposable.add(
            viewModel.getPagerAdapterSetup()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeBy(
                    onSuccess = {
                        setupDefaultGranularity(it)
                        setupPagerAdapter(it)
                    }
                )
        )

        viewModel.openInfoBottomSheet.observeNotNull(viewLifecycleOwner) {
            val dialog = trainingHubInfoSheetFragmentCreator.createTrainingHubInfoSheetFragment(it)
            dialog.show(parentFragmentManager, it.name)
        }
    }

    override fun onDestroyView() {
        viewDataBinding.timeRangeSpinner.onItemSelectedListener = null
        onItemSelectedListener = null
        pageChangeListener?.let { viewDataBinding.viewpagerDiary.removeOnPageChangeListener(it) }
        pageChangeListener = null
        super.onDestroyView()
        if (userChangedSomething) {
            inAppReviewTrigger.incNumberOfVisitsForSource(InAppReviewSource.DIARY)
            inAppReviewTrigger.scheduleInAppReviewIfPossible()
        }

        compositeDisposable.clear()
    }

    private fun setupToolbar() {
        // todo Options should be defined elsewhere or use Enum.values()
        val options = listOf(
            GraphTimeRange.EIGHT_WEEKS,
            GraphTimeRange.EIGHT_MONTHS,
            GraphTimeRange.THIRTEEN_MONTHS,
            GraphTimeRange.EIGHT_YEARS
        )

        viewDataBinding.timeRangeSpinner.adapter = CustomDropdownArrayAdapter(
            requireContext(),
            options.map { it.toLocalizable() }
        )
        onItemSelectedListener = object : OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long
            ) {
                viewModel.updateSelectedTimeRange(options[position])
                userChangedSomething = true
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
            }
        }
        viewDataBinding.timeRangeSpinner.onItemSelectedListener = onItemSelectedListener
        val timeRange = viewModel.getSelectedTimeRange()
        viewDataBinding.timeRangeSpinner.setSelection(options.indexOf(timeRange))
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        savedInstanceState?.run {
            getBoolean(STATE_INITIAL_PAGE_SET, false).run {
                initialPageSet = this
            }
            (getSerializable(STATE_PREVIOUS_PAGE) as DiaryPage?)?.run {
                previousPage = this
            }
        }
    }

    override fun onStart() {
        super.onStart()

        emarsysAnalytics.trackEvent(AnalyticsEvent.DIARY_SCREEN)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.DIARY_SCREEN)

        handleTabChangeAnalyticsAfterBeingStopped()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        outState.putBoolean(STATE_INITIAL_PAGE_SET, initialPageSet)
        outState.putSerializable(STATE_PREVIOUS_PAGE, previousPage)
    }

    private fun setupDefaultGranularity(setup: DiaryPagerAdapterSetup) {
        if (setup.isEon) {
            val prefsName = DiaryPagePreferencesKeys.DIARY_PAGE_PREFS_NAME
            val prefsKey = DiaryPagePreferencesKeys.DIARY_PAGE_GRAPH_GRANULARITY

            // The default graph granularity should be MONTHLY if a dive device is paired. Set the default to
            // SharedPreferences here unless user has already touched the granularity setting.
            context?.getSharedPreferences(prefsName, Context.MODE_PRIVATE)?.let { prefs ->
                if (prefs.getString(prefsKey, null) == null) {
                    prefs.edit {
                        // Dive device paired and user has not touched the setting, use MONTHLY granularity
                        putString(prefsKey, GraphGranularity.MONTHLY.toString())
                    }
                }
            }
        }
    }

    private fun setupPagerAdapter(setup: DiaryPagerAdapterSetup) {
        /**
         * Without this check Suunto app will crash when
         * 1. Enable "Don't keep activities" from developer settings
         * 2. Open app, navigate to diary, open a workout
         * 3. Send the app to the background and back
         * 4. Navigate back from the workout details -> crash
         */
        if (!isAdded) return

        val adapter = DiaryPagerAdapterNew(
            childFragmentManager,
            setup,
            tssAnalysisFragmentCreator = tssAnalysisFragmentCreator,
            trainingFragmentCreator = trainingFragmentCreator,
            freeDiveFragmentCreator = freeDiveFragmentCreator,
            scubaDiveFragmentCreator = scubaDiveFragmentCreator,
            activityFragmentCreator = activityFragmentCreator,
            recoveryFragmentCreator = recoveryFragmentCreator,
            insightsFragmentCreator = insightsFragmentCreator,
            trainingZoneSummaryFragmentCreator = trainingZoneSummaryFragmentCreator
        )

        viewDataBinding.viewpagerDiary.adapter = adapter

        setupTabLayout(adapter)

        viewModel.navigationEvent.observeNotNull(viewLifecycleOwner) { tabType: Diary.TabType ->
            val suuntoDiaryType = tabType.toAnalyticsName()
            val index = adapter.getIndexByTabType(suuntoDiaryType)
            viewDataBinding.viewpagerDiary.setCurrentItem(index, true)
        }
        updateToolbarVisibility(adapter)
    }

    private fun setupTabLayout(adapter: DiaryPagerAdapterNew) {
        if (adapter.count > 1) {
            val tabLayout = viewDataBinding.tablayoutDiary
            tabLayout.setupWithViewPager(viewDataBinding.viewpagerDiary)
            tabLayout.isInlineLabel = true

            // We need to set tab icons programmatically after setting up adapter because
            // PagerAdapter doesn't support setting tab titles as icons
            val count = adapter.count
            val icons = adapter.getIconIds()
            val styles = adapter.getTabStyles()
            val contentDescriptions = adapter.getTabContentDescriptionStringResIds()
            tabLayout.setupTabsFromArray(count, icons, styles, contentDescriptions)

            viewDataBinding.viewpagerDiary.isNestedScrollingEnabled = true
            // Listen to view pager scroll events and update selected tab color accordingly
            pageChangeListener = object : ViewPager.OnPageChangeListener {
                override fun onPageScrollStateChanged(state: Int) = Unit
                override fun onPageSelected(position: Int) {
                    setSelectedTabIndicatorColorByIndex(styles, position)
                    handleTabChangeEffects(adapter, position)
                    trackTrendsViewChanged(adapter)
                    userChangedSomething = true
                }

                override fun onPageScrolled(
                    position: Int,
                    positionOffset: Float,
                    positionOffsetPixels: Int
                ) {
                    // When entering diary screen and the tab position is 0, the onPageSelected does
                    // not get called. So in that case only we need to send analytics from here.
                    if (initialTabChange) {
                        setSelectedTabIndicatorColorByIndex(styles, position)
                        if (position == 0) {
                            handleTabChangeEffects(adapter, position)
                        }
                    }
                }
            }
            // Listen to view pager scroll events and update selected tab color accordingly
            pageChangeListener?.let {
                viewDataBinding.viewpagerDiary.addOnPageChangeListener(it)
            }

            setupInitialPage(adapter)
        } else {
            viewDataBinding.tablayoutDiary.visibility = View.GONE
        }
    }

    private fun handleTabChangeEffects(adapter: DiaryPagerAdapterNew, position: Int) {
        val tabName = adapter.getTabTypes()[position]
        if (!initialTabChange) {
            tabChangeSource = DIARY_SCREEN
        }
        updateToolbarVisibility(adapter)
        viewModel.storeLastActivePage(tabName)
        initialTabChange = false
    }

    private fun updateToolbarVisibility(adapter: DiaryPagerAdapterNew) {
        try {
            val tabType = adapter.getTabTypes()[viewDataBinding.viewpagerDiary.currentItem]
            val index = adapter.getIndexByTabType(tabType)
            viewDataBinding.timeRangeSpinner.visibility = if (adapter.getHasTimeRanges()[index]) {
                View.VISIBLE
            } else {
                View.INVISIBLE
            }
        } catch (e: Exception) {
            Timber.w(
                e,
                "Attempts to update toolbar visibility for item ${viewDataBinding.viewpagerDiary.currentItem}"
            )
        }
    }

    private fun handleTabChangeAnalyticsAfterBeingStopped() {
        if (!initialTabChange) {
            val pager = viewDataBinding.viewpagerDiary
            val adapter = pager.adapter
            (adapter as? DiaryPagerAdapterNew)?.let {
                handleTabChangeEffects(it, pager.currentItem)
            }
        }
    }

    private fun setupInitialPage(adapter: DiaryPagerAdapterNew) {
        if (!initialPageSet) {
            (arguments?.getSerializable(ARG_SHOW_TAB_TYPE) as? Diary.TabType)?.let { tabType ->
                val suuntoDiaryType = tabType.toAnalyticsName()
                val index = adapter.getIndexByTabType(suuntoDiaryType)

                tabType.diaryPage?.let { page ->
                    val primaryGraph =
                        arguments?.getSerializable(ARG_SHOW_PRIMARY_GRAPH_TYPE) as? GraphDataType
                    val secondaryGraph =
                        arguments?.getSerializable(ARG_SHOW_SECONDARY_GRAPH_TYPE) as? GraphDataType
                    viewModel.setGraphTypesForPage(page, primaryGraph, secondaryGraph)
                }

                setupInitialPage(index)
            } ?: run {
                viewModel.loadLastActivePage()?.run {
                    setupInitialPage(adapter.getIndexByTabType(this))
                }
            }

            trackTrendsScreen(adapter)
            initialPageSet = true
        }
    }

    private fun trackTrendsScreen(adapter: DiaryPagerAdapterNew) {
        val tabType = adapter.getTabTypes()[viewDataBinding.viewpagerDiary.currentItem]
        diaryTabTypeFromAnalyticsName(tabType)?.diaryPage?.run {
            trendsAnalytics.trackTrendsScreens(
                this,
                viewModel.getSelectedTimeRange(),
                source = if (isOpenedFromBottomBar()) AnalyticsPropertyValue.TrendsScreenSource.NAVIGATION_BAR else null
            )
        }
    }

    private fun isOpenedFromBottomBar(): Boolean {
        // ARG_SHOW_TAB_TYPE null means DiaryFragment opened from the bottom bar
        val bundle = arguments
        return bundle != null &&
            (!bundle.containsKey(ARG_SHOW_TAB_TYPE) || bundle.getSerializable(ARG_SHOW_TAB_TYPE) == null)
    }

    private fun trackTrendsViewChanged(adapter: DiaryPagerAdapterNew) {
        val tabType = adapter.getTabTypes()[viewDataBinding.viewpagerDiary.currentItem]
        diaryTabTypeFromAnalyticsName(tabType)?.diaryPage?.run {
            if (previousPage != null) {
                trendsAnalytics.trackTrendsViewChanged(
                    this,
                    previousPage,
                    viewModel.getSelectedTimeRange()
                )
            }
            previousPage = this
        }
    }

    private fun setupInitialPage(index: Int) {
        viewDataBinding.viewpagerDiary.setCurrentItem(index, false)
    }

    private fun setSelectedTabIndicatorColorByIndex(styles: List<Int>, index: Int) {
        if (index >= 0 && index < styles.size) {
            val contextThemeWrapper = ContextThemeWrapper(context, styles[index])
            val selectedColor =
                ThemeColors.resolveColor(contextThemeWrapper, R.attr.suuntoDiaryAccentColor)
            viewDataBinding.tablayoutDiary.setSelectedTabIndicatorColor(selectedColor)
            // Set selected text color
            val defaultColor =
                ThemeColors.resolveColor(contextThemeWrapper, android.R.attr.textColorSecondary)
            viewDataBinding.tablayoutDiary.setTabTextColors(defaultColor, selectedColor)
        }
    }
}

/**
 * Extension function for setting [TabLayout] icons from list of icon ids'
 */
fun TabLayout.setupTabsFromArray(
    tabsCount: Int,
    icons: List<Int>,
    styles: List<Int>,
    contentDescriptions: List<Int>
) {
    for (i in 0..tabsCount) {
        val tab = getTabAt(i)

        tab?.let {
            val contextThemeWrapper = ContextThemeWrapper(context, styles[i])
            it.icon = AppCompatResources.getDrawable(contextThemeWrapper, icons[i])
            it.text = context.getString(contentDescriptions[i])
            it.contentDescription = context.getString(contentDescriptions[i])
        }
    }
}
