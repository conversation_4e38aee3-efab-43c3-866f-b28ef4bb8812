package com.stt.android.home.dashboardv2

import android.content.SharedPreferences
import android.os.Bundle
import android.view.View
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.AnalyticsPropertyValue.SuuntoDiaryGraphToggleSource.HOME_SCREEN_DASHBOARD_ACTIVITIES
import com.stt.android.analytics.AnalyticsPropertyValue.SuuntoDiaryGraphToggleSource.HOME_SCREEN_DASHBOARD_CALENDAR
import com.stt.android.analytics.AnalyticsPropertyValue.SuuntoDiaryGraphToggleSource.HOME_SCREEN_DASHBOARD_TOTAL_DURATION
import com.stt.android.chart.api.ChartNavigator
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.diary.DiaryNavigator
import com.stt.android.diary.recovery.RecoveryNavigator
import com.stt.android.domain.diary.models.GraphDataType
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.home.dashboardv2.widgets.ActivityDistanceWidgetInfo
import com.stt.android.home.dashboardv2.widgets.ActivityDurationWidgetInfo
import com.stt.android.home.dashboardv2.widgets.ActivitySpeedWidgetInfo
import com.stt.android.home.dashboardv2.widgets.ActivityTimesWidgetInfo
import com.stt.android.home.dashboardv2.widgets.ActivityWidgetInfo
import com.stt.android.home.dashboardv2.widgets.AscentWidgetInfo
import com.stt.android.home.dashboardv2.widgets.CalendarWidgetInfo
import com.stt.android.home.dashboardv2.widgets.CalorieWidgetInfo
import com.stt.android.home.dashboardv2.widgets.CommuteWidgetInfo
import com.stt.android.home.dashboardv2.widgets.DailyHeartRateWidgetInfo
import com.stt.android.home.dashboardv2.widgets.DurationWidgetInfo
import com.stt.android.home.dashboardv2.widgets.HrvWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Last7DaysHeartRateWidgetInfo
import com.stt.android.home.dashboardv2.widgets.MapWidgetInfo
import com.stt.android.home.dashboardv2.widgets.MenstrualPeriodWidgetInfo
import com.stt.android.home.dashboardv2.widgets.ProgressWidgetInfo
import com.stt.android.home.dashboardv2.widgets.RecoveryWidgetInfo
import com.stt.android.home.dashboardv2.widgets.ResourceWidgetInfo
import com.stt.android.home.dashboardv2.widgets.SleepWidgetInfo
import com.stt.android.home.dashboardv2.widgets.StepWidgetInfo
import com.stt.android.home.dashboardv2.widgets.TSSWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Vo2MaxWidgetInfo
import com.stt.android.home.dashboardv2.widgets.WidgetInfo
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.home.dayview.DayViewActivity
import com.stt.android.home.dayview.DayViewItemType
import com.stt.android.home.devicetype.DeviceTypeSelectActivity
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer
import com.stt.android.offlinemaps.OfflineMapsSelectionActivity
import com.stt.android.offlinemaps.analytics.OfflineMapsAnalytics
import com.stt.android.social.userprofile.HeadsetNavigator
import com.stt.android.ui.map.selection.MyTracksGranularity
import com.stt.android.ui.map.selection.toDiaryCalendarListContainerGranularity
import com.stt.android.utils.STTConstants
import com.stt.android.utils.toEpochMilli
import com.stt.android.watch.DeviceActivity
import com.stt.android.watch.DeviceOnboardingNavigator
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import javax.inject.Inject

internal abstract class BaseSuuntoDashboardFragment : BaseDashboardFragment() {

    @Inject
    lateinit var deviceOnboardingNavigator: DeviceOnboardingNavigator

    @Inject
    lateinit var headsetNavigator: HeadsetNavigator

    @Inject
    lateinit var offlineMapsAnalytics: OfflineMapsAnalytics

    @Inject
    lateinit var chartNavigator: ChartNavigator

    @Inject
    lateinit var diaryNavigator: DiaryNavigator

    @Inject
    lateinit var recoveryNavigator: RecoveryNavigator

    @Inject
    @FeatureTogglePreferences
    lateinit var featureTogglePreferences: SharedPreferences

    // If no headphones have been connected, we don't need to start the service
    private var syncHeadphoneServiceStarted: Boolean = false

    private var stopTimestamp = 0L

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.openDeviceOnboardingFlow
                .collect(::openDeviceOnboardingActivity)
        }
        launchHeadphoneSyncDataService()
    }

    private fun openDeviceOnboardingActivity(suuntoDeviceType: SuuntoDeviceType) {
        deviceOnboardingNavigator.newOnboardingActivityIntent(requireContext(), suuntoDeviceType)
            ?.let { startActivity(it) }
    }

    override fun onStart() {
        super.onStart()
        viewModel.checkIfDeviceOnboardingIsNeeded()
        toolbarViewModel.observeWatchState()
        toolbarViewModel.checkSupportOfflineMaps()
    }

    override fun onStop() {
        super.onStop()
        stopTimestamp = LocalDateTime.now().toEpochMilli()
    }

    private fun launchHeadphoneSyncDataService() {
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                toolbarViewModel.showHeadsetEntrance.collect { show ->
                    // avoid launch headphone service frequently
                    if (show && LocalDateTime.now()
                            .toEpochMilli() - stopTimestamp > LAUNCH_HEADPHONE_SERVICE_INTERVAL
                    ) {
                        headsetNavigator.launchSyncDataService(requireContext())
                        syncHeadphoneServiceStarted = true
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        if (syncHeadphoneServiceStarted)
            headsetNavigator.stopSyncDataService(requireContext())
        super.onDestroy()
    }

    override fun openWatchDevice() {
        val context = requireContext()
        context.startActivity(DeviceActivity.newStartIntent(context))
    }

    override fun openHeadsetDevice() {
        headsetNavigator.launchHeadsetActivity(requireContext())
    }

    override fun downloadOfflineMap() {
        val context = requireContext()
        context.startActivity(
            OfflineMapsSelectionActivity.newStartIntent(
                context,
                AnalyticsPropertyValue.DownloadMapsScreenSource.HOME_SCREEN_MENU,
                offlineMapsAnalytics,
            )
        )
    }

    override fun pairDevice() {
        val context = requireContext()
        context.startActivity(
            DeviceTypeSelectActivity.newStartIntent(context)
        )
    }

    override fun openWidget(widgetInfo: WidgetInfo) {
        val newWidgetChartEnabled = featureTogglePreferences.getBoolean(
            STTConstants.FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGET_CHART,
            STTConstants.FeatureTogglePreferences.KEY_ENABLE_NEW_WIDGET_CHART_DEFAULT,
        )
        if (newWidgetChartEnabled && openNewWidgetChart(widgetInfo)) {
            return
        }

        when (widgetInfo) {
            is ActivityWidgetInfo -> {
                val type = widgetInfo.granularityType.toDiaryCalendarListContainerGranularity()
                openDiaryCalendar(type, true, HOME_SCREEN_DASHBOARD_ACTIVITIES)
            }
            is CalendarWidgetInfo -> {
                val type = widgetInfo.granularityType.toDiaryCalendarListContainerGranularity()
                openDiaryCalendar(type, false, HOME_SCREEN_DASHBOARD_CALENDAR)
            }
            is CommuteWidgetInfo -> tagsNavigator.openDiaryForTag(
                requireContext(),
                resources.getString(SuuntoTag.COMMUTE.nameRes)
            )
            is MapWidgetInfo -> {
                viewModel.handleMapClick(widgetInfo.granularityType)
                requireActivity().startActivity(
                    homeActivityNavigator.newStartIntentToExploreTab(
                        requireContext(),
                        false,
                        AnalyticsPropertyValue.MapScreenSource.HOME_SCREEN_DASHBOARD
                    )
                )
            }
            is ProgressWidgetInfo -> requireActivity().startActivity(
                homeActivityNavigator.newStartIntentToDiaryProgressTab(requireContext())
            )
            is CalorieWidgetInfo -> requireActivity().startActivity(
                homeActivityNavigator.newStartIntentToDiaryCaloriesTab(requireActivity())
            )
            is StepWidgetInfo -> requireActivity().startActivity(
                homeActivityNavigator.newStartIntentToDiaryStepsTab(requireActivity())
            )
            is ResourceWidgetInfo -> requireActivity().startActivity(
                DayViewActivity.newStartIntent(
                    requireContext(),
                    initialItemToScrollTo = DayViewItemType.RECOVERY
                )
            )
            is AscentWidgetInfo -> requireActivity().startActivity(
                homeActivityNavigator.newStartIntentToDiaryWorkoutList(
                    requireActivity(),
                    secondaryGraphType = GraphDataType.ASCENT
                )
            )
            is DurationWidgetInfo -> {
                widgetInfo.period.mapGranularity()?.let {
                    openDiaryCalendar(it, false, HOME_SCREEN_DASHBOARD_TOTAL_DURATION)
                } ?: requireActivity().startActivity(
                    homeActivityNavigator.newStartIntentToDiaryWorkoutList(
                        requireActivity(),
                        secondaryGraphType = GraphDataType.DURATION
                    )
                )
            }
            is SleepWidgetInfo -> requireActivity().startActivity(
                DayViewActivity.newStartIntent(
                    requireContext(),
                    initialItemToScrollTo = DayViewItemType.SLEEP
                )
            )
            is DailyHeartRateWidgetInfo,
            is Last7DaysHeartRateWidgetInfo -> requireActivity().startActivity(
                DayViewActivity.newStartIntent(
                    requireContext(),
                    initialItemToScrollTo = DayViewItemType.HEART_RATE
                )
            )
            is HrvWidgetInfo,
            is RecoveryWidgetInfo -> requireActivity().startActivity(
                homeActivityNavigator.newStartIntentToDiaryRecoveryTab(requireContext())
            )
            is TSSWidgetInfo -> requireActivity().startActivity(
                homeActivityNavigator.newStartIntentToDiaryProgressTab(requireContext())
            )
            is Vo2MaxWidgetInfo -> requireActivity().startActivity(
                homeActivityNavigator.newStartIntentToDiaryWorkoutList(
                    requireActivity(),
                    secondaryGraphType = GraphDataType.FITNESS_LEVEL,
                )
            )
            is ActivityDistanceWidgetInfo,
            is ActivityDurationWidgetInfo,
            is ActivitySpeedWidgetInfo,
            is ActivityTimesWidgetInfo -> openDiaryCalendar(
                granularity = DiaryCalendarListContainer.Granularity.WEEK,
                showActivitiesList = true,
                source = HOME_SCREEN_DASHBOARD_ACTIVITIES,
            )
            is MenstrualPeriodWidgetInfo -> logMenstrualCycle()
        }
    }

    private fun openNewWidgetChart(widgetInfo: WidgetInfo): Boolean {
        val chartContent: ChartContent
        val chartGranularity: ChartGranularity
        when (widgetInfo) {
            is ActivityDistanceWidgetInfo -> return false
            is ActivityDurationWidgetInfo -> return false
            is ActivitySpeedWidgetInfo -> return false
            is ActivityTimesWidgetInfo -> return false
            is ActivityWidgetInfo -> {
                val timeRange = when (widgetInfo.granularityType) {
                    MyTracksGranularity.Type.THIS_WEEK -> GraphTimeRange.CURRENT_WEEK
                    MyTracksGranularity.Type.THIS_MONTH -> GraphTimeRange.CURRENT_MONTH
                    MyTracksGranularity.Type.LAST_30_DAYS -> GraphTimeRange.THIRTY_DAYS
                    else -> GraphTimeRange.CURRENT_WEEK
                }
                diaryNavigator.openActivityScreen(requireContext(), timeRange)
                return true
            }
            is AscentWidgetInfo -> {
                chartContent = ChartContent.ASCENT
                chartGranularity = ChartGranularity.WEEKLY
            }
            is CalendarWidgetInfo -> {
                val timeRange = when (widgetInfo.granularityType) {
                    MyTracksGranularity.Type.THIS_WEEK -> GraphTimeRange.CURRENT_WEEK
                    MyTracksGranularity.Type.THIS_MONTH -> GraphTimeRange.CURRENT_MONTH
                    MyTracksGranularity.Type.LAST_30_DAYS -> GraphTimeRange.THIRTY_DAYS
                    else -> GraphTimeRange.CURRENT_WEEK
                }
                diaryNavigator.openCalendarScreen(requireContext(), timeRange)
                return true
            }
            is CalorieWidgetInfo -> {
                chartContent = ChartContent.CALORIES
                chartGranularity = ChartGranularity.DAILY
            }
            is DailyHeartRateWidgetInfo -> {
                chartContent = ChartContent.HEART_RATE
                chartGranularity = ChartGranularity.DAILY
            }
            is CommuteWidgetInfo -> {
                chartContent = ChartContent.COMMUTE
                chartGranularity = ChartGranularity.MONTHLY
            }
            is DurationWidgetInfo -> {
                chartContent = ChartContent.DURATION
                chartGranularity = when (widgetInfo.period.mapGranularity()) {
                    DiaryCalendarListContainer.Granularity.WEEK -> ChartGranularity.WEEKLY
                    DiaryCalendarListContainer.Granularity.MONTH -> ChartGranularity.MONTHLY
                    DiaryCalendarListContainer.Granularity.LAST_30_DAYS -> ChartGranularity.THIRTY_DAYS
                    DiaryCalendarListContainer.Granularity.YEAR -> ChartGranularity.YEARLY
                    null -> ChartGranularity.SEVEN_DAYS
                }
            }
            is HrvWidgetInfo -> {
                chartContent = ChartContent.HRV
                chartGranularity = ChartGranularity.WEEKLY
            }
            is MapWidgetInfo -> {
                val timeRange = when (widgetInfo.granularityType) {
                    MyTracksGranularity.Type.THIS_WEEK -> GraphTimeRange.CURRENT_WEEK
                    MyTracksGranularity.Type.THIS_MONTH -> GraphTimeRange.CURRENT_MONTH
                    MyTracksGranularity.Type.LAST_30_DAYS -> GraphTimeRange.THIRTY_DAYS
                    else -> GraphTimeRange.CURRENT_WEEK
                }
                diaryNavigator.openMapScreen(requireContext(), timeRange)
                return true
            }
            is MenstrualPeriodWidgetInfo -> return false
            is Last7DaysHeartRateWidgetInfo -> {
                chartContent = when (widgetInfo.widgetType) {
                    WidgetType.MINIMUM_HEART_RATE -> ChartContent.MINIMUM_HEART_RATE
                    WidgetType.MINIMUM_SLEEP_HEART_RATE -> ChartContent.SLEEPING_MINIMUM_HEART_RATE
                    WidgetType.RESTING_HEART_RATE -> ChartContent.RESTING_HEART_RATE
                    else -> throw IllegalArgumentException("${widgetInfo.widgetType} not supported for Last7DaysHeartRateWidgetInfo")
                }
                chartGranularity = ChartGranularity.WEEKLY
            }
            is ProgressWidgetInfo -> {
                diaryNavigator.openProgressScreen(requireContext())
                return true
            }
            is RecoveryWidgetInfo -> {
                if (!featureTogglePreferences.getBoolean(
                        STTConstants.FeatureTogglePreferences.KEY_NEW_TRAINING_ZONE,
                        STTConstants.FeatureTogglePreferences.KEY_NEW_TRAINING_ZONE_DEFAULT)
                ) {
                    return false
                }
                recoveryNavigator.launchRecoveryV2(requireContext())
                return true
            }
            is ResourceWidgetInfo -> {
                chartContent = ChartContent.RESOURCES
                chartGranularity = ChartGranularity.DAILY
            }
            is SleepWidgetInfo -> {
                chartContent = ChartContent.SLEEP
                chartGranularity = ChartGranularity.DAILY
            }

            is StepWidgetInfo -> {
                chartContent = ChartContent.STEPS
                chartGranularity = ChartGranularity.DAILY
            }
            is TSSWidgetInfo -> {
                chartContent = ChartContent.TSS
                chartGranularity = ChartGranularity.WEEKLY
            }
            is Vo2MaxWidgetInfo -> {
                chartContent = ChartContent.VO2MAX
                chartGranularity = ChartGranularity.SIX_WEEKS
            }
        }

        chartNavigator.openChartScreen(
            context = requireContext(),
            chartContent = chartContent,
            chartStyle = ChartStyle.SINGLE,
            chartGranularity = chartGranularity,
        )

        return true
    }

    companion object {
        private const val LAUNCH_HEADPHONE_SERVICE_INTERVAL = 30 * 1000L // unit: ms
    }
}
