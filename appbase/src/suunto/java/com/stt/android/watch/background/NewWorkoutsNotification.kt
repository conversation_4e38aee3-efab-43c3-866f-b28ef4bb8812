package com.stt.android.watch.background

import android.annotation.SuppressLint
import android.app.PendingIntent
import android.content.Context
import androidx.core.app.NotificationManagerCompat
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceType.PUSH_MESSAGE
import com.stt.android.analytics.AnalyticsPropertyValue.AppOpenSourceTypeDetail.ACTIVITIES_SYNCED
import com.stt.android.analytics.AppOpenAnalyticsActivity
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.home.HomeActivityNavigator
import com.stt.android.notifications.CHANNEL_ID_ACTIVITY_SYNCED
import com.stt.android.notifications.STTNotificationUI
import rx.Completable
import timber.log.Timber

@SuppressLint("MissingPermission")
fun showNewWorkoutsNotification(
    context: Context,
    workoutHeaderController: WorkoutHeaderController,
    homeActivityNavigator: HomeActivityNavigator,
    userName: String,
    newWorkouts: Boolean
): Completable {
    if (!newWorkouts) {
        return Completable.complete()
    }
    val builder = STTNotificationUI.buildBasicNotificationBuilder(
        context,
        CHANNEL_ID_ACTIVITY_SYNCED
    )
    val intent = homeActivityNavigator.newStartIntentToDiaryWorkoutList(context)
    // Create a synthetic back stack for the activity that is launched from the notification
    val stackBuilder = homeActivityNavigator.getTaskStackBuilder(context, intent)
    stackBuilder.addNextIntent(AppOpenAnalyticsActivity.newStartIntent(context, PUSH_MESSAGE, ACTIVITIES_SYNCED))

    builder.setContentIntent(
        stackBuilder.getPendingIntent(
            0,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    )
    builder.setAutoCancel(true)

    return workoutHeaderController
        .loadUnseenWorkoutsCount(userName)
        .toSingle()
        .doOnSuccess { unseenWorkoutsCount ->
            val count = unseenWorkoutsCount?.toInt() ?: 0
            if (count > 0) {
                builder.setContentText(
                    context.resources
                        .getQuantityString(
                            R.plurals.watch_notification_successful_entries,
                            count,
                            count
                        )
                )
                val notificationManager = NotificationManagerCompat.from(context)
                notificationManager.notify(R.id.watch_notification_id, builder.build())
            }
        }
        .doOnError { throwable -> Timber.w(throwable, "Failed to show new workouts notification") }
        .toCompletable()
}

fun NotificationManagerCompat.clearNewWorkoutsNotification() = cancel(R.id.watch_notification_id)
