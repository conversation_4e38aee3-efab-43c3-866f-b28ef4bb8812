package com.stt.android.home.diary.diarycalendar.planner

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.SavedStateHandle
import app.cash.turbine.test
import com.google.common.truth.Truth.assertThat
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.home.WorkoutBroadcastActionListener
import com.stt.android.home.diary.diarycalendar.planner.analytics.AiPlannerAnalytics
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlan
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlanLevel
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlanStatus
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerProgramDetails
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerProgramDetailsEventInfo
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerProgramDetailsQuestionnaire
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerProgramPlan
import com.stt.android.home.diary.diarycalendar.planner.domain.models.WeeklyProgram
import com.stt.android.home.diary.diarycalendar.planner.domain.models.WeeklyTargets
import com.stt.android.home.diary.diarycalendar.planner.models.Option
import com.stt.android.home.diary.diarycalendar.planner.models.PaceMeasurement
import com.stt.android.home.diary.diarycalendar.planner.models.Question
import com.stt.android.home.diary.diarycalendar.planner.models.WeeklyChartType
import com.stt.android.home.diary.diarycalendar.planner.models.fakeMultiChoiceQuestion1
import com.stt.android.home.diary.diarycalendar.planner.models.fakeSingleChoiceQuestion1
import com.stt.android.home.diary.diarycalendar.planner.models.fakeSingleChoiceQuestion2
import com.stt.android.home.diary.diarycalendar.planner.models.fakeValueInputNumberQuestion
import com.stt.android.home.diary.diarycalendar.planner.usercases.CreatePlanUiStateUseCase
import com.stt.android.home.diary.diarycalendar.planner.usercases.DeleteActivePlanUseCase
import com.stt.android.home.diary.diarycalendar.planner.usercases.DeletePlanByIdUseCase
import com.stt.android.home.diary.diarycalendar.planner.usercases.GenerateTrainingPlanUseCase
import com.stt.android.home.diary.diarycalendar.planner.usercases.GetLastPlanUseCase
import com.stt.android.home.diary.diarycalendar.planner.usercases.GetLastPlanUseCaseResult
import com.stt.android.home.diary.diarycalendar.planner.usercases.GetWorkoutPlannerProgramDetailsByIdUseCase
import com.stt.android.home.diary.diarycalendar.planner.usercases.GetWorkoutPlannerProgramDetailsByIdUseCaseResult
import com.stt.android.home.diary.diarycalendar.planner.usercases.SearchPlansUseCase
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.testutils.NewCoroutinesTestRule
import com.stt.android.workouts.details.values.WorkoutValue
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters

@RunWith(MockitoJUnitRunner::class)
class DiaryCalendarPlannerViewModelTest {
    private val programId: String = "programId"

    @Rule
    @JvmField
    val coroutinesTestRule = NewCoroutinesTestRule()

    @Mock
    lateinit var getWorkoutPlannerProgramDetailsByIdUseCase: GetWorkoutPlannerProgramDetailsByIdUseCase

    @Mock
    lateinit var generateTrainingPlanUseCase: GenerateTrainingPlanUseCase

    @Mock
    lateinit var infoModelFormatter: InfoModelFormatter

    @Mock
    lateinit var mockContext: Context

    private lateinit var diaryCalendarPlannerSurveyViewModel: TrainingPlannerProgramDetailsViewModel

    @Mock
    lateinit var getLastPlanUseCase: GetLastPlanUseCase

    @Mock
    lateinit var searchPlansUseCase: SearchPlansUseCase

    @Mock
    lateinit var deletePlanByIdUseCase: DeletePlanByIdUseCase

    @Mock
    lateinit var deleteActivePlanUseCase: DeleteActivePlanUseCase

    @Mock
    lateinit var createPlanUiStateUseCase: CreatePlanUiStateUseCase

    @Mock
    lateinit var trainingPlannerUiStateMapper: TrainingPlannerUiStateMapper

    @Mock
    lateinit var workoutBroadcastActionListener: WorkoutBroadcastActionListener

    @Mock
    lateinit var userSettingsController: UserSettingsController

    @Mock
    lateinit var sharedPrefs: SharedPreferences

    @Mock
    lateinit var analytics: AiPlannerAnalytics

    private fun initSurveyViewModel() {
        whenever(
            infoModelFormatter.formatValue(
                eq(SummaryItem.ASCENTALTITUDE),
                any(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull()
            )
        ).thenReturn(
            WorkoutValue.createEmpty()
        )
        whenever(infoModelFormatter.unit).thenReturn(MeasurementUnit.METRIC)
        diaryCalendarPlannerSurveyViewModel = TrainingPlannerProgramDetailsViewModel(
            getWorkoutPlannerProgramDetailsByIdUseCase = getWorkoutPlannerProgramDetailsByIdUseCase,
            generateTrainingPlanUseCase = generateTrainingPlanUseCase,
            savedStateHandle = SavedStateHandle(
                mapOf(
                    TrainingPlannerNavArgs.META_PROGRAM_ID to programId,
                )
            ),
            measurementUnit = MeasurementUnit.METRIC,
            trainingPlannerUiStateMapper = TrainingPlannerUiStateMapper(
                infoModelFormatter = infoModelFormatter,
            ),
            analytics = analytics,
        )
        diaryCalendarPlannerSurveyViewModel.showSurvey()
    }

    private fun createPlannerViewModel() = DiaryCalendarPlannerViewModel(
        getLastPlanUseCase = getLastPlanUseCase,
        searchPlansUseCase = searchPlansUseCase,
        deletePlanByIdUseCase = deletePlanByIdUseCase,
        deleteActivePlanUseCase = deleteActivePlanUseCase,
        createPlanUiStateUseCase = createPlanUiStateUseCase,
        trainingPlannerUiStateMapper = trainingPlannerUiStateMapper,
        workoutBroadcastActionListener = workoutBroadcastActionListener,
        userSettingsController = userSettingsController,
        sharedPrefs = sharedPrefs,
        analytics = analytics,
        coroutinesDispatchers = object : CoroutinesDispatchers {
            override val main = Dispatchers.Unconfined
            override val computation = Dispatchers.Unconfined
            override val io = Dispatchers.Unconfined
        }
    )

    private fun makeFakeTrainingPlannerProgramDetails(questions: List<Question>) =
        TrainingPlannerProgramDetails(
            id = "id",
            version = "1",
            bannerUrl = "",
            eventInfo = TrainingPlannerProgramDetailsEventInfo(
                ascent = 0,
                date = "2025-01-01",
                distance = 0,
                name = "",
                richInfo = "",
                terrain = "",
                weather = ""
            ),
            description = "",
            durationWeeks = 0,
            name = "",
            richInfo = "",
            sports = listOf(),
            questionnaire = TrainingPlannerProgramDetailsQuestionnaire(
                version = "",
                questions = questions
            ),
            level = TrainingPlanLevel.BEGINNER,
            focus = "Race",
        )

    @Test
    fun currentQuestionIndexIsOneWhenStarting() = runTest {
        whenever(mockContext.getString(any())).thenReturn("Mocked String")
        whenever(infoModelFormatter.context).thenReturn(mockContext)
        whenever(getWorkoutPlannerProgramDetailsByIdUseCase.invoke(programId)).thenReturn(
            GetWorkoutPlannerProgramDetailsByIdUseCaseResult.Data(
                makeFakeTrainingPlannerProgramDetails(
                    listOf(
                        fakeSingleChoiceQuestion1, fakeSingleChoiceQuestion2
                    )
                )
            )
        )
        initSurveyViewModel()
        diaryCalendarPlannerSurveyViewModel.uiState.test {
            val state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
            assertThat(state.externalQuestionIndex).isEqualTo(1)
        }
    }

    @Test
    fun totalNumberOfQuestionsIsShownCorrectly() = runTest {
        whenever(mockContext.getString(any())).thenReturn("Mocked String")
        whenever(infoModelFormatter.context).thenReturn(mockContext)
        whenever(getWorkoutPlannerProgramDetailsByIdUseCase.invoke(programId)).thenReturn(
            GetWorkoutPlannerProgramDetailsByIdUseCaseResult.Data(
                makeFakeTrainingPlannerProgramDetails(
                    listOf(
                        fakeSingleChoiceQuestion1, fakeSingleChoiceQuestion2
                    )
                )
            )
        )
        initSurveyViewModel()
        diaryCalendarPlannerSurveyViewModel.uiState.test {
            val state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
            assertThat(state.totalNumberOfQuestions).isEqualTo(2)
        }
    }

    //
    @Test
    fun continueMovesTheUserToNextQuestionIfAny() = runTest {
        whenever(mockContext.getString(any())).thenReturn("Mocked String")
        whenever(infoModelFormatter.context).thenReturn(mockContext)
        whenever(getWorkoutPlannerProgramDetailsByIdUseCase.invoke(programId)).thenReturn(
            GetWorkoutPlannerProgramDetailsByIdUseCaseResult.Data(
                makeFakeTrainingPlannerProgramDetails(
                    questions = listOf(
                        fakeSingleChoiceQuestion1, fakeSingleChoiceQuestion2
                    )
                )
            )
        )
        initSurveyViewModel()
        diaryCalendarPlannerSurveyViewModel.uiState.test {
            var state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
            assertThat(state.currentQuestion).isEqualTo(fakeSingleChoiceQuestion1)
            state.onContinueClick()
            state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
            assertThat(state.currentQuestion).isEqualTo(fakeSingleChoiceQuestion2)
        }
    }

    @Test
    fun selectingOptionForSingleChoice() = runTest {
        whenever(mockContext.getString(any())).thenReturn("Mocked String")
        whenever(infoModelFormatter.context).thenReturn(mockContext)
        whenever(getWorkoutPlannerProgramDetailsByIdUseCase.invoke(programId)).thenReturn(
            GetWorkoutPlannerProgramDetailsByIdUseCaseResult.Data(
                makeFakeTrainingPlannerProgramDetails(
                    questions = listOf(
                        fakeSingleChoiceQuestion1, fakeSingleChoiceQuestion2
                    )
                )
            )
        )
        initSurveyViewModel()

        diaryCalendarPlannerSurveyViewModel.uiState.test {
            var state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
            assertThat(state.currentQuestion).isEqualTo(fakeSingleChoiceQuestion1)
            var question = state.currentQuestion as Question.SingleChoice
            assertThat(question.answer).isNull()
            diaryCalendarPlannerSurveyViewModel.onOptionsSelected(
                listOf(fakeSingleChoiceQuestion1.options.first()),
                question.id
            )
            state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
            question = state.currentQuestion as Question.SingleChoice
            assertThat(question.answer).isNotNull()
            assertThat(question.answer).isEqualTo(fakeSingleChoiceQuestion1.options.first())
        }
    }

    @Test
    fun selectingOptionForMultiChoices() = runTest {
        whenever(mockContext.getString(any())).thenReturn("Mocked String")
        whenever(infoModelFormatter.context).thenReturn(mockContext)
        whenever(getWorkoutPlannerProgramDetailsByIdUseCase.invoke(programId)).thenReturn(
            GetWorkoutPlannerProgramDetailsByIdUseCaseResult.Data(
                makeFakeTrainingPlannerProgramDetails(
                    questions = listOf(
                        fakeMultiChoiceQuestion1
                    )
                )
            )
        )
        initSurveyViewModel()

        diaryCalendarPlannerSurveyViewModel.uiState.test {
            var state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
            assertThat(state.currentQuestion).isEqualTo(fakeMultiChoiceQuestion1)
            var question = state.currentQuestion as Question.MultiChoice
            assertThat(question.answers).isEmpty()
            // Select an option
            diaryCalendarPlannerSurveyViewModel.onOptionsSelected(
                listOf(fakeMultiChoiceQuestion1.options.first()),
                question.id
            )
            state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
            question = state.currentQuestion as Question.MultiChoice
            assertThat(question.answers).isNotEmpty()
            assertThat(question.answers).containsExactly(fakeMultiChoiceQuestion1.options.first())

            // Deselect the selected option
            diaryCalendarPlannerSurveyViewModel.onOptionsSelected(
                listOf(),
                question.id
            )
            state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
            question = state.currentQuestion as Question.MultiChoice
            assertThat(question.answers).isEmpty()

            // select more than one option
            diaryCalendarPlannerSurveyViewModel.onOptionsSelected(
                listOf(
                    fakeMultiChoiceQuestion1.options[0],
                    fakeMultiChoiceQuestion1.options[1]
                ),
                question.id
            )
            state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
            question = state.currentQuestion as Question.MultiChoice
            assertThat(question.answers).isNotEmpty()
            assertThat(question.answers).containsExactly(
                fakeMultiChoiceQuestion1.options[0],
                fakeMultiChoiceQuestion1.options[1]
            )
        }
    }

    @Test(expected = IllegalStateException::class)
    fun whenOnOptionChangeCalled_andCurrentQuestionIsInputField_shouldThrowAnException() = runTest {
        whenever(getWorkoutPlannerProgramDetailsByIdUseCase.invoke(programId)).thenReturn(
            GetWorkoutPlannerProgramDetailsByIdUseCaseResult.Data(
                makeFakeTrainingPlannerProgramDetails(
                    questions = listOf(
                        fakeValueInputNumberQuestion
                    )
                )
            )
        )
        initSurveyViewModel()

        diaryCalendarPlannerSurveyViewModel.uiState.test {
            val state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
            assertThat(state.currentQuestion).isEqualTo(
                fakeValueInputNumberQuestion
            )
            diaryCalendarPlannerSurveyViewModel.onOptionsSelected(
                listOf(Option("", "")),
                fakeValueInputNumberQuestion.id
            )
        }
    }

    @Test(expected = IllegalStateException::class)
    fun whenOnNumberChangeCalled_andCurrentQuestionIsNotNumber_shouldThrowAnException() = runTest {
        whenever(getWorkoutPlannerProgramDetailsByIdUseCase.invoke(programId)).thenReturn(
            GetWorkoutPlannerProgramDetailsByIdUseCaseResult.Data(
                makeFakeTrainingPlannerProgramDetails(
                    questions = listOf(
                        fakeMultiChoiceQuestion1
                    )
                )
            )
        )
        initSurveyViewModel()

        diaryCalendarPlannerSurveyViewModel.uiState.test {
            val state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
            assertThat(state.currentQuestion).isEqualTo(
                fakeMultiChoiceQuestion1
            )
            diaryCalendarPlannerSurveyViewModel.onNumberChange(10, fakeMultiChoiceQuestion1.id)
        }
    }

    @Test(expected = IllegalStateException::class)
    fun whenOnDistanceChangeCalled_andCurrentQuestionIsNotDistance_shouldThrowAnException() =
        runTest {
            whenever(getWorkoutPlannerProgramDetailsByIdUseCase.invoke(programId)).thenReturn(
                GetWorkoutPlannerProgramDetailsByIdUseCaseResult.Data(
                    makeFakeTrainingPlannerProgramDetails(
                        questions = listOf(
                            fakeMultiChoiceQuestion1
                        )
                    )
                )
            )
            initSurveyViewModel()

            diaryCalendarPlannerSurveyViewModel.uiState.test {
                val state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
                assertThat(state.currentQuestion).isEqualTo(
                    fakeMultiChoiceQuestion1
                )
                diaryCalendarPlannerSurveyViewModel.onDistanceChange(
                    10,
                    fakeMultiChoiceQuestion1.id
                )
            }
        }

    @Test(expected = IllegalStateException::class)
    fun whenOnDurationChangeCalled_andCurrentQuestionIsNotDuration_shouldThrowAnException() =
        runTest {
            whenever(getWorkoutPlannerProgramDetailsByIdUseCase.invoke(programId)).thenReturn(
                GetWorkoutPlannerProgramDetailsByIdUseCaseResult.Data(
                    makeFakeTrainingPlannerProgramDetails(
                        questions = listOf(
                            fakeMultiChoiceQuestion1
                        )
                    )
                )
            )
            initSurveyViewModel()

            diaryCalendarPlannerSurveyViewModel.uiState.test {
                val state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
                assertThat(state.currentQuestion).isEqualTo(
                    fakeMultiChoiceQuestion1
                )
                diaryCalendarPlannerSurveyViewModel.onDurationChange(
                    2,
                    10,
                    fakeMultiChoiceQuestion1.id
                )
            }
        }

    @Test(expected = IllegalStateException::class)
    fun whenOnPaceChangeCalled_andCurrentQuestionIsNotPace_shouldThrowAnException() = runTest {
        whenever(getWorkoutPlannerProgramDetailsByIdUseCase.invoke(programId)).thenReturn(
            GetWorkoutPlannerProgramDetailsByIdUseCaseResult.Data(
                makeFakeTrainingPlannerProgramDetails(
                    questions = listOf(
                        fakeMultiChoiceQuestion1
                    )
                )
            )
        )
        initSurveyViewModel()

        diaryCalendarPlannerSurveyViewModel.uiState.test {
            val state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
            assertThat(state.currentQuestion).isEqualTo(
                fakeMultiChoiceQuestion1
            )
            diaryCalendarPlannerSurveyViewModel.onPaceChange(
                PaceMeasurement(2.0),
                fakeMultiChoiceQuestion1.id
            )
        }
    }

    @Test(expected = IllegalStateException::class)
    fun whenOnDateChangeCalled_andCurrentQuestionIsNotDate_shouldThrowAnException() = runTest {
        whenever(getWorkoutPlannerProgramDetailsByIdUseCase.invoke(programId)).thenReturn(
            GetWorkoutPlannerProgramDetailsByIdUseCaseResult.Data(
                makeFakeTrainingPlannerProgramDetails(
                    questions = listOf(
                        fakeMultiChoiceQuestion1
                    )
                )
            )
        )
        initSurveyViewModel()

        diaryCalendarPlannerSurveyViewModel.uiState.test {
            val state = awaitItem() as ProgramDetails.Details.Survey.AnswerQuestions
            assertThat(state.currentQuestion).isEqualTo(
                fakeMultiChoiceQuestion1
            )
            diaryCalendarPlannerSurveyViewModel.onDateChange(10000000, fakeMultiChoiceQuestion1.id)
        }
    }

    @Test
    fun `auto-dismiss plan completed prompt after 7 days`() = runTest {
        whenever(getLastPlanUseCase.invoke()).thenReturn(GetLastPlanUseCaseResult.NoLastPlan)
        val mockEditor = mock<SharedPreferences.Editor>()
        whenever(sharedPrefs.edit()).thenReturn(mockEditor)
        whenever(sharedPrefs.getString(any(), any())).thenReturn(WeeklyChartType.TSS.name)
        whenever(mockEditor.putString(any(), any())).thenReturn(mockEditor)
        whenever(mockEditor.apply()).then { }

        val viewModel = createPlannerViewModel()

        testPromptDismissal(DayOfWeek.MONDAY, viewModel)
        testPromptDismissal(DayOfWeek.SUNDAY, viewModel)
    }

    private fun testPromptDismissal(
        firstDayOfWeek: DayOfWeek,
        viewModel: DiaryCalendarPlannerViewModel
    ) {
        val monday = LocalDate.of(2025, 3, 31)
        val expectedLastDate = if (firstDayOfWeek == DayOfWeek.MONDAY) {
            LocalDate.of(2025, 4, 20)
        } else {
            LocalDate.of(2025, 4, 19)
        }
        val startOfWeek = monday.with(
            TemporalAdjusters.previousOrSame(
                firstDayOfWeek
            )
        )
        val middleOfWeek = startOfWeek.plusDays(3)
        val endOfWeek = startOfWeek.plusDays(6)
        testPromptDismissal(viewModel, startOfWeek, firstDayOfWeek, expectedLastDate)
        testPromptDismissal(viewModel, middleOfWeek, firstDayOfWeek, expectedLastDate)
        testPromptDismissal(viewModel, endOfWeek, firstDayOfWeek, expectedLastDate)
    }

    private fun testPromptDismissal(
        viewModel: DiaryCalendarPlannerViewModel,
        startOfWeek: LocalDate,
        firstDayOfWeek: DayOfWeek,
        expectedLastDate: LocalDate,
    ) {
        val plan = createPlan(startOfWeek)
        val weekAfter = expectedLastDate.plusWeeks(1)
        val eightDaysAfter = expectedLastDate.plusDays(8)
        assertThat(
            viewModel.autoDismissalCheck(
                plan,
                firstDayOfWeek,
                expectedLastDate
            )
        ).isEqualTo(false)
        assertThat(
            viewModel.autoDismissalCheck(
                plan,
                firstDayOfWeek,
                weekAfter
            )
        ).isEqualTo(false)
        assertThat(
            viewModel.autoDismissalCheck(
                plan,
                firstDayOfWeek,
                eightDaysAfter
            )
        ).isEqualTo(true)
    }

    private fun createPlan(startDate: LocalDate) = TrainingPlan(
        id = "2",
        metaPlanId = "1",
        name = "Couch to 5 k",
        durationWeeks = 3,
        startDate = startDate,
        targetDate = startDate.plusWeeks(3),
        status = TrainingPlanStatus.ACTIVE,
        weeklyPrograms = listOf(
            WeeklyProgram(
                weekNumber = 1,
                weeklyTargets = WeeklyTargets(
                    distanceInMeters = 1,
                    duration = 2,
                    trainingLoad = 3
                ),
                plannedWorkouts = emptyList(),
                goal = null,
                note = null
            ),
            WeeklyProgram(
                weekNumber = 2,
                weeklyTargets = WeeklyTargets(
                    distanceInMeters = 1,
                    duration = 2,
                    trainingLoad = 3
                ),
                plannedWorkouts = emptyList(),
                goal = null,
                note = null
            ),
            WeeklyProgram(
                weekNumber = 3,
                weeklyTargets = WeeklyTargets(
                    distanceInMeters = 1,
                    duration = 2,
                    trainingLoad = 3
                ),
                plannedWorkouts = emptyList(),
                goal = null,
                note = null
            ),
        ),
        answers = emptyList(),
        questions = emptyList(),
        metaPlanHeader = TrainingPlannerProgramPlan(
            id = "1",
            version = "1",
            thumbnailUrl = "url",
            durationWeeks = 8,
            sports = listOf(CoreActivityType.RUNNING),
            name = "UTMB",
            focus = "Race",
            level = TrainingPlanLevel.BEGINNER,
        ),
        description = "Description",
        richInfo = "Rich text",
        eventInfo = null,
    )
}
