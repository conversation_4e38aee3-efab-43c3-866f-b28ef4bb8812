<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.stt.android.ui.map.selection.MapSelectionViewModel" />
    </data>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            style="@style/SharpCornerBottomSheetStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bottom_sheet_rounded_top_corners_background"
            android:paddingBottom="@dimen/size_spacing_xlarge">

            <ImageView
                android:id="@+id/filter_drag_handle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_spacing_medium"
                android:contentDescription="@null"
                android:elevation="@dimen/elevation_navbar"
                android:src="@drawable/ic_handle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/mapTypeTitle"
                style="@style/Body.Large.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginTop="@dimen/size_spacing_large"
                android:text="@string/map_type_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/filter_drag_handle" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mapTypeList"
                android:layout_width="match_parent"
                android:layout_height="@dimen/map_type_selection_item_height"
                android:layout_marginTop="@dimen/size_spacing_small"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:orientation="horizontal"
                android:paddingStart="@dimen/size_spacing_xsmall"
                android:paddingEnd="@dimen/size_spacing_xsmall"
                android:scrollbars="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintTop_toBottomOf="@+id/mapTypeTitle"
                tools:listitem="@layout/item_map_selection" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/popularToggleGroup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:constraint_referenced_ids="popularToggleTitle, popularToggle, popularToggleDivider" />

            <View
                android:id="@+id/popularToggleDivider"
                style="@style/HorizontalDivider"
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/size_spacing_smaller"
                app:layout_constraintTop_toBottomOf="@+id/myTracksList" />

            <TextView
                android:id="@+id/popularToggleTitle"
                style="@style/Body.Large.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginTop="@dimen/size_spacing_smaller"
                android:text="@string/popular_toggle_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/popularToggleDivider" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/popularToggle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:layout_weight="1"
                app:layout_constraintTop_toBottomOf="@+id/popularToggleTitle"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:checked="@{viewModel.showPopularRoutesLiveData}"
                android:onCheckedChanged="@{(switch, checked) -> viewModel.setShowPopularRoutes(checked)}"
                android:text="@string/popular_toggle_subtitle"
                android:textAppearance="@style/Body.Medium" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/poiToggleGroup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="poiToggleTitle, poiToggle, poiToggleDivider"/>

            <View
                android:id="@+id/poiToggleDivider"
                style="@style/HorizontalDivider"
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/size_spacing_smaller"
                app:layout_constraintTop_toBottomOf="@+id/popularToggle" />

            <TextView
                android:id="@+id/poiToggleTitle"
                style="@style/Body.Large.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginTop="@dimen/size_spacing_smaller"
                android:text="@string/poi_toggle_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/poiToggleDivider" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/poiToggle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:layout_weight="1"
                app:layout_constraintTop_toBottomOf="@+id/poiToggleTitle"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:checked="@{viewModel.showPOIsLiveData}"
                android:onCheckedChanged="@{(switch, checked) -> viewModel.setShowPOIs(checked)}"
                android:text="@string/poi_toggle_subtitle"
                android:textAppearance="@style/Body.Medium" />

            <View
                android:id="@+id/heatmapDivider"
                style="@style/HorizontalDivider"
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/size_spacing_smaller"
                app:layout_constraintTop_toBottomOf="@+id/poiToggle" />

            <TextView
                android:id="@+id/heatmapTitle"
                style="@style/Body.Large.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginTop="@dimen/size_spacing_smaller"
                android:text="@string/heatmap_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/heatmapDivider" />

            <TextView
                android:id="@+id/heatmapSubtitle"
                style="@style/Body.Medium"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:text="@string/heatmap_subtitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/heatmapTitle"
                app:layout_constraintTop_toBottomOf="@+id/heatmapTitle" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/heatmapList"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_spacing_small"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:orientation="horizontal"
                android:paddingStart="@dimen/size_spacing_xsmall"
                android:paddingEnd="@dimen/size_spacing_xsmall"
                android:scrollbars="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintTop_toBottomOf="@+id/heatmapSubtitle"
                tools:listitem="@layout/item_heatmap_selection" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/heatmapsGroup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="heatmapDivider, heatmapTitle, heatmapSubtitle,
                heatmapList" />

            <View
                android:id="@+id/myTracksDivider"
                style="@style/HorizontalDivider"
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/size_spacing_smaller"
                app:layout_constraintTop_toBottomOf="@+id/mapTypeList" />

            <TextView
                android:id="@+id/myTracksTitle"
                style="@style/Body.Large.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginTop="@dimen/size_spacing_smaller"
                android:text="@string/my_tracks_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/myTracksDivider" />

            <TextView
                android:id="@+id/myTracksSubtitle"
                style="@style/Body.Medium"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:text="@string/my_tracks_subtitle"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/myTracksTitle"
                app:layout_constraintTop_toBottomOf="@+id/myTracksTitle" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/myTracksList"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_spacing_small"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:orientation="horizontal"
                android:paddingStart="@dimen/size_spacing_xsmall"
                android:paddingEnd="@dimen/size_spacing_xsmall"
                android:scrollbars="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintTop_toBottomOf="@+id/myTracksSubtitle"
                tools:listitem="@layout/item_my_tracks_selection" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/myTracksGroup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="myTracksDivider, myTracksTitle, myTracksSubtitle,
                myTracksList" />

            <View
                android:id="@+id/roadSurfaceDivider"
                style="@style/HorizontalDivider"
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/size_spacing_smaller"
                app:layout_constraintTop_toBottomOf="@+id/heatmapList" />

            <TextView
                android:id="@+id/roadSurfaceTitle"
                style="@style/Body.Large.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginTop="@dimen/size_spacing_smaller"
                android:text="@string/road_surface_selection_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/roadSurfaceDivider" />

            <TextView
                android:id="@+id/roadSurfaceBeta"
                style="@style/Body.Small"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/beta_feature_tag"
                android:textAllCaps="true"
                android:textSize="@dimen/text_size_xxsmall"
                app:layout_constraintStart_toEndOf="@+id/roadSurfaceTitle"
                app:layout_constraintTop_toTopOf="@+id/roadSurfaceTitle" />

            <ImageView
                android:id="@+id/roadSurfaceInfoButton"
                android:layout_width="@dimen/size_icon_xlarge"
                android:layout_height="@dimen/size_icon_xlarge"
                android:layout_marginStart="@dimen/negative_size_spacing_small"
                android:background="?selectableItemBackgroundBorderless"
                android:contentDescription="@string/road_surface_about_title"
                android:padding="@dimen/size_spacing_smaller"
                android:src="@drawable/ic_info_outline"
                app:layout_constraintBottom_toBottomOf="@+id/roadSurfaceTitle"
                app:layout_constraintStart_toEndOf="@+id/roadSurfaceBeta"
                app:layout_constraintTop_toTopOf="@+id/roadSurfaceTitle"
                app:tint="@color/medium_grey" />

            <!-- Text for this is set in code as a Spannable to get the styling match with Heatmaps &
                 My Tracks that have the Premium note directly appended to their titles -->
            <TextView
                android:id="@+id/roadSurfacePremiumNote"
                style="@style/Body.Large.Bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/negative_size_spacing_xsmall"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="@+id/roadSurfaceTitle"
                app:layout_constraintBottom_toBottomOf="@+id/roadSurfaceTitle"
                app:layout_constraintStart_toEndOf="@id/roadSurfaceInfoButton" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/roadSurfaceList"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_spacing_small"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:orientation="horizontal"
                android:paddingStart="@dimen/size_spacing_xsmall"
                android:paddingEnd="@dimen/size_spacing_xsmall"
                android:scrollbars="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintTop_toBottomOf="@+id/roadSurfaceTitle"
                tools:listitem="@layout/item_road_surface_selection" />

            <LinearLayout
                android:id="@+id/cyclingAllowedContainer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_spacing_medium"
                android:layout_marginTop="@dimen/size_spacing_smaller"
                android:layout_marginEnd="@dimen/size_spacing_medium"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/roadSurfaceList">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/cyclingAllowedIcon"
                    android:layout_width="@dimen/size_icon_medium"
                    android:layout_height="@dimen/size_icon_medium"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/ic_activity_cycling"
                    android:tint="@{viewModel.selectedRoadSurfaceTypes.isEmpty() ? @color/primary_text_disabled_inverse : @color/near_black}" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/cyclingAllowedToggle"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/size_spacing_medium"
                    android:layout_weight="1"
                    android:checked="@{viewModel.hideCyclingForbiddenRoadsLiveData}"
                    android:enabled="@{!viewModel.selectedRoadSurfaceTypes.isEmpty()}"
                    android:onCheckedChanged="@{(switch, checked) -> viewModel.setHideCyclingForbiddenRoads(checked)}"
                    android:text="@string/road_surface_hide_cycling_forbidden"
                    android:textAppearance="@style/Body.Medium" />

            </LinearLayout>

            <View
                android:id="@+id/cyclingAllowedDivider"
                style="@style/HorizontalDivider"
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/size_spacing_smaller"
                android:layout_marginStart="@dimen/size_spacing_medium"
                app:layout_constraintTop_toBottomOf="@+id/cyclingAllowedContainer" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/roadSurfaceGroup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="roadSurfaceDivider, roadSurfaceTitle,
                roadSurfaceBeta, roadSurfaceInfoButton, roadSurfaceList, cyclingAllowedContainer" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</layout>
