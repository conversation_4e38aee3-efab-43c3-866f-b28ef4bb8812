package com.stt.android.controllers

import android.content.SharedPreferences
import androidx.annotation.WorkerThread
import com.stt.android.BuildConfig
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.domain.UserSession
import com.stt.android.domain.user.GetCurrentUserUseCase
import com.stt.android.domain.user.User
import com.stt.android.utils.STTConstants
import java.util.concurrent.locks.ReadWriteLock
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.concurrent.withLock

@Singleton
class CurrentUserController @Inject constructor(
    private val sessionLock: ReadWriteLock,
    private val backendController: BackendController,
    private val getCurrentUserUseCase: GetCurrentUserUseCase,
    @FeatureTogglePreferences private val featureTogglePreferences: SharedPreferences,
) {
    var currentUser: User
        private set

    init {
        sessionLock.writeLock().withLock {
            /*
             * We rely on the fact that the logged in user ID will always be
             * STTConstants.CURRENT_USER_DEFAULT_ID
             */
            currentUser = getCurrentUserUseCase.getCurrentUser()
        }
    }

    val isFieldTester: Boolean
        get() = if (BuildConfig.DEBUG &&
            featureTogglePreferences.getBoolean(
                STTConstants.FeatureTogglePreferences.KEY_ENABLE_FIELD_TESTER_FOR_DEBUG,
                STTConstants.FeatureTogglePreferences.KEY_ENABLE_FIELD_TESTER_FOR_DEBUG_DEFAULT,
            )) {
            true
        } else {
            currentUser.isLoggedIn && currentUser.isFieldTester
        }

    @WorkerThread
    fun store(user: User) {
        currentUser = getCurrentUserUseCase.storeCurrentUser(user)
    }

    val isLoggedIn: Boolean get() = currentUser.isLoggedIn

    val isAnonymous: Boolean get() = currentUser.isAnonymous

    val username: String get() = currentUser.username

    val realNameOrUsername: String get() = currentUser.realNameOrUsername

    val session: UserSession? get() = currentUser.session?.let(UserSession::fromDomainSession)

    @WorkerThread
    @Throws(Exception::class)
    fun linkWithFacebook(accessToken: String?) {
        val currentSession = sessionLock.readLock().withLock {
            check(isLoggedIn) { "User not yet logged in!" }
            session
        }

        if (backendController.linkWithFB(currentSession, accessToken)) {
            sessionLock.writeLock().withLock {
                store(currentUser.makeFacebookReady())
            }
        } else {
            throw Exception("Failed to link with Facebook")
        }
    }
}
