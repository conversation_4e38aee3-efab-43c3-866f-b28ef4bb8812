package com.stt.android.multimedia.sportie

import android.content.Context
import android.graphics.Color
import android.text.Editable
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewPropertyAnimator
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.view.inputmethod.InputMethodManager
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.core.widget.TextViewCompat
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.core.domain.GraphType
import com.stt.android.databinding.SportieOverlayBinding
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.infomodel.SummaryItem
import com.stt.android.logbook.SuuntoLogbookWindow
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.fragments.workout.WorkoutLineChartShare
import com.stt.android.ui.fragments.workout.analysis.WorkoutAnalysisHelper
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.firstOfType
import com.stt.android.workouts.details.values.WorkoutValue
import com.stt.android.workouts.details.values.isSuuntoRun
import com.stt.android.workouts.sharepreview.SportieOverlayViewClickListener
import com.stt.android.workouts.sharepreview.WorkoutShareGraphOption
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.rx2.await
import kotlinx.coroutines.withContext
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Locale
import javax.inject.Inject
import kotlin.math.min
import kotlin.math.roundToInt

/**
 * Sportie overlay base class. Brand flavors need to override this class and provide formatting
 * for texts used in sportie.
 */
@AndroidEntryPoint
abstract class SportieOverlayViewBase @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    @Inject
    lateinit var coroutinesDispatchers: CoroutinesDispatchers

    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    private var sportieOverlayViewClickListener: SportieOverlayViewClickListener? = null

    private val activityWorkoutValues = mutableListOf<WorkoutValue>()
    private var firstDataWorkoutValueIndex = DEFAULT_FIRST_DATA_INDEX
    private var secondDataWorkoutValueIndex = DEFAULT_SECOND_DATA_INDEX
    private var thirdDataWorkoutValueIndex = DEFAULT_THIRD_DATA_INDEX
    private var firstWorkoutValue: WorkoutValue? = null
    private var secondWorkoutValue: WorkoutValue? = null
    private var thirdWorkoutValue: WorkoutValue? = null
    private val runningShowAnimations: MutableList<ViewPropertyAnimator> = mutableListOf()

    // record activityType startTime visible state
    private var showActivityType = true
    private var showStartTime = true

    private val editIcons: List<ImageView>
    private var shownEditIcons: List<ImageView>? = null

    private val xxSmallSpacing: Int

    // Graph
    private var graphOptions = emptyList<WorkoutShareGraphOption>()
    private var graphIndex = DEFAULT_GRAPH_INDEX
    var graphType: GraphType? = null

    protected var margin = 0
    private var marginValuesToGraph = 0
    private var logoTopMargin = 0

    private val binding = SportieOverlayBinding.inflate(LayoutInflater.from(context), this)

    private val graphView: WorkoutLineChartShare
        get() = binding.graphContainer.findViewById(R.id.graphView)

    init {
        editIcons = with(binding) {
            listOf(editIconData1, editIconData2, editIconData3, editIconGraph, editIconData4)
        }
        editIcons.forEach {
            it.alpha = 0f
            it.visibility = View.INVISIBLE
        }
        xxSmallSpacing = context.resources.getDimensionPixelSize(R.dimen.size_spacing_xxsmall)
        setEditText()
        binding.graphContainer.infoModelFormatter = infoModelFormatter
    }

    private val isShareCustomised: Boolean
        get() = firstDataWorkoutValueIndex != DEFAULT_FIRST_DATA_INDEX ||
            secondDataWorkoutValueIndex != DEFAULT_SECOND_DATA_INDEX ||
            thirdDataWorkoutValueIndex != DEFAULT_THIRD_DATA_INDEX ||
            graphIndex != DEFAULT_GRAPH_INDEX

    fun setClickListener(
        sportieOverlayViewClickListener: SportieOverlayViewClickListener,
        enableEditIconClickAnimation: Boolean,
    ) {
        this.sportieOverlayViewClickListener = sportieOverlayViewClickListener
        with(binding) {
            for (view in listOf(firstData, editIconData1)) {
                view.setOnClickListener {
                    sportieOverlayViewClickListener.onWorkoutValueClick(activityWorkoutValues, firstDataWorkoutValueIndex, FIRST_TEXT_VIEW_INDEX)
                }
            }
            for (view in listOf(secondData, editIconData2)) {
                view.setOnClickListener {
                    sportieOverlayViewClickListener.onWorkoutValueClick(activityWorkoutValues, secondDataWorkoutValueIndex, SECOND_TEXT_VIEW_INDEX)
                }
            }
            for (view in listOf(thirdData, editIconData3)) {
                view.setOnClickListener {
                    sportieOverlayViewClickListener.onWorkoutValueClick(activityWorkoutValues, thirdDataWorkoutValueIndex, THIRD_TEXT_VIEW_INDEX)
                }
            }
            for (view in listOf(fourthData, editIconData4)) {
                view.setOnClickListener {
                    sportieOverlayViewClickListener.onWorkoutValueClick(activityWorkoutValues, activityWorkoutValues.size - 1, FOURTH_TEXT_VIEW_INDEX)
                }
            }

            if (enableEditIconClickAnimation) {
                setOnClickListener {
                    if (darkOverlay.isVisible.not()) {
                        startEditIconShowAnimation()
                    }
                }
            }
        }
    }

    private fun startEditIconHideAnimation() {
        shownEditIcons?.forEach { view ->
            view.animate()
                .alpha(0f)
                .setInterpolator(AccelerateInterpolator())
                .setStartDelay(800L)
                .withEndAction {
                    view.visibility = View.INVISIBLE
                }
                .start()
        }
    }

    private fun setEditText() = with(binding) {
        descriptionEditText.addTextChangedListener(object : TextWatcher {
            var descText = ""
            var cursorPosition = 0

            override fun afterTextChanged(s: Editable) {
                descriptionEditText.removeTextChangedListener(this)

                if (descriptionEditText.lineCount > descriptionEditText.maxLines) {
                    descriptionEditText.setText(descText)
                    descriptionEditText.setSelection(cursorPosition)
                } else {
                    descText = descriptionEditText.text.toString()
                    cursorPosition = descriptionEditText.selectionEnd
                }
                descriptionEditText.addTextChangedListener(this)
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }
        })
    }

    /**
     * Gets span factory for values in the summary text.
     */
    protected abstract val valueSpanFactory: TextFormatter.SpanFactory

    /**
     * Gets span factory for other parts than values in the summary text.
     */
    protected abstract val textSpanFactory: TextFormatter.SpanFactory

    fun startEditIconShowAnimation() {
        if (runningShowAnimations.isNotEmpty()) {
            // animations already running, returning
            return
        }
        shownEditIcons?.forEachIndexed { index, view ->
            val anim = view.animate()
                .alpha(1f)
                .setInterpolator(DecelerateInterpolator())
                .setStartDelay(index * 150L)
                .withStartAction {
                    view.visibility = View.VISIBLE
                }
            anim.withEndAction {
                runningShowAnimations -= anim
                if (runningShowAnimations.isEmpty()) {
                    startEditIconHideAnimation()
                }
            }
            runningShowAnimations += anim
            anim.start()
        }
    }

    fun setSportieInfoIndexes(sportieInfo: SportieInfo) {
        firstDataWorkoutValueIndex = sportieInfo.firstDataIndex
        secondDataWorkoutValueIndex = sportieInfo.secondDataIndex
        thirdDataWorkoutValueIndex = sportieInfo.thirdDataIndex
        graphIndex = sportieInfo.graphIndex
        showActivityType = sportieInfo.showActivityType
        showStartTime = sportieInfo.showStartTime
    }

    suspend fun setWorkout(
        sportieItem: SportieItem,
        measurementUnit: MeasurementUnit,
        sportieInfo: SportieInfo,
        initialSummaryItems: List<SummaryItem> = emptyList(),
    ) {
        setSportieInfoIndexes(sportieInfo)

        binding.descriptionText.text = sportieInfo.descriptionText

        setSummaryText(sportieItem, initialSummaryItems)

        graphOptions = getActivityGraphOptions(sportieItem, measurementUnit)

        if (graphOptions.size > 1) { // Always contains SummaryGraph.NONE
            shownEditIcons = editIcons
            setupGraphClickListeners()
            setupGraph(sportieItem, measurementUnit)
        } else {
            shownEditIcons = editIcons.minus(binding.editIconGraph)
        }
    }

    private fun setSummaryText(
        sportieItem: SportieItem,
        initialSummaryItems: List<SummaryItem>,
    ) {
        getActivityWorkoutValues(
            sportieItem = sportieItem,
            sml = sportieItem.sml,
            activityWindow = sportieItem.sml.getActivityWindow(null),
        )

        updateSummaryLabels(initialSummaryItems)
    }

    private fun setupGraphClickListeners() = with(binding) {
        for (view in listOf(graphContainer, graphView, editIconGraph)) {
            view.setOnClickListener {
                sportieOverlayViewClickListener?.onGraphClick(graphOptions, graphIndex)
            }
        }
    }

    fun setEditMode(enabled: Boolean, showKeyboard: Boolean) = with(binding) {
        val inputMethodManager = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        if (enabled) {
            darkOverlay.visibility = View.VISIBLE
            descriptionEditText.isEnabled = true
            descriptionEditText.setText(descriptionText.text)
            descriptionEditText.visibility = View.VISIBLE
            descriptionText.visibility = View.GONE
            if (showKeyboard) {
                descriptionEditText.requestFocus()
                inputMethodManager.showSoftInput(descriptionEditText, 0)
            }
        } else {
            darkOverlay.visibility = View.GONE
            descriptionText.text = descriptionEditText.text.toString()
            descriptionText.visibility = View.VISIBLE
            descriptionEditText.isEnabled = false
            descriptionEditText.visibility = View.GONE
        }
    }

    fun getSportieSelection() = SportieSelection(
        firstWorkoutValue = firstWorkoutValue,
        secondWorkoutValue = secondWorkoutValue,
        thirdWorkoutValue = thirdWorkoutValue,
        isShareCustomised = isShareCustomised,
        sportieGraphType = graphType
    )

    private suspend fun setupGraph(
        graphIndex: Int,
        sportieItem: SportieItem,
        measurementUnit: MeasurementUnit
    ) {
        this.graphIndex = graphIndex
        setupGraph(sportieItem, measurementUnit)
    }

    private suspend fun setupGraph(
        sportieItem: SportieItem,
        measurementUnit: MeasurementUnit,
    ) {
        val graphOption = graphOptions.getOrNull(graphIndex) ?: return
        graphType = graphOption.type
        if (graphType != GraphType.NONE) {
            with(binding.graphContainer) {
                alpha = 1f
                title = graphOption.name
                unit = graphOption.unit
            }
            setGraphData(sportieItem, measurementUnit)
        } else {
            binding.graphContainer.alpha = 0f
        }
    }

    private suspend fun setGraphData(
        sportieItem: SportieItem,
        measurementUnit: MeasurementUnit,
    ) {
        val graphType = graphType ?: return
        when {
            graphType.isDiveGraph() &&
                sportieItem.workoutHeader.activityType.supportsDiveProfile -> {
                    val diveExtension = sportieItem.workoutExtensions
                        .firstOfType<DiveExtension>()
                        ?: return
                    graphView.drawDiveGraph(
                        graphType,
                        diveExtension,
                        sportieItem.sml,
                        measurementUnit
                    ).await()
                }

            graphType == GraphType.Summary(SummaryGraph.HEARTRATE) ->
                graphView.drawHeartRateGraph(
                    sportieItem.hrEvents,
                    SportieHelper.MAX_HR_VALUES_SHARE
                ).await()

            else -> {
                graphView.drawGraph(
                    sportieItem.workoutExtensions.firstOfType<SummaryExtension>().isSuuntoRun(),
                    graphType,
                    sportieItem.geoPoints,
                    sportieItem.workoutHeader,
                    sportieItem.sml,
                    measurementUnit,
                ).await()
            }
        }
    }

    private fun updateSummaryLabels(defaultSummaryItems: List<SummaryItem>) = with(binding) {
        if (defaultSummaryItems.isNotEmpty()) {
            defaultSummaryItems.take(3).forEachIndexed { index, summaryItem ->
                activityWorkoutValues.indexOfFirst { it.item == summaryItem }.takeIf { it != -1 }
                    ?.let {
                        when (index) {
                            0 -> firstDataWorkoutValueIndex = it
                            1 -> secondDataWorkoutValueIndex = it
                            2 -> thirdDataWorkoutValueIndex = it
                        }
                    }
            }
        }

        // because index == activityWorkoutValues.size - 1 is sportType and startTime
        // firstData, secondData, thirdData shouldn't show it
        if (firstDataWorkoutValueIndex < activityWorkoutValues.size - 1) {
            activityWorkoutValues.getOrNull(firstDataWorkoutValueIndex).let {
                firstWorkoutValue = it
                setSummaryData(firstData, it)
            }
        }
        if (secondDataWorkoutValueIndex < activityWorkoutValues.size - 1) {
            activityWorkoutValues.getOrNull(secondDataWorkoutValueIndex).let {
                secondWorkoutValue = it
                setSummaryData(secondData, it)
            }
        }
        if (thirdDataWorkoutValueIndex < activityWorkoutValues.size - 1) {
            activityWorkoutValues.getOrNull(thirdDataWorkoutValueIndex).let {
                thirdWorkoutValue = it
                setSummaryData(thirdData, it)
            }
        }
        // sportType and startTime
        setSportTypeData(fourthData,
            activityWorkoutValues.getOrNull(activityWorkoutValues.size - 1)
        )
    }

    private fun setSportTypeData(textView: TextView, workoutValue: WorkoutValue?) {
        val label = workoutValue?.label
        val text =
            if (label != null) String.format(Locale.getDefault(), "\n%s", label) else ""
        val textSpannable = SpannableString(text)
        for (span in textSpanFactory.createSpans()) {
            textSpannable.setSpan(
                span,
                0,
                textSpannable.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        // In order to keep the measurement of the view unchanged, the display and hiding are controlled by setting transparency here
        if (workoutValue?.showActivityStartTime == false) {
            textSpannable.setSpan(
                ForegroundColorSpan(Color.TRANSPARENT),
                0,
                textSpannable.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        val value = workoutValue?.value
        val valueSpannable = SpannableString(value)
        for (span in textSpanFactory.createSpans()) {
            valueSpannable.setSpan(
                span,
                0,
                valueSpannable.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        for (span in valueSpanFactory.createSpans()) {
            valueSpannable.setSpan(
                span,
                0,
                valueSpannable.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        if (workoutValue?.showActivityType == false) {
            valueSpannable.setSpan(
                ForegroundColorSpan(Color.TRANSPARENT),
                0,
                valueSpannable.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        textView.text = TextUtils.concat(
            valueSpannable,
            textSpannable
        )
    }

    private fun getActivityWorkoutValues(
        sportieItem: SportieItem,
        sml: Sml,
        activityWindow: SuuntoLogbookWindow?,
    ) {
        val (workoutValueFactory, summaryItems) = SportieUtils.extractWorkoutValuesForSportie(
            sportieItem = sportieItem,
            sml = sml,
            activityWindow = activityWindow,
        )

        // resetting activity workout values
        activityWorkoutValues.clear()
        activityWorkoutValues.add(WorkoutValue(SummaryItem.NONE, "", resources.getString(R.string.none)))
        val items = workoutValueFactory.getValueForItems(summaryItems) +
            workoutValueFactory.getSuuntoPlusValues()
        activityWorkoutValues.addAll(
            items.filter {
                when (it.item) {
                    SummaryItem.DIVETIME -> activityWindow?.diveTime != 0.0f
                    SummaryItem.DIVEINWORKOUT -> activityWindow?.diveInWorkout != 0
                    SummaryItem.DIVERECOVERYTIME -> activityWindow?.diveRecoveryTime != 0.0f
                    SummaryItem.DIVETIMEMAX -> activityWindow?.diveTimeMax != 0.0f
                    else -> it.value != null && it.label.isNotEmpty()
                }
            }
        )
        // add activityType and startTime,this value
        val startTime = sportieItem.workoutHeader.startTime
        val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm")
        val sportTypeWorkValue = WorkoutValue(
            value = sportieItem.workoutHeader.activityType.getLocalizedName(resources),
            label = dateTimeFormatter.format(
                LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(startTime),
                    ZoneId.systemDefault()
                )
            ),
            showActivityType = showActivityType,
            showActivityStartTime = showStartTime
        )
        // This value is added to the end, the value index is activityWorkoutValues.size()-1
        activityWorkoutValues.add(sportTypeWorkValue)
    }

    private suspend fun getActivityGraphOptions(
        sportieItem: SportieItem,
        measurementUnit: MeasurementUnit,
    ): List<WorkoutShareGraphOption> = withContext(coroutinesDispatchers.io) {
        val activityType = sportieItem.workoutHeader.activityType
        buildList {
            add(GraphType.Summary(SummaryGraph.NONE))

            addAll(WorkoutAnalysisHelper.createGraphList(activityType, sportieItem.geoPoints, sportieItem.sml))

            // Depth graph is added separately here
            if (activityType.supportsDiveProfile) {
                // Only add it if there is already not added and there are data
                val depthGraphType = GraphType.Summary(SummaryGraph.DEPTH)
                if (sportieItem.sml.streamData.depth.isNotEmpty() && !contains(depthGraphType)) {
                    add(depthGraphType)
                } else if (sportieItem.sml.streamData.depth.none { it.value != 0f }) {
                    // If no data or all values are 0 try to remove
                    remove(depthGraphType)
                }
            }

            val hasHrData = sportieItem.hrEvents.isNotEmpty()
            if (hasHrData) {
                add(GraphType.Summary(SummaryGraph.HEARTRATE))
            }
        }.map { graphType ->
            val unit = WorkoutAnalysisHelper.getGraphUnitStringRes(
                infoModelFormatter,
                graphType,
                measurementUnit,
                activityType.isSwimming,
            )?.let(context::getString)
                .orEmpty()

            WorkoutShareGraphOption(
                graphType,
                name = WorkoutAnalysisHelper.getGraphNameTitle(context, graphType),
                unit = unit,
            )
        }
    }

    private fun setSummaryData(textView: TextView, workoutValue: WorkoutValue?) {
        // if null is passed as value we generate a mock WorkoutValue with hidden labels so that the measured textview
        // has a realistic size even though it is hidden.
        val value = workoutValue ?: WorkoutValue(item = SummaryItem.NONE, value = "00:00", label = "label")
        textView.alpha = if (value.item == SummaryItem.NONE) 0f else 1f
        textView.text = TextFormatter.formatWorkoutValue(
            resources,
            value,
            valueSpanFactory,
            textSpanFactory,
            true
        )
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)

        with(binding) {
            val width = MeasureSpec.getSize(widthMeasureSpec)
            val height = MeasureSpec.getSize(heightMeasureSpec)

            // Calculate maximum width for the summary texts based on the dimensions of the whole overlay.
            val summaryMaxWidth = (width * SUMMARY_TEXT_WIDTH_TO_VIEW_WIDTH_RATIO).roundToInt()

            // Set font size based on the smaller dimension, width or height to get same font size for images in landscape
            // and portrait mode.
            // The actual font size will be smaller if the text won't fit.
            val referenceDimension = min(width, height)
            val summaryMaxFontSize = (summaryMaxWidth * SUMMARY_TEXT_FONT_SIZE_TO_REF_DIM_RATIO).roundToInt()

            if (summaryMaxFontSize < 1) {
                // No room for text. Measure all views with zero size.
                val zeroMeasureSpec = MeasureSpec.makeMeasureSpec(0, MeasureSpec.EXACTLY)
                firstData.measure(zeroMeasureSpec, zeroMeasureSpec)
                secondData.measure(zeroMeasureSpec, zeroMeasureSpec)
                thirdData.measure(zeroMeasureSpec, zeroMeasureSpec)
                fourthData.measure(zeroMeasureSpec, zeroMeasureSpec)
                bgGradient.measure(zeroMeasureSpec, zeroMeasureSpec)
                topLogo.measure(zeroMeasureSpec, zeroMeasureSpec)
                graphContainer.measure(zeroMeasureSpec, zeroMeasureSpec)
                return
            }

            for (view in listOf<View>(descriptionEditText, descriptionText)) {
                view.measure(
                    MeasureSpec.makeMeasureSpec(
                        (width * DESCRIPTION_TEXT_WIDTH_WEIGHT).roundToInt(),
                        MeasureSpec.EXACTLY
                    ),
                    MeasureSpec.makeMeasureSpec(
                        (height * DESCRIPTION_TEXT_HEIGHT_WEIGHT).roundToInt(),
                        MeasureSpec.EXACTLY
                    )
                )
            }

            autoScaleSummaryTexts(summaryMaxFontSize, summaryMaxWidth, height / 4)

            // we want the margin to be proportional to text view height
            margin = firstData.measuredHeight / 8
            marginValuesToGraph = margin / 4
            logoTopMargin = margin
            // Gradient heights are based on summary text height.
            measureGradients(width, height)

            darkOverlay.measure(
                MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY),
                MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY)
            )

            measureTopLogo()
            measureGraph(referenceDimension, width, height)
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        binding.bgGradient.layout(left, top, right, bottom)
        binding.darkOverlay.layout(left, top, right, bottom)

        val horizontalCenter = (left + right) / 2
        // make firstData secondData thirdData align top, based on the max height of them
        val maxHeight = binding.run {
            listOf(firstData, secondData, thirdData).maxOfOrNull {
                it.measuredHeight
            }
        }
        layoutFirstData(left, bottom, margin, maxHeight)
        layoutThirdData(right, bottom, margin, maxHeight)
        // Second data is dependent on layouts of first and third
        layoutSecondData(bottom, margin, maxHeight)
        // Top logo is centered.
        layoutLogo(horizontalCenter, top, logoTopMargin)
        layoutDescriptionText(top, horizontalCenter, margin)
        layoutGraph(left, margin, right - left)
        layoutFourData(margin, right)
    }

    private fun layoutFourData(margin: Int, right: Int) = with(binding) {
        fourthData.layout(
            right - fourthData.measuredWidth - margin,
            editIconGraph.top - margin - fourthData.measuredHeight,
            right - margin,
            editIconGraph.top - margin
        )

        layoutEditIcon(editIconData4, fourthData)
    }

    private fun layoutDescriptionText(top: Int, horizontalCenter: Int, margin: Int) =
        with(binding) {
            val descLeft = horizontalCenter - descriptionText.measuredWidth / 2
            val descTop = top + topLogo.measuredHeight + margin
            for (view in listOf<View>(descriptionEditText, descriptionText)) {
                view.layout(
                    descLeft,
                    descTop,
                    descLeft + view.measuredWidth,
                    descTop + view.measuredHeight
                )
            }
        }

    private fun layoutEditIcon(editIcon: View, anchor: View) {
        with(editIcon) {
            layout(
                anchor.right - measuredWidth + paddingRight,
                anchor.top - measuredHeight - xxSmallSpacing + paddingBottom,
                anchor.right + paddingRight,
                anchor.top - xxSmallSpacing + paddingBottom
            )
        }
    }

    private fun layoutFirstData(left: Int, bottom: Int, margin: Int, topOffset: Int?) = with(binding) {
        firstData.layout(
            left + margin,
            bottom - margin - (topOffset ?: firstData.measuredHeight),
            left + margin + firstData.measuredWidth,
            bottom - margin
        )

        layoutEditIcon(editIconData1, firstData)
    }

    private fun layoutSecondData(bottom: Int, margin: Int, topOffset: Int?) = with(binding) {
        val firstDataRight = left + margin + firstData.measuredWidth
        val thirdDataLeft = right - margin - thirdData.measuredWidth
        val secondLeft = firstDataRight + (thirdDataLeft - firstDataRight - secondData.measuredWidth) / 2
        secondData.layout(
            secondLeft,
            bottom - margin - (topOffset ?: secondData.measuredHeight),
            secondLeft + secondData.measuredWidth,
            bottom - margin
        )

        layoutEditIcon(editIconData2, secondData)
    }

    private fun layoutThirdData(right: Int, bottom: Int, margin: Int, topOffset: Int?) = with(binding) {
        thirdData.layout(
            right - margin - thirdData.measuredWidth,
            bottom - margin - (topOffset ?: thirdData.measuredHeight),
            right - margin,
            bottom - margin
        )

        layoutEditIcon(editIconData3, thirdData)
    }

    private fun layoutLogo(horizontalCenter: Int, top: Int, margin: Int) = with(binding) {
        val logoLeft = horizontalCenter - topLogo.measuredWidth / 2
        topLogo.layout(
            logoLeft,
            top + margin,
            logoLeft + topLogo.measuredWidth,
            top + topLogo.measuredHeight + margin
        )
    }

    private fun layoutGraph(left: Int, margin: Int, parentWidth: Int) = with(binding) {
        graphContainer.layout(
            left + margin,
            firstData.top - marginValuesToGraph - graphContainer.measuredHeight,
            parentWidth - margin,
            firstData.top - marginValuesToGraph
        )

        layoutEditIcon(editIconGraph, graphContainer)
    }

    /**
     * Measure top logo. Keep the original aspect ratio of the logo.
     */
    private fun measureTopLogo() = with(binding) {
        val topLogoDrawable = topLogo.drawable
        val topLogoHeight = firstData.measuredHeight * LOGO_HEIGHT_TO_REF_SUMMARY_TEXT_RATIO
        val topLogoWidth = topLogoHeight * topLogoDrawable.intrinsicWidth / topLogoDrawable.intrinsicHeight
        topLogo.measure(
            MeasureSpec.makeMeasureSpec(topLogoWidth.roundToInt(), MeasureSpec.EXACTLY),
            MeasureSpec.makeMeasureSpec(topLogoHeight.roundToInt(), MeasureSpec.EXACTLY)
        )
    }

    private fun measureGradients(width: Int, height: Int) {
        binding.bgGradient.measure(
            MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY),
            MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY)
        )
    }

    private fun measureGraph(referenceDimension: Int, width: Int, height: Int) = with(binding) {
        graphContainer.setFontSize(
            TypedValue.COMPLEX_UNIT_PX,
            firstData.textSize * GRAPH_FONT_SIZE_TO_REF_SUMMARY_TEXT_RATIO
        )
        graphContainer.setTextMargin((GRAPH_TEXT_MARGIN_TO_REF_DIM_RATIO * referenceDimension).roundToInt())
        graphContainer.setLineWidth(GRAPH_LINE_WIDTH_TO_REF_DIM_RATIO * referenceDimension)

        // Measure first graph height as percentage of width/height
        val graphHeight = (referenceDimension * GRAPH_HEIGHT_TO_REF_DIM_RATIO).roundToInt().let {
            // Then limit it to the space left from other view items
            val graphHeightLimit =
                height -
                    (
                        margin + topLogo.measuredHeight + margin + descriptionText.measuredHeight + margin +
                            marginValuesToGraph + firstData.measuredHeight + margin
                        )
            min(graphHeightLimit, it)
        }

        graphContainer.measure(
            MeasureSpec.makeMeasureSpec(width - 2 * margin, MeasureSpec.EXACTLY),
            MeasureSpec.makeMeasureSpec(graphHeight, MeasureSpec.EXACTLY)
        )
    }

    /**
     * Auto scale summary text so that the whole text fits to the available space.
     * Measure first using the maximum dimensions. Then layout, which sets the font size.
     * Then measure again with the final font size so that view dimensions wrap the text.
     */
    private fun autoScaleSummaryTexts(summaryMaxFontSize: Int, summaryMaxWidth: Int, height: Int) =
        with(binding) {
            val textViews = listOf(firstData, secondData, thirdData, fourthData)
            val textViewWidth = summaryMaxWidth / 3
            for (textView in textViews) {
                textView.apply {
                    TextViewCompat.setAutoSizeTextTypeUniformWithConfiguration(
                        this,
                        1,
                        summaryMaxFontSize,
                        1,
                        TypedValue.COMPLEX_UNIT_PX
                    )

                    val widthMeasureSpec = when (this) {
                        fourthData -> MeasureSpec.makeMeasureSpec(summaryMaxWidth, MeasureSpec.EXACTLY)
                        else -> MeasureSpec.makeMeasureSpec(textViewWidth, MeasureSpec.EXACTLY)
                    }
                    measure(
                        widthMeasureSpec,
                        MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY)
                    )
                    layout(0, 0, measuredWidth, measuredHeight)
                    TextViewCompat.setAutoSizeTextTypeWithDefaults(
                        this,
                        TextViewCompat.AUTO_SIZE_TEXT_TYPE_NONE
                    )
                }
            }
            setTextViewsTextSizeToMinimumAndMeasure(textViews)
        }

    private fun setTextViewsTextSizeToMinimumAndMeasure(textViews: List<TextView>) = with(binding) {
        textViews.asSequence()
            .map { textView -> textView.textSize }
            .minOrNull()?.let { minTextSize ->
                textViews.forEach {
                    it.apply {
                        setTextSize(TypedValue.COMPLEX_UNIT_PX, minTextSize)
                        measure(
                            MeasureSpec.makeMeasureSpec(measuredWidth, MeasureSpec.AT_MOST),
                            MeasureSpec.makeMeasureSpec(measuredHeight, MeasureSpec.AT_MOST)
                        )
                    }
                }
                descriptionEditText.setTextSize(TypedValue.COMPLEX_UNIT_PX, minTextSize)
                descriptionText.setTextSize(TypedValue.COMPLEX_UNIT_PX, minTextSize)
            }
    }

    fun onWorkoutLabelSelected(selectedTextViewIndex: Int, workoutValueIndex: Int) {
        when (selectedTextViewIndex) {
            FIRST_TEXT_VIEW_INDEX -> {
                firstDataWorkoutValueIndex = workoutValueIndex
            }
            SECOND_TEXT_VIEW_INDEX -> {
                secondDataWorkoutValueIndex = workoutValueIndex
            }
            THIRD_TEXT_VIEW_INDEX -> {
                thirdDataWorkoutValueIndex = workoutValueIndex
            }
        }
        updateSummaryLabels(emptyList())
    }

    fun updateShowActivityAndStartTimeState(
        showNothing: Boolean,
        showActivityType: Boolean,
        showActivityStartTime: Boolean
    ) {
        this.showActivityType = showActivityType
        this.showStartTime = showActivityStartTime
        val activityWorkoutValue = activityWorkoutValues.getOrNull(activityWorkoutValues.size - 1)
        activityWorkoutValue.let {
            val updateWorkoutValue = it?.copy(
                item = if (showNothing) SummaryItem.NONE else null,
                showActivityType = showActivityType,
                showActivityStartTime = showActivityStartTime
            )
            updateWorkoutValue?.let { update ->
                activityWorkoutValues[activityWorkoutValues.size - 1] = update
            }
            setSportTypeData(binding.fourthData, updateWorkoutValue)
        }
    }

    fun getCurrentSportieInfo(): SportieInfo {
        return SportieInfo(
            firstDataWorkoutValueIndex,
            secondDataWorkoutValueIndex,
            thirdDataWorkoutValueIndex,
            binding.descriptionText.text.toString(),
            graphIndex,
            showActivityType,
            showStartTime
        )
    }

    suspend fun onGraphSelected(sportieItem: SportieItem, index: Int, measurementUnit: MeasurementUnit) {
        setupGraph(index, sportieItem, measurementUnit)
    }

    private fun GraphType.isDiveGraph(): Boolean {
        return this == GraphType.Summary(SummaryGraph.DEPTH) ||
            this == GraphType.Summary(SummaryGraph.GASCONSUMPTION) ||
            this == GraphType.Summary(SummaryGraph.TANKPRESSURE)
    }

    companion object {
        private const val GRAPH_HEIGHT_TO_REF_DIM_RATIO = 1f / 3.5f
        private const val SUMMARY_TEXT_WIDTH_TO_VIEW_WIDTH_RATIO = 0.9f
        private const val SUMMARY_TEXT_FONT_SIZE_TO_REF_DIM_RATIO = 0.046f
        private const val GRAPH_FONT_SIZE_TO_REF_SUMMARY_TEXT_RATIO = 2f / 3
        private const val GRAPH_TEXT_MARGIN_TO_REF_DIM_RATIO = 0.012f
        private const val GRAPH_LINE_WIDTH_TO_REF_DIM_RATIO = 0.003f
        private const val LOGO_HEIGHT_TO_REF_SUMMARY_TEXT_RATIO = 3f / 5

        const val DEFAULT_FIRST_DATA_INDEX = 1
        const val DEFAULT_SECOND_DATA_INDEX = 2
        const val DEFAULT_THIRD_DATA_INDEX = 3

        private const val FIRST_TEXT_VIEW_INDEX = 0
        private const val SECOND_TEXT_VIEW_INDEX = 1
        private const val THIRD_TEXT_VIEW_INDEX = 2
        const val FOURTH_TEXT_VIEW_INDEX = 3
        private const val DESCRIPTION_TEXT_WIDTH_WEIGHT = 0.6F
        private const val DESCRIPTION_TEXT_HEIGHT_WEIGHT = 0.3F

        const val DEFAULT_GRAPH_INDEX = 1
    }
}
