package com.stt.android.ui.fragments.workout

import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import androidx.annotation.ColorInt
import androidx.core.content.ContextCompat
import androidx.core.graphics.withClip
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import com.stt.android.R
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workout.WorkoutHrEvent
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.DiveExtension
import io.reactivex.Completable
import com.stt.android.core.R as CR

class WorkoutLineChartShare @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : WorkoutLineChartBase(context, attrs, defStyle) {

    var progressive = false
    var ratio = 0f
        set(value) {
            field = value
            invalidate()
        }

    var parentChartView: YAxisView? = null
    var axisMinValueText: String = ""
    var axisMaxValueText: String = ""

    init {
        lineColor = ContextCompat.getColor(context, CR.color.sharing_chart_line_color)
        fillColorStart = ContextCompat.getColor(context, R.color.sharing_chart_trend_fill_color_start)
        fillColorEnd = ContextCompat.getColor(context, R.color.sharing_chart_trend_fill_color_end)

        setTouchEnabled(true)
        extraTopOffset = 0f
        extraBottomOffset = 0f

        with(xAxis) {
            isEnabled = false
            setDrawGridLines(false)
        }

        with(axisRight) {
            isEnabled = true
            setDrawZeroLine(false)
            setDrawLabels(false)
            setDrawGridLines(false)
            setDrawAxisLine(false)
        }
    }

    override fun drawDiveGraph(
        graphType: GraphType,
        diveExtension: DiveExtension?,
        sml: Sml,
        measurementUnit: MeasurementUnit,
        drawEvents: Boolean
    ): Completable =
        super.drawDiveGraph(graphType, diveExtension, sml, measurementUnit, drawEvents)
            .doOnComplete(::onChartDataReady)

    override fun drawGraph(
        isSuuntoRun: Boolean,
        graphType: GraphType,
        geoPoints: List<WorkoutGeoPoint>,
        workoutHeader: WorkoutHeader,
        sml: Sml?,
        measurementUnit: MeasurementUnit
    ): Completable =
        super.drawGraph(isSuuntoRun, graphType, geoPoints, workoutHeader, sml, measurementUnit)
            .doOnComplete(::onChartDataReady)

    override fun drawHeartRateGraph(
        hrEvents: List<WorkoutHrEvent>,
        maxHeartRateValues: Int,
        multisportPartActivity: MultisportPartActivity?
    ): Completable =
        super.drawHeartRateGraph(hrEvents, maxHeartRateValues, multisportPartActivity)
            .doOnComplete(::onChartDataReady)

    override fun setExtraBottomOffset(offset: Float) {
        super.setExtraBottomOffset(offset)
        parentChartView?.bottomOffsetChanged(extraBottomOffset)
    }

    override fun setExtraTopOffset(offset: Float) {
        super.setExtraTopOffset(offset)
        parentChartView?.topOffsetChanged(extraTopOffset)
    }

    override fun addAvgLine(avgValue: Float) {}

    // Draw graph always filled
    override fun prepareXYAxis(
        lineData: LineData,
        minValue: Float?,
        maxValue: Float?,
        minRange: Float?,
        inverted: Boolean,
        yValueFormatter: ValueFormatter,
        xValueFormatter: ValueFormatter
    ) {
        with(axisRight) {
            axisMinimum = minValue ?: lineData.getDataSetByIndex(0).yMin
            axisMaximum = if (minRange == null) {
                maxValue ?: lineData.getDataSetByIndex(0).yMax
            } else {
                getMaxFromMinRange(minValue, lineData, inverted, maxValue, minRange)
            }
            isInverted = inverted
            this.valueFormatter = yValueFormatter
        }
    }

    override fun createLineDataSet(
        entries: List<Entry?>,
        @ColorInt color: Int,
        isFilled: Boolean,
        label: String,
        invertFillColors: Boolean
    ): LineDataSet = super.createLineDataSet(entries, color, true, label, invertFillColors)

    private fun onChartDataReady() {
        axisMinValueText = axisRight.run { valueFormatter.getFormattedValue(axisMinimum) }
        parentChartView?.minValueTextChanged(axisMinValueText)
        axisMaxValueText = axisRight.run { valueFormatter.getFormattedValue(axisMaximum) }
        parentChartView?.maxValueTextChanged(axisMaxValueText)
    }

    override fun draw(canvas: Canvas) {
        if (progressive) {
            canvas.withClip(0f, 0f, width * ratio, height.toFloat()) {
                super.draw(this)
            }
        } else {
            super.draw(canvas)
        }
    }
}
