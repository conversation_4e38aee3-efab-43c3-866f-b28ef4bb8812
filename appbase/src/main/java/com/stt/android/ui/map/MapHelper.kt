package com.stt.android.ui.map

import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.text.method.LinkMovementMethod
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.widget.TextView
import androidx.core.content.edit
import androidx.core.graphics.toColorInt
import androidx.core.text.HtmlCompat
import androidx.core.view.isVisible
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.R
import com.stt.android.domain.Point
import com.stt.android.domain.sml.RecordingStatusEvent
import com.stt.android.domain.sml.RecordingStatusEventType
import com.stt.android.domain.sml.SmlStreamData
import com.stt.android.domain.user.HeatmapType
import com.stt.android.domain.user.RoadSurfaceType
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.maps.MapType
import com.stt.android.maps.SuuntoCameraUpdate
import com.stt.android.maps.SuuntoChinaOfflineRegion
import com.stt.android.maps.SuuntoHeatmapLayerOptions
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMaps
import com.stt.android.maps.SuuntoOfflineRegionTileOverlayOptions
import com.stt.android.maps.SuuntoSelectedTopRoutesLayerOptions
import com.stt.android.maps.SuuntoStartingPointsLayerOptions
import com.stt.android.maps.SuuntoTileOverlay
import com.stt.android.maps.SuuntoTileOverlayOptions
import com.stt.android.maps.SuuntoTileSource
import com.stt.android.maps.SuuntoTopRoutesLayerOptions
import com.stt.android.maps.SuuntoUiSettings
import com.stt.android.maps.TopRouteType
import com.stt.android.maps.newLatLngBounds
import com.stt.android.maps.newLatLngZoom
import com.stt.android.utils.STTConstants
import timber.log.Timber

object MapHelper {

    const val TOP_ROUTES_MIN_ZOOM = 4
    private const val TOP_ROUTES_MAX_ZOOM = 14 // Vector source has no data beyond level 14

    private const val HEATMAP_MAX_ZOOM = 14
    private const val TOP_ROUTES_COLOR = "#808bb4e2"
    private const val TOP_ROUTES_COLOR_SELECTED = "#ff047BFF"

    // Tile source has no data beyond zoom level 9
    private const val STARTING_POINTS_MAX_ZOOM = 9

    private var defaultZoomLevel = -1.0f

    const val PROVIDER_AMAP_NAME = "AMap"
    const val PROVIDER_MAPBOX_NAME = "Mapbox"

    @JvmStatic
    fun updateMapType(map: SuuntoMap, mapType: MapType, credit: TextView?) {
        map.setMapType(mapType.name)

        if (credit != null) {
            updateCreditText(mapType, credit)
        }
    }

    fun addHeatmapOverlay(map: SuuntoMap, heatmapType: HeatmapType): SuuntoTileOverlay? =
        map.addTileOverlay(createHeatmapTileOverlayOptions(heatmapType))

    fun addOfflineRegionOverlay(map: SuuntoMap, geoJsonUrl: String, id: String, color: Int): SuuntoTileOverlay? =
        map.addTileOverlay(createOfflineRegionTileOverlayOptions(id, geoJsonUrl, color))

    fun addChinaOfflineRegionOverlay(map: SuuntoMap, id: String, geoJsonUrl: String, color: Int): SuuntoChinaOfflineRegion {
        return SuuntoChinaOfflineRegion(
            id,
            map.addTileOverlay(createOfflineRegionTileOverlayOptions(id, geoJsonUrl, Color.WHITE)),
            map.addTileOverlay(createChinaOfflineRegionMaskTileOverlayOptions(id, geoJsonUrl, color))
        )
    }

    fun addOfflineRegionMaskOverlay(map: SuuntoMap, geoJsonUrl: String): SuuntoTileOverlay? =
        map.addTileOverlay(createOfflineRegionMaskTileOverlayOptions(geoJsonUrl))

    fun addRoadSurfaceOverlay(
        map: SuuntoMap,
        roadSurfaceTypes: List<RoadSurfaceType>,
        hideCyclingForbiddenRoads: Boolean
    ) = map.addTileOverlayGroup(
        roadSurfaceTypes.map {
            it.getTileOverlayOptions(hideCyclingForbiddenRoads = hideCyclingForbiddenRoads)
        }
    )

    fun addStartingPointsOverlay(map: SuuntoMap, heatmapType: HeatmapType): SuuntoTileOverlay? =
        map.addTileOverlay(createStartingPointsTileOverlayOptions(heatmapType))

    fun addTopRoutesOverlay(
        map: SuuntoMap,
        topRouteType: TopRouteType,
        isSelectedRouteOverlay: Boolean,
        isTransparent: Boolean
    ): SuuntoTileOverlay? {
        return map.addTileOverlay(
            createTopRoutesTileOverlayOptions(topRouteType, isSelectedRouteOverlay, isTransparent)
        )
    }

    private fun createTopRoutesTileOverlayOptions(
        topRouteType: TopRouteType,
        isSelectedRouteOverlay: Boolean,
        isTransparent: Boolean
    ): SuuntoTileOverlayOptions {
        val tileSource = SuuntoTileSource(
            topRouteType.tileUrl,
            512,
            SuuntoTileSource.Scheme.XYZ
        ).apply {
            minZoom = TOP_ROUTES_MIN_ZOOM
            maxZoom = TOP_ROUTES_MAX_ZOOM
        }

        val layerTypeOptions = if (isSelectedRouteOverlay) {
            SuuntoSelectedTopRoutesLayerOptions(
                topRouteType.name,
                TOP_ROUTES_COLOR_SELECTED.toColorInt()
            )
        } else {
            SuuntoTopRoutesLayerOptions(
                topRouteType.name,
                TOP_ROUTES_COLOR.toColorInt()
            )
        }

        return SuuntoTileOverlayOptions(
            // Cannot set fully transparent or otherwise we cannot access rendered features in mapbox
            opacity = if (isTransparent) 0.0001f else 1f,
            tileSource = tileSource,
            layerTypeOptions = layerTypeOptions
        )
    }

    @JvmStatic
    fun createHeatmapTileOverlayOptions(heatmapType: HeatmapType): SuuntoTileOverlayOptions {
        val tileSource = SuuntoTileSource(
            heatmapType.tileUrlTemplate,
            256,
            SuuntoTileSource.Scheme.XYZ
        )

        tileSource.maxZoom = HEATMAP_MAX_ZOOM

        return SuuntoTileOverlayOptions(
            tileSource = tileSource,
            layerTypeOptions = SuuntoHeatmapLayerOptions(heatmapType.name)
        )
    }

    @JvmStatic
    private fun createOfflineRegionTileOverlayOptions(
        id: String,
        geoJsonUrl: String,
        color: Int,
    ): SuuntoTileOverlayOptions = SuuntoTileOverlayOptions(
        tileSource = SuuntoTileSource(geoJsonUrl),
        layerTypeOptions = SuuntoOfflineRegionTileOverlayOptions("offline_region$id", color),
        zIndex = 1f,
    )

    private fun createChinaOfflineRegionMaskTileOverlayOptions(id: String, geoJsonUrl: String, color: Int): SuuntoTileOverlayOptions {
        val tileSource = SuuntoTileSource(geoJsonUrl)

        return SuuntoTileOverlayOptions(
            tileSource = tileSource,
            layerTypeOptions = SuuntoOfflineRegionTileOverlayOptions(
                name = "offline_region_mask_$id",
                color = color,
                isMask = true
            ),
            opacity = 0.5f,
            zIndex = 0f,
            sourceId = id
        )
    }

    @JvmStatic
    private fun createOfflineRegionMaskTileOverlayOptions(geoJsonUrl: String): SuuntoTileOverlayOptions {
        val tileSource = SuuntoTileSource(geoJsonUrl)

        return SuuntoTileOverlayOptions(
            tileSource = tileSource,
            layerTypeOptions = SuuntoOfflineRegionTileOverlayOptions(
                name = "offline_region_mask",
                color = "#303030".toColorInt(),
                isMask = true
            ),
            opacity = 0.15f,
            zIndex = 0f
        )
    }

    @JvmStatic
    fun createStartingPointsTileOverlayOptions(heatmapType: HeatmapType): SuuntoTileOverlayOptions {
        val tileSource = SuuntoTileSource(
            heatmapType.startingPointTileUrlTemplate,
            256,
            SuuntoTileSource.Scheme.XYZ
        )
        tileSource.maxZoom = STARTING_POINTS_MAX_ZOOM

        return SuuntoTileOverlayOptions(
            opacity = 0.6f,
            tileSource = tileSource,
            layerTypeOptions = SuuntoStartingPointsLayerOptions(
                heatmapType.name,
                heatmapType.startingPointColor,
                heatmapType.startingPointStroke
            )
        )
    }

    fun updateCreditText(mapType: MapType, credit: TextView) {
        val creditText = mapType.credit
        if (creditText.isNullOrEmpty()) {
            credit.visibility = View.INVISIBLE
        } else {
            credit.movementMethod = LinkMovementMethod.getInstance()
            credit.text = HtmlCompat.fromHtml(creditText, HtmlCompat.FROM_HTML_MODE_LEGACY)
            credit.visibility = View.VISIBLE
        }
    }

    @JvmStatic
    fun getDefaultZoomLevel(context: Context): Float {
        if (defaultZoomLevel < 0) {
            defaultZoomLevel = context.getSharedPreferences(
                STTConstants.MapPreferences.MAP_PREFS_NAME,
                Context.MODE_PRIVATE
            )
                .getFloat(
                    STTConstants.MapPreferences.KEY_ZOOM_LEVEL,
                    STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
                )
        }
        return defaultZoomLevel
    }

    @JvmStatic
    fun setDefaultZoomLevel(context: Context, zoomLevel: Float) {
        context.getSharedPreferences(
            STTConstants.MapPreferences.MAP_PREFS_NAME,
            Context.MODE_PRIVATE
        )
            .edit {
                putFloat(STTConstants.MapPreferences.KEY_ZOOM_LEVEL, zoomLevel)
            }
        defaultZoomLevel = zoomLevel
    }

    @JvmStatic
    fun resetZoomLevel(context: Context) {
        context.getSharedPreferences(
            STTConstants.MapPreferences.MAP_PREFS_NAME,
            Context.MODE_PRIVATE
        )
            .edit {
                putFloat(
                    STTConstants.MapPreferences.KEY_ZOOM_LEVEL,
                    STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
                )
            }
        defaultZoomLevel = STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
    }

    @JvmStatic
    fun getBearing(context: Context): Boolean {
        return context.getSharedPreferences(
            STTConstants.MapPreferences.MAP_PREFS_NAME,
            Context.MODE_PRIVATE
        )
            .getBoolean(
                STTConstants.MapPreferences.KEY_BEARING,
                STTConstants.MapPreferences.DEFAULT_BEARING
            )
    }

    @JvmStatic
    fun setBearing(context: Context, bearing: Boolean) {
        context.getSharedPreferences(
            STTConstants.MapPreferences.MAP_PREFS_NAME,
            Context.MODE_PRIVATE
        )
            .edit {
                putBoolean(STTConstants.MapPreferences.KEY_BEARING, bearing)
            }
    }

    @JvmStatic
    fun resetBearing(context: Context) {
        context.getSharedPreferences(
            STTConstants.MapPreferences.MAP_PREFS_NAME,
            Context.MODE_PRIVATE
        )
            .edit {
                putBoolean(
                    STTConstants.MapPreferences.KEY_BEARING,
                    STTConstants.MapPreferences.DEFAULT_BEARING
                )
            }
    }

    fun moveCameraToFitStartPositions(
        resources: Resources,
        map: SuuntoMap,
        startPositions: List<Point>
    ) {
        var northeastLatitude = -90.0
        var northeastLongitude = -180.0
        var southwestLatitude = 90.0
        var southwestLongitude = 180.0

        startPositions.forEach {
            northeastLatitude = northeastLatitude.coerceAtLeast(it.latitude)
            northeastLongitude = northeastLongitude.coerceAtLeast(it.longitude)
            southwestLatitude = southwestLatitude.coerceAtMost(it.latitude)
            southwestLongitude = southwestLongitude.coerceAtMost(it.longitude)
        }

        map.moveCamera(
            newLatLngBounds(
                LatLngBounds(
                    LatLng(southwestLatitude, southwestLongitude),
                    LatLng(northeastLatitude, northeastLongitude)
                ),
                resources.getDimensionPixelSize(R.dimen.d20)
            )
        )
    }

    @JvmStatic
    fun moveCameraToBoundGeoPoints(
        resources: Resources,
        map: SuuntoMap,
        routePoints: List<WorkoutGeoPoint>
    ) {
        if (routePoints.size == 1) {
            map.moveCamera(
                newLatLngZoom(
                    routePoints[0].latLng,
                    STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
                )
            )
            return
        }

        var northeastLatitude = -90.0
        var northeastLongitude = -180.0
        var southwestLatitude = 90.0
        var southwestLongitude = 180.0

        routePoints.forEach {
            northeastLatitude = northeastLatitude.coerceAtLeast(it.latitude)
            northeastLongitude = northeastLongitude.coerceAtLeast(it.longitude)
            southwestLatitude = southwestLatitude.coerceAtMost(it.latitude)
            southwestLongitude = southwestLongitude.coerceAtMost(it.longitude)
        }

        map.moveCamera(
            newLatLngBounds(
                LatLngBounds(
                    LatLng(southwestLatitude, southwestLongitude),
                    LatLng(northeastLatitude, northeastLongitude)
                ),
                resources.getDimensionPixelSize(R.dimen.d20)
            )
        )
    }

    @JvmStatic
    fun moveCameraToBoundLatLngs(
        resources: Resources,
        map: SuuntoMap,
        bounds: LatLngBounds,
        animate: Boolean
    ): SuuntoCameraUpdate {
        val cameraUpdate = newLatLngBounds(
            bounds,
            resources.getDimensionPixelSize(R.dimen.map_route_padding)
        )

        if (animate) {
            map.animateCamera(cameraUpdate, 200, null)
        } else {
            map.moveCamera(cameraUpdate)
        }

        return cameraUpdate
    }

    @JvmStatic
    fun moveCameraToLatLng(map: SuuntoMap, latLng: LatLng, animate: Boolean): SuuntoCameraUpdate {
        val cameraUpdate = newLatLngZoom(
            latLng,
            STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
        )

        if (animate) {
            map.animateCamera(cameraUpdate, 200, null)
        } else {
            map.moveCamera(cameraUpdate)
        }

        return cameraUpdate
    }

    @JvmStatic
    fun moveCameraToBoundLatLngs(
        resources: Resources,
        map: SuuntoMap,
        mapWidth: Int,
        mapHeight: Int,
        latLngs: List<LatLng>
    ) {
        val padding = resources.getDimensionPixelSize(R.dimen.map_route_padding)
        moveCameraToBoundLatLngs(map, mapWidth, mapHeight, latLngs, padding)
    }

    @JvmStatic
    fun moveCameraToBoundLatLngs(
        map: SuuntoMap,
        mapWidth: Int,
        mapHeight: Int,
        latLngs: List<LatLng>,
        padding: Int
    ) {
        if (latLngs.isEmpty()) return

        if (latLngs.size == 1) {
            map.moveCamera(
                newLatLngZoom(
                    latLngs[0],
                    STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL
                )
            )
            return
        }

        val bounds = LatLngBounds.builder().apply {
            latLngs.forEach { include(it) }
        }.build()

        // Trying to set more padding than fits in the map view may crash. Let's avoid that.
        var constrainedPadding = padding
        if (padding * 2 >= mapWidth || padding * 2 >= mapHeight) {
            Timber.w(
                "Too much padding for map! Padding=$padding, map size=$mapWidth x $mapHeight"
            )
            constrainedPadding = 0
        }

        map.moveCamera(newLatLngBounds(bounds, constrainedPadding))
    }

    /**
     * Convert the sub-list of WorkoutGeoPoint, with position [start, end), to a list of LatLng.
     */
    @JvmStatic
    fun geoPointsToLatLngs(
        geoPoints: List<WorkoutGeoPoint>,
        start: Int,
        end: Int
    ): List<LatLng> {
        val latLngs: MutableList<LatLng> = ArrayList(end - start)
        for (i in start until end) {
            // we do not use iterator here to avoid a potential ConcurrentModificationException
            // where the geoPoints might be modified by RecordWorkoutService
            latLngs.add(geoPoints[i].latLng)
        }
        return latLngs
    }

    /**
     * Applies given paddings & margins to the map, adjusting behavior between
     * Google Maps and Mapbox.
     *
     * With Google Maps only [SuuntoMap.setPadding] is supported, and with Mapbox
     * the logo, attribution and compass can be moved separately. Basically this
     * method first calls [SuuntoMap.setPadding] with appropriate paratemers, and
     * then for Mapbox moves the separately movable UI elements back to where they
     * should be.
     *
     * @param paddingTop With Google Maps, this is ignored. With Mapbox it is used in [SuuntoMap.setPadding].
     *
     * @param paddingBottom Bottom padding to be applied, if [addCreditHeightToBottomPadding] is
     *   true then the height of [mapCreditTextView] is added to this.
     *
     * @param compassTopMargin With Google Maps, this is used in the [SuuntoMap.setPadding] call.
     *   With Mapbox it is used in [SuuntoUiSettings.updateCompassMargins].
     *
     * @param mapboxLogoLeftMargin Moves the Mapbox logo & the attribution horizontally,
     *   can be 0 if there is no bottom sheet or the sheet's corners are not rounded.
     *   If there is a bottom sheet with rounded corners, adding margin to move the logo
     *   above the flat part looks better. Check that this margin is applied to the credit text
     *   separately.
     *
     * @param mapCreditTextView TextView used to show credits for Premium map styles. [paddingBottom]
     *   is applied to this as margin.
     *
     * @param addCreditHeightToBottomPadding Use true if the [mapCreditTextView] is on the same side
     *   of the map as the logo, this makes the logo & attribution to move above the credit if it
     *   is visible.
     */
    @JvmStatic
    fun updateMapPaddingWithDefaults(
        resources: Resources,
        map: SuuntoMap,
        paddingTop: Int,
        paddingBottom: Int,
        compassTopMargin: Int,
        mapboxLogoLeftMargin: Int,
        mapCreditTextView: TextView?,
        addCreditHeightToBottomPadding: Boolean
    ) {
        val avoidMapCreditBottomPadding = if (
            addCreditHeightToBottomPadding &&
            mapCreditTextView?.isVisible == true
        ) {
            val mapCreditHeight = mapCreditTextView.height
            if (mapCreditHeight == 0) {
                // credit was just added & is becoming visible, estimate height from PaintMetricsInt
                val creditFontMetrics = mapCreditTextView.paint.fontMetricsInt
                creditFontMetrics.bottom - creditFontMetrics.top
            } else {
                mapCreditHeight
            }
        } else {
            0
        }

        (mapCreditTextView?.layoutParams as? MarginLayoutParams)?.let {
            it.bottomMargin = paddingBottom
            mapCreditTextView.layoutParams = it
        }

        val isMapbox = map.getProviderName() == PROVIDER_MAPBOX_NAME
        val isAMap = map.getProviderName() == PROVIDER_AMAP_NAME

        val horizontalPadding = if (isMapbox || isAMap) {
            resources.getDimensionPixelSize(
                R.dimen.map_padding_horizontal_mapbox
            )
        } else {
            resources.getDimensionPixelSize(
                R.dimen.map_padding_horizontal_googlemap
            )
        }

        val mapTopPadding = if (isMapbox || isAMap) {
            paddingTop
        } else {
            compassTopMargin
        }

        map.setPadding(
            horizontalPadding,
            mapTopPadding,
            horizontalPadding,
            paddingBottom + avoidMapCreditBottomPadding
        )

        if (isMapbox || isAMap) {
            val mapboxCompassHorizontalPadding = resources.getDimensionPixelSize(
                R.dimen.map_compass_padding_horizontal_mapbox
            )
            map.getUiSettings().updateCompassMargins(
                left = mapboxCompassHorizontalPadding,
                top = compassTopMargin,
                right = mapboxCompassHorizontalPadding
            )

            val defaultLogoMargin =
                resources.getDimensionPixelSize(com.stt.android.maps.mapbox.R.dimen.maps_provider_mapbox_four_dp)
            val defaultAttributionMargin =
                resources.getDimensionPixelSize(com.stt.android.maps.mapbox.R.dimen.maps_provider_mapbox_ninety_two_dp)

            map.getUiSettings().updateLogoMargins(
                left = defaultLogoMargin + mapboxLogoLeftMargin
            )
            map.getUiSettings().updateAttributionMargins(
                left = defaultAttributionMargin + mapboxLogoLeftMargin
            )
        }
    }

    /**
     * Returns a pair of activity routes and non-activity routes.
     */
    fun filterGeoPointsByActivity(
        geoPoints: List<WorkoutGeoPoint>,
        streamData: SmlStreamData,
    ): Pair<List<List<LatLng>>, List<List<LatLng>>>? =
        filterGeoPointsActivityRoutes(geoPoints, streamData)?.let { (inActivityRoutes, nonActivityRoutes) ->
            inActivityRoutes.map { route -> route.map { it.latLng } } to nonActivityRoutes.map { route -> route.map { it.latLng } }
        }


    fun filterGeoPointsActivityRoutes(
        geoPoints: List<WorkoutGeoPoint>,
        streamData: SmlStreamData,
    ): Pair<List<List<WorkoutGeoPoint>>, List<List<WorkoutGeoPoint>>>? {
        if (geoPoints.isEmpty()) {
            return null
        }
        Timber.d("filterGeoPointsByActivity: geo points - %d", geoPoints.size)

        val pauseResumePairs = streamData.findPauseResumeTimePairs() ?: return null
        Timber.d("filterGeoPointsByActivity: pause resume pairs - %s", pauseResumePairs)

        val activityRoutes = mutableListOf<List<WorkoutGeoPoint>>()
        val nonActivityRoutes = mutableListOf<List<WorkoutGeoPoint>>()
        var currentPauseResumeIndex = 0
        var currentPauseResumePair = pauseResumePairs.getOrNull(currentPauseResumeIndex)
        val currentActivityRoute = mutableListOf<WorkoutGeoPoint>()
        geoPoints.forEach { geoPoint ->
            if (currentPauseResumePair == null) {
                // no pause / resume info, just add the point
                currentActivityRoute.add(geoPoint)
                return@forEach
            }

            if (geoPoint.timestamp <= requireNotNull(currentPauseResumePair).first) {
                // still tracking, add the point
                currentActivityRoute.add(geoPoint)
                return@forEach
            }

            if (currentActivityRoute.isNotEmpty()) {
                activityRoutes.add(currentActivityRoute.toList())
                currentActivityRoute.clear()
            }

            if (geoPoint.timestamp >= requireNotNull(currentPauseResumePair).second) {
                // resumed tracking, add the point, update non-activity route and pause / resume info
                currentActivityRoute.add(geoPoint)

                activityRoutes.lastOrNull()
                    ?.lastOrNull()
                    ?.let { geoPointInActivity ->
                        nonActivityRoutes.add(listOf(geoPointInActivity, geoPoint))
                    }

                currentPauseResumeIndex++
                currentPauseResumePair = pauseResumePairs.getOrNull(currentPauseResumeIndex)
            }
        }
        if (currentActivityRoute.isNotEmpty()) {
            activityRoutes.add(currentActivityRoute.toList())
        }

        return activityRoutes to nonActivityRoutes
    }

    /**
     * Returns a list of pause - resume time pairs, or null if missing start or end event.
     */
    fun SmlStreamData.findPauseResumeTimePairs(): List<Pair<Long, Long>>? {
        var startTime = -1L
        var stopTime = -1L
        val pauseResumePairs = mutableListOf<Pair<Long, Long>>()

        events.forEach { event ->
            val statusEvent = event as? RecordingStatusEvent ?: return@forEach
            when (statusEvent.type) {
                RecordingStatusEventType.Start -> startTime = statusEvent.timestamp
                RecordingStatusEventType.Stop -> stopTime = statusEvent.timestamp
                RecordingStatusEventType.Pause -> {
                    if ((pauseResumePairs.lastOrNull()?.second ?: Long.MAX_VALUE) > 0L) {
                        pauseResumePairs.add(statusEvent.timestamp to -1L)
                    }
                }
                RecordingStatusEventType.Resume -> pauseResumePairs.lastOrNull()
                    ?.let { (pauseTime, resumeTime) ->
                        if (resumeTime == -1L) {
                            pauseResumePairs.removeAt(pauseResumePairs.lastIndex)
                            pauseResumePairs.add(pauseTime to statusEvent.timestamp)
                        }
                    }
            }
        }

        if (startTime < 0L || stopTime < 0L) {
            Timber.w("Cannot filter points without valid start or stop events")
            return null
        }

        pauseResumePairs.lastOrNull()
            ?.let { (pauseTime, resumeTime) ->
                if (resumeTime == -1L) {
                    pauseResumePairs.removeAt(pauseResumePairs.lastIndex)
                    pauseResumePairs.add(pauseTime to stopTime)
                }
            }

        return pauseResumePairs
    }

    fun filterLapPointsNotInDashLines(highlightedLapPoints: List<LatLng>, dashLines: List<List<LatLng>>): List<List<LatLng>> {
        val result = mutableListOf<List<LatLng>>()
        var currentIndex = 0
        dashLines.forEach {
            val startIndex = highlightedLapPoints.indexOf(it.first())
            if (startIndex == -1) {
                return@forEach
            }
            if (currentIndex < startIndex) {
                result.addAll(listOf(highlightedLapPoints.subList(currentIndex, startIndex + 1)))
            }
            val endIndex = highlightedLapPoints.indexOf(it.last())
            if (endIndex == -1) {
                return@forEach
            }
            currentIndex = endIndex
        }
        if (currentIndex < highlightedLapPoints.size) {
            result.addAll(listOf(highlightedLapPoints.subList(currentIndex, highlightedLapPoints.size)))
        }
        return result
    }

    fun filterNonHighlightedPointsNotInDashLines(nonHighlightedPoints: List<List<LatLng>>, dashLines: List<List<LatLng>>) : List<List<LatLng>> {
        val result = mutableListOf<List<LatLng>>()
        for (list in nonHighlightedPoints) {
            val filteredList = filterLapPointsNotInDashLines(list, dashLines)
            if (filteredList.isNotEmpty()) {
                result.addAll(filteredList)
            }
        }
        return result
    }

    fun isDefaultProviderAMap(): Boolean {
        return SuuntoMaps.defaultProvider?.name == PROVIDER_AMAP_NAME
    }

    fun provideForced2dMode(providerName: String?): Boolean {
        return when (providerName) {
            PROVIDER_MAPBOX_NAME,
            PROVIDER_AMAP_NAME -> false

            else -> true
        }
    }

    /**
     * Keep the previous code logic, and add AMap map display function selection for places where mapbox was previously used
     * 1. dashboardMapView for old & new home ui
     * 2. workoutSharePreview
     * 3. workoutCoverImage
     */
    fun getMapsProviderNameWithoutGoogle(): String {
        return when (val mapProviderName = SuuntoMaps.getProviderOrDefault().name) {
            PROVIDER_AMAP_NAME -> mapProviderName
            else -> PROVIDER_MAPBOX_NAME
        }
    }

    /**
     * Determine whether the current map supports the waypoint snapshot function
     */
    fun snapshottingWaypointsSupports(providerName: String?): Boolean {
        return when (providerName) {
            PROVIDER_AMAP_NAME,
            PROVIDER_MAPBOX_NAME -> true
            else -> false
        }
    }
}
