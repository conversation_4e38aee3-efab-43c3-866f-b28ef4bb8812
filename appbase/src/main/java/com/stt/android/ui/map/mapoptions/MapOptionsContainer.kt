package com.stt.android.ui.map.mapoptions

import com.stt.android.domain.user.HeatmapType
import com.stt.android.domain.user.RoadSurfaceType
import com.stt.android.maps.MapType
import com.stt.android.ui.map.selection.MyTracksGranularity

data class MapOptionsContainer constructor(
    val selectedMapOptions: SelectedMapOptions,
    val isTurnByTurnOptionVisible: Boolean,
    val isHeatmapOptionVisible: <PERSON>olean,
    val isRoadSurfaceOptionVisible: Boolean,
    val isMyTracksOptionVisible: Boolean,
    val isPOIOptionVisible: Boolean,
    val is3dOptionVisible: Boolean,
    val is3dOptionViewEnabled: Boolean,
    val onMapStyleOptionClick: () -> Unit,
    val onHeatmapOptionClick: () -> Unit,
    val onRoadSurfaceOptionClick: () -> Unit,
    val onMyTracksOptionClick: () -> Unit,
    val onPOIOptionToggled: () -> Unit,
    val onTurnByTurnOptionToggled: () -> Unit,
    val on3dOptionToggled: () -> Unit
)

data class SelectedMapOptions constructor(
    val mapType: MapType?,
    val heatmapType: HeatmapType?,
    val roadSurfaceTypes: List<RoadSurfaceType>,
    val myTracksPeriod: MyTracksGranularity?,
    val showPOIs: Boolean,
    val turnByTurnEnabled: Boolean,
    val map3dEnabled: Boolean,
    val showPopularRoutes: Boolean
)
