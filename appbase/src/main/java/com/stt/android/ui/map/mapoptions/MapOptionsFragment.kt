package com.stt.android.ui.map.mapoptions

import android.graphics.Color
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EdgeEffect
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.lifecycle.map
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.epoxy.EpoxyRecyclerView
import com.airbnb.epoxy.EpoxyViewHolder
import com.stt.android.R
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewStateListFragment2
import com.stt.android.databinding.FragmentMapOptionsBinding
import com.stt.android.extensions.combineLatest
import com.stt.android.extensions.combineLatestAllowNulls
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.map.Map3dEnabledLiveData
import com.stt.android.ui.map.SelectedHeatmapTypeLiveData
import com.stt.android.ui.map.SelectedMapTypeLiveData
import com.stt.android.ui.map.SelectedMyTracksGranularityLiveData
import com.stt.android.ui.map.SelectedRoadSurfaceTypesLiveData
import com.stt.android.ui.map.ShowPOIsLiveData
import com.stt.android.ui.map.ShowPopularRoutesLiveData
import com.stt.android.ui.map.TurnByTurnEnabledLiveData
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class MapOptionsFragment @Inject constructor() :
    ViewStateListFragment2<MapOptionsContainer, MapOptionsViewModel>() {
    override val viewModel: MapOptionsViewModel by viewModels()
    override val layoutId = R.layout.fragment_map_options

    private var listener: MapOptionsListener? = null

    private val binding: FragmentMapOptionsBinding get() = requireBinding()

    @Inject
    lateinit var selectedMapTypeLiveData: SelectedMapTypeLiveData

    @Inject
    lateinit var selectedHeatmapTypeLiveData: SelectedHeatmapTypeLiveData

    @Inject
    lateinit var selectedRoadSurfaceTypesLiveData: SelectedRoadSurfaceTypesLiveData

    @Inject
    lateinit var selectedMyTracksGranularityLiveData: SelectedMyTracksGranularityLiveData

    @Inject
    lateinit var turnByTurnEnabledLiveData: TurnByTurnEnabledLiveData

    @Inject
    lateinit var showPOIsLiveData: ShowPOIsLiveData

    @Inject
    lateinit var showPopularRoutesLiveData: ShowPopularRoutesLiveData

    @Inject
    lateinit var map3dEnabledLiveData: Map3dEnabledLiveData

    @Inject
    lateinit var mapSelectionModel: MapSelectionModel

    private var itemDecoration: RecyclerView.ItemDecoration? = null
            ?: throw RuntimeException("Missing KEY_MAPS_PROVIDER_NAME")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupLiveData()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        setupRecyclerView(binding.list)
        return view
    }

    fun setListener(listener: MapOptionsListener) {
        this.listener = listener
    }

    fun set3dMapOptionViewEnabled(enabled: Boolean) {
        viewModel.set3dOptionViewEnabled(enabled)
    }

    private fun setupLiveData() {
        combineLatest(
            combineLatestAllowNulls(
                selectedMapTypeLiveData,
                selectedHeatmapTypeLiveData,
                selectedRoadSurfaceTypesLiveData

            ),
            combineLatestAllowNulls(
                selectedMyTracksGranularityLiveData,
                showPOIsLiveData,
                turnByTurnEnabledLiveData
            ),
            combineLatestAllowNulls(
                map3dEnabledLiveData,
                showPopularRoutesLiveData
            )
        ).map { (first, second, third) ->
            val (mapType, heatmapType, roadSurfaceTypes) = first
            val (myTracks, showPOIs, turnByTurnEnabled) = second
            val (map3dEnabled, showPopularRoues) = third

            SelectedMapOptions(
                mapType = mapType,
                heatmapType = heatmapType,
                roadSurfaceTypes = roadSurfaceTypes ?: emptyList(),
                myTracksPeriod = myTracks,
                showPOIs = showPOIs ?: true,
                turnByTurnEnabled = turnByTurnEnabled ?: false,
                map3dEnabled = map3dEnabled ?: false,
                showPopularRoutes = showPopularRoues ?: true
            )
        }.observeK(this) { selectedMapOptions ->
            selectedMapOptions?.run { viewModel.update(this) }
        }

        viewModel.onMapOptionClick.observeNotNull(this) { option ->
            listener?.onMapOptionClick(option)
        }
    }

    private fun setupRecyclerView(recyclerView: EpoxyRecyclerView) {
        setupItemDecoration(recyclerView)
        setupItemAnimator(recyclerView)
        setupEdgeEffectFactory(recyclerView)
    }

    private fun setupItemDecoration(recyclerView: EpoxyRecyclerView) {
        val extraMargin = resources.getDimensionPixelOffset(R.dimen.size_spacing_medium)

        itemDecoration = object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                val itemCount = recyclerView.adapter?.itemCount ?: 0
                val childAdapterPosition = recyclerView.getChildAdapterPosition(view)
                if (childAdapterPosition == itemCount - 1) {
                    outRect.right = extraMargin
                }
                outRect.left = extraMargin
            }
        }
        itemDecoration?.let { recyclerView.addItemDecoration(it) }
    }

    private fun setupItemAnimator(recyclerView: EpoxyRecyclerView) {
        recyclerView.itemAnimator = object : DefaultItemAnimator() {
            override fun animateAdd(holder: RecyclerView.ViewHolder?): Boolean {
                return false
            }

            override fun animateRemove(holder: RecyclerView.ViewHolder?): Boolean {
                return false
            }
        }
    }

    private fun setupEdgeEffectFactory(recyclerView: EpoxyRecyclerView) {
        val layoutManager = recyclerView.layoutManager as? LinearLayoutManager
        // Provides bouncy overscroll effect when pulling/flinging a horizontal list
        recyclerView.edgeEffectFactory = object : RecyclerView.EdgeEffectFactory() {
            override fun createEdgeEffect(view: RecyclerView, direction: Int): EdgeEffect {
                val edgeEffect = object : EdgeEffect(view.context) {

                    override fun onPull(deltaDistance: Float) {
                        super.onPull(deltaDistance)
                        handlePull(deltaDistance)
                    }

                    override fun onPull(deltaDistance: Float, displacement: Float) {
                        super.onPull(deltaDistance, displacement)
                        handlePull(deltaDistance)
                    }

                    private fun handlePull(deltaDistance: Float) {
                        // This is called on every touch event while the list is scrolled with a finger.
                        // We simply update the view properties without animation.
                        val sign = if (direction == DIRECTION_RIGHT) -1 else 1
                        val translationXDelta =
                            sign * view.height * deltaDistance * OVERSCROLL_TRANSLATION_MAGNITUDE
                        view.forEachVisibleHolder { holder: EpoxyViewHolder ->
                            if (layoutManager?.orientation == RecyclerView.HORIZONTAL) {
                                (holder.model as? MapOptionItemModel)?.let { model ->
                                    model.translation.cancel()
                                    holder.itemView.translationX += translationXDelta
                                }
                            }
                        }
                    }

                    override fun onRelease() {
                        super.onRelease()
                        // The finger is lifted. This is when we should start the animations to bring
                        // the view property values back to their resting states.
                        view.forEachVisibleHolder { holder: EpoxyViewHolder ->
                            if (layoutManager?.orientation == RecyclerView.HORIZONTAL) {
                                (holder.model as? MapOptionItemModel)?.translation?.start()
                            }
                        }
                    }

                    override fun onAbsorb(velocity: Int) {
                        super.onAbsorb(velocity)
                        val sign = if (direction == DIRECTION_RIGHT) -1 else 1
                        // The list has reached the edge on fling.
                        val translationVelocity = sign * velocity * FLING_TRANSLATION_MAGNITUDE
                        if (layoutManager?.orientation == RecyclerView.HORIZONTAL) {
                            view.forEachVisibleHolder { holder: EpoxyViewHolder ->
                                (holder.model as? MapOptionItemModel)
                                    ?.translation?.setStartVelocity(translationVelocity)
                                    ?.start()
                            }
                        }
                    }
                }
                edgeEffect.color = Color.TRANSPARENT
                return edgeEffect
            }
        }
    }

    override fun onDestroyView() {
        binding.list.adapter = null
        itemDecoration?.let { binding.list.removeItemDecoration(it) }
        binding.list.edgeEffectFactory = RecyclerView.EdgeEffectFactory()
        recyclerView.itemAnimator = null
        listener = null
        super.onDestroyView()
    }

    private inline fun <reified T : EpoxyViewHolder> RecyclerView.forEachVisibleHolder(
        action: (T) -> Unit
    ) {
        for (i in 0 until childCount) {
            action(getChildViewHolder(getChildAt(i)) as T)
        }
    }

    fun interface MapOptionsListener {
        fun onMapOptionClick(option: MapOption)
    }

    companion object {
        const val TAG =
            "com.stt.android.home.explore.mapoptions.MapOptionsFragment"
        const val KEY_MAPS_PROVIDER_NAME =
            "com.stt.android.home.explore.mapoptions.KEY_MAPS_PROVIDER_NAME"
        const val KEY_ANALYTICS_SOURCE =
            "co.stt.android.home.explore.mapoptions.KEY_ANALYTICS_SOURCE"
        const val KEY_SHOW_TURN_BY_TURN_OPTION =
            "com.stt.android.home.explore.mapoptions.KEY_SHOW_TURN_BY_TURN_OPTION"
        const val KEY_SHOW_HEATMAP_OPTION =
            "com.stt.android.home.explore.mapoptions.KEY_SHOW_HEATMAP_OPTION"
        const val KEY_SHOW_ROAD_SURFACE_OPTION =
            "com.stt.android.home.explore.mapoptions.KEY_SHOW_ROAD_SURFACE_OPTION"
        const val KEY_SHOW_MY_TRACKS_OPTION =
            "com.stt.android.home.explore.mapoptions.KEY_SHOW_MY_TRACKS_OPTION"
        const val KEY_SHOW_POI_SETTING =
            "com.stt.android.home.explore.mapoptions.KEY_SHOW_POI_SETTING"
        const val KEY_SHOW_3D_OPTION =
            "com.stt.android.home.explore.mapoptions.KEY_SHOW_3D_OPTION"
        const val KEY_ENABLE_3D_OPTION =
            "com.stt.android.home.explore.mapoptions.KEY_ENABLE_3D_OPTION"

        // The magnitude of translation distance while the list is over-scrolled.
        private const val OVERSCROLL_TRANSLATION_MAGNITUDE = 0.5f

        // The magnitude of translation distance when the list reaches the edge on fling.
        private const val FLING_TRANSLATION_MAGNITUDE = 0.2f

        @JvmStatic
        fun newInstance(
            mapsProviderName: String,
            analyticsSource: String,
            showHeatmapOption: Boolean,
            showRoadSurfaceOption: Boolean,
            showMyTracksOption: Boolean,
            showPOISetting: Boolean,
            showTurnByTurnOption: Boolean = false,
            show3dOption: Boolean = false,
            enable3dOption: Boolean = true,
        ) = MapOptionsFragment().apply {
            arguments = bundleOf(
                KEY_MAPS_PROVIDER_NAME to mapsProviderName,
                KEY_ANALYTICS_SOURCE to analyticsSource,
                KEY_SHOW_HEATMAP_OPTION to showHeatmapOption,
                KEY_SHOW_ROAD_SURFACE_OPTION to showRoadSurfaceOption,
                KEY_SHOW_MY_TRACKS_OPTION to showMyTracksOption,
                KEY_SHOW_POI_SETTING to showPOISetting,
                KEY_SHOW_TURN_BY_TURN_OPTION to showTurnByTurnOption,
                KEY_SHOW_3D_OPTION to show3dOption,
                KEY_ENABLE_3D_OPTION to enable3dOption
            )
        }
    }
}
