package com.stt.android.ui.map.selection

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.map
import com.stt.android.R
import com.stt.android.databinding.ItemMyTracksSelectionBinding
import com.stt.android.core.R as CR

class MyTracksSelectionItem(
    granularity: MyTracksGranularity?,
    val requiresPremium: Boolean,
    viewModel: MapSelectionViewModel,
    lifecycleOwner: LifecycleOwner
) : BaseMapSelectionItem<ItemMyTracksSelectionBinding>(lifecycleOwner) {
    var granularity: MyTracksGranularity? = granularity
        private set

    val isSelected: LiveData<Boolean> =
        viewModel.selectedMyTracksGranularity.map { granularity?.type == it?.type }

    override fun getLayout() = R.layout.item_my_tracks_selection

    val iconRes: Int? = granularity.iconRes()

    val iconSelectedRes: Int? = granularity.iconSelectedRes()

    val titleRes: Int = granularity.titleRes()

    // title precedes titleRes if both are non-null
    val title: String? get() = granularity?.formatCustomDates()

    val showPremiumOverlay: LiveData<Boolean> = viewModel.hasPremium.map { hasPremium ->
        !hasPremium && requiresPremium
    }

    override fun getPrimaryTextColor(context: Context): Int =
        if (requiresPremium) {
            context.getColor(CR.color.dark_grey_st)
        } else {
            super.getPrimaryTextColor(context)
        }

    fun updateRange(
        rangeStartMillis: Long?,
        rangeEndMillis: Long?
    ) {
        this.granularity = this.granularity?.copy(
            rangeStartMillis = rangeStartMillis,
            rangeEndMillis = rangeEndMillis
        )
        notifyChanged()
    }
}
