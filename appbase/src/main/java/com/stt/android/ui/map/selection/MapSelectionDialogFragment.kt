package com.stt.android.ui.map.selection

import android.app.Dialog
import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
import androidx.core.os.bundleOf
import androidx.core.util.Pair
import androidx.core.view.ViewCompat
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.viewModels
import androidx.lifecycle.asLiveData
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.gms.maps.model.LatLng
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.MaterialDatePicker
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue.MapFeatureChanged.HEATMAP
import com.stt.android.analytics.AnalyticsPropertyValue.MapFeatureChanged.MAP_MODE
import com.stt.android.analytics.AnalyticsPropertyValue.MapFeatureChanged.MY_ROUTES
import com.stt.android.analytics.AnalyticsPropertyValue.MapFeatureChanged.ROAD_SURFACE
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.databinding.FragmentMapSelectionBinding
import com.stt.android.di.PremiumRequiredMyTracksGranularityTypes
import com.stt.android.extensions.combineLatest
import com.stt.android.home.explore.ExploreAnalytics
import com.stt.android.inappreview.InAppReviewSource
import com.stt.android.inappreview.InAppReviewTrigger
import com.stt.android.maps.mapbox.MapboxMapsProvider
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.map.MapHelper
import com.stt.android.ui.map.RoadSurfaceInfoDialogFragment
import com.stt.android.ui.map.SelectedMyTracksGranularityLiveData
import com.stt.android.ui.map.mapoptions.MapOption
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment
import com.stt.android.utils.addRequiresPremiumNote
import com.stt.android.utils.createPremiumNoteTagSpannable
import com.stt.android.utils.removeRequiresPremiumNote
import com.stt.android.utils.toCalendarInt
import com.xwray.groupie.GroupAdapter
import com.xwray.groupie.GroupieViewHolder
import com.xwray.groupie.Section
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject
import kotlin.math.max
import kotlin.math.min

@AndroidEntryPoint
class MapSelectionDialogFragment : SmartBottomSheetDialogFragment() {

    @Inject
    lateinit var selectedMyTracksGranularityLiveData: SelectedMyTracksGranularityLiveData

    @Inject
    lateinit var exploreAnalytics: ExploreAnalytics

    @Inject
    lateinit var inAppReviewTrigger: InAppReviewTrigger

    @Inject
    @PremiumRequiredMyTracksGranularityTypes
    lateinit var premiumRequiredMyTracksGranularityTypes: Set<MyTracksGranularity.Type>

    @Inject
    lateinit var mapSelectionModel: MapSelectionModel

    private var _binding: FragmentMapSelectionBinding? = null
    val binding get() = _binding!!

    lateinit var customDatesItem: MyTracksSelectionItem

    private val viewModel: MapSelectionViewModel by viewModels()

    private val mapTypesSection = Section()

    private var selectedMapTypeMadeVisible = false
    private var selectedHeatmapMadeVisible = false
    private var showHeatmaps = false
    private var showRoadSurface = false
    private var showMyTracks = false
    private var showMyPOIsGroup = false
    private var analyticsSource = ""
    private var itemWidth: Int = 0
    private var extraItemPadding: Int = 0

    // This will be used to know what ever the user changed anything in order to mark the screen has visited for the in-app review flow
    private var userChangedSomething: Boolean = false

    private var showPopularRoutes = false

    private val onUnhandledKeyEventListenerCompat by lazy {
        ViewCompat.OnUnhandledKeyEventListenerCompat { _, event ->
            handleUnhandledKeyEvent(event)
        }
    }

    private val onUnhandledKeyEventListener by lazy {
        View.OnUnhandledKeyEventListener { _, event ->
            handleUnhandledKeyEvent(event)
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return super.onCreateDialog(savedInstanceState).apply {
            // Set the dialog window as not focusable. Otherwise the window below the dialog
            // containing the map will lose focus and update slowly when map type is changed.
            window?.addFlags(FLAG_NOT_FOCUSABLE)

            val focusedOption = arguments?.getInt(KEY_FOCUSED_OPTION)?.let { ordinal ->
                MapOption.entries.getOrNull(ordinal)
            } ?: MapOption.MAP_STYLE

            if (this is BottomSheetDialog) {
                setOnShowListener {
                    makeOptionVisible(focusedOption, this)
                }
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        itemWidth =
            inflater.context.resources.getDimensionPixelSize(R.dimen.map_selection_item_width)

        arguments?.run {
            val mapsProviderName = getString(KEY_MAPS_PROVIDER_NAME, "")
            showHeatmaps = !MapHelper.isDefaultProviderAMap() &&
                getBoolean(KEY_SHOW_HEATMAPS, false)
            showRoadSurface = mapsProviderName == MapboxMapsProvider.NAME &&
                getBoolean(KEY_SHOW_ROAD_SURFACE, false)
            showMyTracks = getBoolean(KEY_SHOW_MY_TRACKS, false)
            showPopularRoutes = getBoolean(KEY_SHOW_POPULAR_ROUTES, false)
            showMyPOIsGroup = getBoolean(KEY_SHOW_MY_POIS_GROUP, false)
            analyticsSource = getString(KEY_ANALYTICS_SOURCE, null) ?: ""

            if (containsKey(KEY_MAP_CENTER_LATITUDE) && containsKey(KEY_MAP_CENTER_LONGITUDE)) {
                viewModel.mapCenter = LatLng(
                    getDouble(KEY_MAP_CENTER_LATITUDE),
                    getDouble(KEY_MAP_CENTER_LONGITUDE)
                )
            }

            viewModel.mapsProviderName = mapsProviderName
            viewModel.showHeatmaps = showHeatmaps
        }

        setupObservers()

        _binding = DataBindingUtil.inflate(
            inflater,
            R.layout.fragment_map_selection,
            container,
            false
        )
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    private fun setupObservers() {
        selectedMyTracksGranularityLiveData.observeK(viewLifecycleOwner) {
            if (this::customDatesItem.isInitialized) {
                customDatesItem.updateRange(it?.rangeStartMillis, it?.rangeEndMillis)
            }
        }

        viewModel.hasPremium.observeNotNull(viewLifecycleOwner) {
            if (!it) {
                binding.roadSurfacePremiumNote.isVisible = showRoadSurface
                binding.heatmapTitle.text =
                    binding.heatmapTitle.text.addRequiresPremiumNote(requireContext(), tagStyled = true)
                binding.myTracksTitle.text =
                    binding.myTracksTitle.text.addRequiresPremiumNote(requireContext(), tagStyled = true)
            } else {
                binding.roadSurfacePremiumNote.isVisible = false
                binding.heatmapTitle.text =
                    binding.heatmapTitle.text.removeRequiresPremiumNote(requireContext())
                binding.myTracksTitle.text =
                    binding.myTracksTitle.text.removeRequiresPremiumNote(requireContext())
            }
        }

        combineLatest(
            viewModel.hasPremium,
            mapSelectionModel.hasPremiumRequiredSelections().asLiveData()
        ).observeNotNull(viewLifecycleOwner) { (hasPremium, premiumMapFeaturesSelected) ->
            if (!hasPremium && premiumMapFeaturesSelected) {
                dismissAllowingStateLoss()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.roadSurfacePremiumNote.text = createPremiumNoteTagSpannable(requireContext())

        initMapTypeItems()

        if (showHeatmaps) {
            initHeatmapTypeItems()
        } else {
            binding.heatmapsGroup.visibility = View.GONE
        }

        if (showRoadSurface) {
            initRoadSurfaceTypes()
        } else {
            binding.roadSurfaceGroup.visibility = View.GONE
        }

        if (showMyTracks) {
            val value = selectedMyTracksGranularityLiveData.value
            initMyTracksItems(value?.rangeStartMillis, value?.rangeEndMillis)
        } else {
            binding.myTracksGroup.visibility = View.GONE
        }

        if (showMyPOIsGroup) {
            binding.poiToggleGroup.visibility = View.VISIBLE
        } else {
            binding.poiToggleGroup.visibility = View.GONE
        }

        if (showPopularRoutes) {
            binding.popularToggleGroup.visibility = View.VISIBLE
        } else {
            binding.popularToggleGroup.visibility = View.GONE
        }

        binding.roadSurfaceInfoButton.setOnClickListener {
            RoadSurfaceInfoDialogFragment.newInstance().show(childFragmentManager, null)
        }
    }

    override fun onStart() {
        super.onStart()

        activity?.window?.decorView?.rootView?.let { view ->
            if (Build.VERSION.SDK_INT >= 28) {
                // Don't use ViewCompat implementation for SDK version >= 28. It keeps a reference
                // to the listener even after removing the listener, causing a memory leak.
                view.addOnUnhandledKeyEventListener(onUnhandledKeyEventListener)
            } else {
                ViewCompat.addOnUnhandledKeyEventListener(view, onUnhandledKeyEventListenerCompat)
            }
        }
    }

    override fun onStop() {
        activity?.window?.decorView?.rootView?.let { view ->
            if (Build.VERSION.SDK_INT >= 28) {
                view.removeOnUnhandledKeyEventListener(onUnhandledKeyEventListener)
            } else {
                ViewCompat.removeOnUnhandledKeyEventListener(view, onUnhandledKeyEventListenerCompat)
            }
        }

        super.onStop()
    }

    override fun onDestroyView() {
        // clearing groupie adapters to avoid memory leaks
        binding.heatmapList.adapter = null
        binding.unbind()
        _binding = null
        if (userChangedSomething) {
            inAppReviewTrigger.incNumberOfVisitsForSource(InAppReviewSource.MAP)
            inAppReviewTrigger.scheduleInAppReviewIfPossible()
        }

        super.onDestroyView()
    }

    private fun handleUnhandledKeyEvent(event: KeyEvent): Boolean {
        // Handle back press manually because the dialog has been set as not focusable.
        return if (event.keyCode == KeyEvent.KEYCODE_BACK) {
            dialog?.cancel()
            true
        } else {
            false
        }
    }

    private fun initMapTypeItems() {
        binding.mapTypeList.adapter = GroupAdapter<GroupieViewHolder>().apply {
            add(mapTypesSection)

            setOnItemClickListener { item, _ ->
                if (item is MapSelectionItem) {
                        viewModel.setSelectedMapType(item.mapType)
                        exploreAnalytics.trackMapChangeMode(analyticsSource, MAP_MODE) // TODO - analytics update for premium?
                        userChangedSomething = true
                    }
                }
            }

        viewModel.mapTypes
            .onEach { mapTypes ->
                mapTypes.map { mapType ->
                    MapSelectionItem(
                        mapType = mapType,
                        viewModel = viewModel,
                        lifecycleOwner = viewLifecycleOwner,
                        resources = resources
                    )
                }.let(mapTypesSection::update)

                scrollSelectedMapTypeVisible()
            }
            .launchIn(lifecycleScope)

        viewModel.selectedMapType.observeK(viewLifecycleOwner) {
            scrollSelectedMapTypeVisible()
        }
    }

    private fun initHeatmapTypeItems() {
        viewModel.selectedHeatmapType.observeK(viewLifecycleOwner) {
            scrollSelectedHeatmapVisible()
        }

        binding.heatmapList.adapter = GroupAdapter<GroupieViewHolder>().apply {
            // The first item is for selecting "none"
            add(
                HeatmapSelectionItem(
                    null,
                    viewModel,
                    <EMAIL>,
                    binding.heatmapList.context
                )
            )

            addAll(
                viewModel.heatmapTypes.map {
                    HeatmapSelectionItem(
                        it,
                        viewModel,
                        <EMAIL>,
                        binding.heatmapList.context
                    )
                }
            )

            setOnItemClickListener { item, _ ->
                if (item is HeatmapSelectionItem) {
                    if (item.heatmapType != viewModel.selectedHeatmapType.value) {
                        viewModel.setSelectedHeatmap(item.heatmapType)
                        exploreAnalytics.trackMapChangeMode(analyticsSource, HEATMAP)
                    } else {
                        // Selecting an already selected heatmap clears the selection
                        viewModel.setSelectedHeatmap(null)
                        exploreAnalytics.trackMapChangeMode(analyticsSource, HEATMAP)
                    }
                    userChangedSomething = true
                }
            }
        }
    }

    private fun initRoadSurfaceTypes() {
        binding.roadSurfaceList.adapter = GroupAdapter<GroupieViewHolder>().apply {
            // The first item is for selecting "none"
            add(
                RoadSurfaceSelectionItem(
                    null,
                    viewModel,
                    <EMAIL>,
                    binding.roadSurfaceList.context
                )
            )

            addAll(
                viewModel.roadSurfaceTypes.map {
                    RoadSurfaceSelectionItem(
                        it,
                        viewModel,
                        <EMAIL>,
                        binding.roadSurfaceList.context
                    )
                }
            )

            setOnItemClickListener { item, _ ->
                if (item is RoadSurfaceSelectionItem) {
                    if (item.roadSurfaceType != null) {
                        viewModel.toggleRoadSurfaceSelected(item.roadSurfaceType)
                    } else {
                        viewModel.clearRoadSurfaceSelection()
                    }
                    exploreAnalytics.trackMapChangeMode(analyticsSource, ROAD_SURFACE)
                    userChangedSomething = true
                }
            }
        }
    }

    private fun initMyTracksItems(
        rangeStartMillis: Long? = null,
        rangeEndMillis: Long? = null
    ) {
        binding.myTracksList.adapter = GroupAdapter<GroupieViewHolder>().apply {
            // The first item is for selecting "none"
            add(
                MyTracksSelectionItem(
                    granularity = null,
                    requiresPremium = false,
                    viewModel = viewModel,
                    lifecycleOwner = <EMAIL>
                )
            )
            customDatesItem = MyTracksSelectionItem(
                granularity = MyTracksGranularity(
                    MyTracksGranularity.Type.CUSTOM_DATES,
                    rangeStartMillis,
                    rangeEndMillis
                ),
                requiresPremium = premiumRequiredMyTracksGranularityTypes.contains(MyTracksGranularity.Type.CUSTOM_DATES),
                viewModel = viewModel,
                lifecycleOwner = <EMAIL>
            )
            add(
                customDatesItem
            )
            add(
                MyTracksSelectionItem(
                    granularity = MyTracksGranularity(MyTracksGranularity.Type.THIS_WEEK),
                    requiresPremium = premiumRequiredMyTracksGranularityTypes.contains(MyTracksGranularity.Type.THIS_WEEK),
                    viewModel = viewModel,
                    lifecycleOwner = <EMAIL>
                )
            )
            add(
                MyTracksSelectionItem(
                    granularity = MyTracksGranularity(MyTracksGranularity.Type.THIS_MONTH),
                    requiresPremium = premiumRequiredMyTracksGranularityTypes.contains(MyTracksGranularity.Type.THIS_MONTH),
                    viewModel = viewModel,
                    lifecycleOwner = <EMAIL>
                )
            )
            add(
                MyTracksSelectionItem(
                    granularity = MyTracksGranularity(MyTracksGranularity.Type.THIS_YEAR),
                    requiresPremium = premiumRequiredMyTracksGranularityTypes.contains(MyTracksGranularity.Type.THIS_YEAR),
                    viewModel = viewModel,
                    lifecycleOwner = <EMAIL>
                )
            )
            add(
                MyTracksSelectionItem(
                    granularity = MyTracksGranularity(MyTracksGranularity.Type.LAST_30_DAYS),
                    requiresPremium = premiumRequiredMyTracksGranularityTypes.contains(MyTracksGranularity.Type.LAST_30_DAYS),
                    viewModel = viewModel,
                    lifecycleOwner = <EMAIL>
                )
            )

            setOnItemClickListener { item, _ ->
                if (item is MyTracksSelectionItem) {
                    if (item.granularity?.type == MyTracksGranularity.Type.CUSTOM_DATES) {
                        val picker = createDatePicker(
                            item.granularity?.rangeStartMillis,
                            item.granularity?.rangeEndMillis
                        )
                        picker.addOnPositiveButtonClickListener(::updateCustomDates)
                        picker.show(childFragmentManager, this.toString())
                    } else {
                        viewModel.setSelectedMyTracksGranularity(item.granularity)
                        exploreAnalytics.trackMapChangeMode(analyticsSource, MY_ROUTES)
                    }
                    userChangedSomething = true
                }
            }
        }
    }

    private fun createDatePicker(
        rangeStartMillis: Long?,
        rangeEndMillis: Long?
    ): MaterialDatePicker<Pair<Long, Long>> {
        val constraintStart = MyTracksGranularity.getMinimumAllowedRangeStartMillis()
        val constraintEnd = MyTracksGranularity.getMaximumAllowedRangeEndMillis()
        val constraintsBuilder = CalendarConstraints.Builder()
            .setStart(constraintStart)
            .setEnd(constraintEnd)
            .setFirstDayOfWeek(viewModel.firstDayOfWeek.toCalendarInt())
        rangeEndMillis?.run {
            constraintsBuilder.setOpenAt(min(rangeEndMillis, constraintEnd))
        }
        val builder = MaterialDatePicker.Builder.dateRangePicker()
            .setTitleText(R.string.date_range_title)
            .setCalendarConstraints(constraintsBuilder.build())
            .setTheme(R.style.CustomCalendarRangePickerTheme)
        if (rangeStartMillis != null && rangeEndMillis != null &&
            rangeStartMillis <= rangeEndMillis
        ) {
            builder.setSelection(
                // Tune the selection to supported window
                Pair(
                    max(rangeStartMillis, constraintStart),
                    min(rangeEndMillis, constraintEnd)
                )
            )
        }
        return builder.build()
    }

    private fun updateCustomDates(range: Pair<Long, Long>) {
        viewModel.setSelectedMyTracksGranularity(
            MyTracksGranularity(
                MyTracksGranularity.Type.CUSTOM_DATES,
                range.first,
                range.second
            )
        )
        exploreAnalytics.trackMapChangeMode(analyticsSource, MY_ROUTES)
    }

    private fun scrollSelectedMapTypeVisible() {
        if (selectedMapTypeMadeVisible) return

        viewModel.getSelectedMapTypeIndex().takeIf { it >= 0 }?.let {
            scrollPositionVisible(it, binding.mapTypeList)
            selectedMapTypeMadeVisible = true
        }
    }

    private fun scrollSelectedHeatmapVisible() {
        if (selectedHeatmapMadeVisible) return

        viewModel.getSelectedHeatmapIndex().takeIf { it >= 0 }?.let {
            scrollPositionVisible(it + 1, binding.heatmapList) // First item is "none"
            selectedHeatmapMadeVisible = true
        }
    }

    private fun scrollPositionVisible(position: Int, view: RecyclerView) {
        view.post {
            (view.layoutManager as? LinearLayoutManager)?.let {
                if (position < it.findFirstCompletelyVisibleItemPosition() ||
                    position > it.findLastCompletelyVisibleItemPosition()
                ) {
                    // Set offset so that the last item is shown partly
                    val offset =
                        view.width - view.paddingLeft - (itemWidth * ITEM_VISIBLE_THRESHOLD_RATIO).toInt() -
                            (itemWidth + extraItemPadding) * 2

                    it.scrollToPositionWithOffset(
                        position,
                        offset
                    )
                }
            }
        }
    }

    private fun makeOptionVisible(option: MapOption, dialog: BottomSheetDialog) {
        val behavior = dialog.behavior
        if (option != MapOption.MAP_STYLE) {
            // Wait until the bottom sheet has settled to expanded state.
            behavior.addBottomSheetCallback(object : BottomSheetBehavior.BottomSheetCallback() {
                override fun onStateChanged(bottomSheet: View, newState: Int) {
                    if (newState == BottomSheetBehavior.STATE_EXPANDED) {
                        val view = when (option) {
                            MapOption.MAP_STYLE -> binding.mapTypeList
                            MapOption.HEATMAP -> binding.heatmapList
                            MapOption.ROAD_SURFACE -> binding.roadSurfaceList
                            MapOption.MY_TRACKS -> binding.myTracksList
                        }
                        scrollViewOnScreen(view)
                        behavior.removeBottomSheetCallback(this)
                    }
                }

                override fun onSlide(bottomSheet: View, slideOffset: Float) {
                    // No action
                }
            })

            behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }
    }

    private fun scrollViewOnScreen(view: View) {
        binding.scrollView.requestChildRectangleOnScreen(
            view,
            Rect(0, 0, view.width, view.height),
            false
        )
    }

    companion object {
        const val FRAGMENT_TAG = "map_selection_dialog_fragment"
        const val KEY_MAPS_PROVIDER_NAME = "maps_provider_name"
        const val KEY_SHOW_HEATMAPS = "show_heatmaps"
        const val KEY_SHOW_ROAD_SURFACE = "show_road_surface"
        const val KEY_SHOW_PREMIUM_MAP_TYPES = "show_premium_map_types"
        const val KEY_SHOW_MY_TRACKS = "show_my_tracks"
        const val KEY_SHOW_MY_POIS_GROUP = "show_my_pois_group"
        const val KEY_MAP_CENTER_LATITUDE = "map_center_latitude"
        const val KEY_MAP_CENTER_LONGITUDE = "map_center_longitude"
        const val KEY_ANALYTICS_SOURCE = "analytics_source"
        const val KEY_FOCUSED_OPTION = "focused_option"
        const val KEY_SHOW_POPULAR_ROUTES = "show_popular_routes"
        private const val ITEM_VISIBLE_THRESHOLD_RATIO = 0.25f

        @JvmStatic
        fun newInstance(
            mapsProviderName: String,
            showHeatmaps: Boolean,
            showRoadSurface: Boolean,
            showMyTracks: Boolean,
            showMyPOIsGroup: Boolean,
            mapCenter: LatLng?,
            analyticsSource: String,
            focusedOption: MapOption = MapOption.MAP_STYLE,
            showPopularRoutes: Boolean = false,
        ) = MapSelectionDialogFragment().apply {
            arguments = bundleOf(
                KEY_MAPS_PROVIDER_NAME to mapsProviderName,
                KEY_SHOW_HEATMAPS to showHeatmaps,
                KEY_SHOW_ROAD_SURFACE to showRoadSurface,
                KEY_SHOW_MY_TRACKS to showMyTracks,
                KEY_SHOW_MY_POIS_GROUP to showMyPOIsGroup,
                KEY_MAP_CENTER_LATITUDE to mapCenter?.latitude,
                KEY_MAP_CENTER_LONGITUDE to mapCenter?.longitude,
                KEY_ANALYTICS_SOURCE to analyticsSource,
                KEY_FOCUSED_OPTION to focusedOption.ordinal,
                KEY_SHOW_POPULAR_ROUTES to showPopularRoutes,
            )
        }
    }
}
