package com.stt.android.ui.fragments.workout

import android.content.Context
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import com.github.mikephil.charting.components.LimitLine
import com.github.mikephil.charting.components.XAxis
import com.stt.android.R
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.domain.workout.WorkoutHrEvent
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.ui.components.charts.HighlightDraggableTouchListener
import com.stt.android.utils.FontUtils
import io.reactivex.Completable
import timber.log.Timber
import com.stt.android.core.R as CR

class WorkoutLineChartEnlarge
@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0
) : WorkoutLineChartBase(context, attrs, defStyle) {

    init {
        setTouchEnabled(true)
        setOnTouchListener(HighlightDraggableTouchListener(this))
        lineColor = ContextCompat.getColor(context, CR.color.black)
        fillColorStart = ContextCompat.getColor(context, R.color.analysis_chart_trend_fill_color_start)
        fillColorEnd = ContextCompat.getColor(context, R.color.analysis_chart_trend_fill_color_end)
        isScaleXEnabled = true
        isScaleYEnabled = true
        lineWidth = 1.5f
        chartTypeface = FontUtils.getChartLabelsTypeface(context)

        with(xAxis) {
            position = XAxis.XAxisPosition.BOTTOM
            setDrawGridLines(false)
            setDrawLabels(true)
            setAvoidFirstLastClipping(true)
            textColor = ContextCompat.getColor(context, CR.color.black)
            typeface = chartTypeface
            textSize = 14f
            axisLineWidth = 1.5f
            axisMinimum = 0.0f
            valueFormatter = xAxisDurationFormatter
        }

        with(axisRight) {
            isEnabled = true
            setDrawAxisLine(false)
            setDrawZeroLine(false)
            setDrawGridLines(true)
            gridLineWidth = 1f
            this.gridColor = gridColor
            setDrawLabels(true)
            setDrawTopYLabelEntry(true)
            setLabelCount(5, false)
            textColor = ContextCompat.getColor(context, CR.color.black)
            textSize = 14f
            typeface = chartTypeface
            gridColor = ContextCompat.getColor(context, CR.color.very_light_gray)
        }
    }

    override fun drawDiveGraph(
        graphType: GraphType,
        diveExtension: DiveExtension?,
        sml: Sml,
        measurementUnit: MeasurementUnit,
        drawEvents: Boolean
    ): Completable =
        super.drawDiveGraph(graphType, diveExtension, sml, measurementUnit, drawEvents)
            .doOnComplete {
                // avoid icon overlapping axis line
                axisRight.axisMaximum /= 0.95f
                onChartDataReady()
            }

    override fun drawGraph(
        isSuuntoRun: Boolean,
        graphType: GraphType,
        geoPoints: List<WorkoutGeoPoint>,
        workoutHeader: WorkoutHeader,
        sml: Sml?,
        measurementUnit: MeasurementUnit
    ): Completable =
        super.drawGraph(isSuuntoRun, graphType, geoPoints, workoutHeader, sml, measurementUnit)
            .doOnComplete(::onChartDataReady)

    override fun drawHeartRateGraph(
        hrEvents: List<WorkoutHrEvent>,
        maxHeartRateValues: Int,
        multisportPartActivity: MultisportPartActivity?
    ): Completable =
        super.drawHeartRateGraph(hrEvents, maxHeartRateValues, multisportPartActivity)
            .doOnComplete(::onChartDataReady)

    override fun addAvgLine(avgValue: Float) {
        val avg = LimitLine(avgValue)
        avg.lineWidth = 0.75f
        val color = ContextCompat.getColor(context, CR.color.black)
        avg.textColor = ContextCompat.getColor(context, CR.color.near_black)
        avg.lineColor = color
        avg.enableDashedLine(5.0f, 10.0f, 0.0f)
        avg.label = context.getString(R.string.avg)
        avg.textSize = 9f
        avg.labelPosition = LimitLine.LimitLabelPosition.LEFT_TOP
        axisRight.addLimitLine(avg)
    }

    private fun onChartDataReady() {
        Timber.w("on chart ready")
    }
}
