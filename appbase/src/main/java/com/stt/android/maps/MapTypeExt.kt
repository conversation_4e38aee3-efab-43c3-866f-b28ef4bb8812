package com.stt.android.maps

import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import java.util.Locale

val MapType.isAvalancheMap: Boolean get() = name == MAP_TYPE_AVALANCHE

val MapType.isSkiMap: Boolean get() = name == MAP_TYPE_SKI

val MapType.isSkiOrAvalancheMap: Boolean get() = name == MAP_TYPE_SKI || name == MAP_TYPE_AVALANCHE

/**
 * This is to check if the given [MapType] is shown to the user for selection. However, users still
 * might not be able to select e.g. if Premium subscription is required.
 */
fun MapType.isAvailable(
    currentUserController: CurrentUserController,
    userSettingsController: UserSettingsController,
): Boolean {
    // For maps that require FT roles, only show them if current user is FT.
    if (requiresFieldTester && !currentUserController.isFieldTester) {
        return false
    }

    val currentCountry = userSettingsController.settings.country
        ?.takeUnless(String::isEmpty)
        ?.lowercase(Locale.ROOT)
        ?: return true

    return availableCountries
        ?.takeUnless(List<*>::isEmpty)
        ?.any { country -> country.lowercase(Locale.ROOT) == currentCountry }
        ?: true
}
