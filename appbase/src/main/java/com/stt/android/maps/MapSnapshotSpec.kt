package com.stt.android.maps

import androidx.annotation.ColorRes
import androidx.annotation.Px
import com.google.android.gms.maps.model.LatLng
import com.stt.android.colorfultrack.WorkoutColorfulTrackMapData
import com.stt.android.core.domain.GraphType
import com.stt.android.domain.routes.TopRoute
import com.stt.android.home.dashboardv2.widgets.DashboardMapSnapshotData

/**
 * A map snapshot specification defining the size for a map snapshot and enough information to
 * fetch the relevant data from the database for snapshot generation.
 *
 * These should be fairly lightweight objects since there can be hundreds of instances in memory
 * at a time.
 *
 * [equals] and [hashCode] have to be properly implemented so data classes are used here.
 */
sealed class MapSnapshotSpec(
    @get:Px open val width: Int,
    @get:Px open val height: Int,
    open val explicitProviderName: String?,
    open val explicitMapType: MapType?,
    open val requiresLogoOverlay: Boolean = true,
) {
    abstract fun cacheKey(): String

    /**
     * spec for map privacy settings preview
     */
    data class Sample @JvmOverloads constructor(
        override val width: Int,
        override val height: Int,
        val explicitRoutePadding: Int? = null,
        val shouldHideMap: Boolean = false,
        val hiddenDistanceStartEnd: Int = 0,
    ) : MapSnapshotSpec(width, height, null, null) {
        override fun cacheKey() = buildString {
            val provider = SuuntoMaps.defaultProvider!!.name

            append("sample-$width-$height-$provider")

            if (explicitRoutePadding != null) {
                append("-padding$explicitRoutePadding")
            }
            if (shouldHideMap) {
                append("-map-hidden")
            }
            if (hiddenDistanceStartEnd > 0) {
                append("-start$hiddenDistanceStartEnd-end$hiddenDistanceStartEnd")
            }
        }
    }

    /**
     * Spec for a map snapshot for a workout that will be read from the database. Workouts are
     * assumed to never change so [workoutId] is enough to be uniquely used as a cache key.
     */
    data class Workout(
        val workoutId: Int,
        override val width: Int,
        override val height: Int,
        override val explicitProviderName: String? = null,
        override val explicitMapType: MapType? = null,
        val explicitRoutePadding: Int? = null,
    ) : MapSnapshotSpec(width, height, explicitProviderName, explicitMapType) {
        override fun cacheKey() = buildString {
            val provider = explicitProviderName ?: SuuntoMaps.defaultProvider!!.name

            append("workout-$workoutId-$width-$height-$provider")

            if (explicitMapType != null) {
                append("-${explicitMapType.name}")
            }

            if (explicitRoutePadding != null) {
                append("-padding$explicitRoutePadding")
            }
        }
    }

    /**
     * Spec for a map snapshot for a route. Routes can be edited so both [routeId] and
     * [segmentsModifiedDateMillis] are used as the cache key to make sure new snapshots are
     * generated when route segments are modified.
     */
    data class Route(
        val routeId: String,
        val segmentsModifiedDateMillis: Long,
        val showTurnByTurnWaypoints: Boolean,
        override val width: Int,
        override val height: Int,
        override val explicitProviderName: String? = null,
        override val explicitMapType: MapType? = null,
    ) : MapSnapshotSpec(width, height, explicitProviderName, explicitMapType) {
        override fun cacheKey() = buildString {
            val provider = explicitProviderName ?: SuuntoMaps.defaultProvider!!.name

            append("workout-$routeId-$segmentsModifiedDateMillis-$width-$height-$provider-$showTurnByTurnWaypoints")

            if (explicitMapType != null) {
                append("-${explicitMapType.name}")
            }
        }
    }

    /**
     * Spec for a map snapshot for a workout where we have the polyline available. This may be
     * something already read from the database or something received from a remote API (such
     * as publicly shared workouts).
     */
    data class WorkoutPolyline(
        val polyline: String?,
        override val width: Int,
        override val height: Int,
        override val explicitProviderName: String? = null,
        override val explicitMapType: MapType? = null,
        val disableZoomToBounds: Boolean = false,
    ) : MapSnapshotSpec(width, height, explicitProviderName, explicitMapType) {
        val polylineHash = (polyline ?: "").hashCode()

        override fun cacheKey() = buildString {
            val provider = explicitProviderName ?: SuuntoMaps.defaultProvider!!.name

            append("polyline-$polylineHash-$width-$height-$provider")

            if (explicitMapType != null) {
                append("-${explicitMapType.name}")
            }
        }
    }

    /**
     * Spec for a map snapshot drawing a list of polylines.
     *
     * An explicit id is needed for caching.
     */
    data class Polylines(
        val id: String,
        val coordinates: List<List<LatLng>>,
        @ColorRes val colorRes: Int,
        val lineWidthPx: Float,
        override val width: Int,
        override val height: Int,
        override val explicitProviderName: String? = null,
        override val explicitMapType: MapType? = null
    ) : MapSnapshotSpec(width, height, explicitProviderName, explicitMapType) {
        override fun cacheKey() = buildString {
            val provider = explicitProviderName ?: SuuntoMaps.defaultProvider!!.name

            append("polylines-$id-$lineWidthPx-$colorRes-${coordinates.size}-$width-$height-$provider")

            if (explicitMapType != null) {
                append("-${explicitMapType.name}")
            }
        }
    }

    data class ColorfulPolylines(
        val colorfulTrackMapData: WorkoutColorfulTrackMapData,
        val graphType: GraphType,
        val workoutId: Int,
        override val width: Int,
        override val height: Int,
        override val explicitProviderName: String? = null,
        override val explicitMapType: MapType? = null,
        val explicitRoutePadding: Int? = null,
    ) : MapSnapshotSpec(width, height, explicitProviderName, explicitMapType) {
        override fun cacheKey() = buildString {
            val provider = explicitProviderName ?: SuuntoMaps.defaultProvider!!.name

            append("colorfulPolylines-v2-${workoutId}_${graphType.key}-$width-$height-$provider")

            if (explicitMapType != null) {
                append("-${explicitMapType.name}")
            }
            if (explicitRoutePadding != null) {
                append("-padding$explicitRoutePadding")
            }
        }
    }

    data class DashboardMapWidget(
        val snapshotInfo: DashboardMapSnapshotData,
        override val width: Int,
        override val height: Int,
        override val explicitProviderName: String? = null,
        override val explicitMapType: MapType? = null
    ) : MapSnapshotSpec(width, height, explicitProviderName, explicitMapType) {
        override fun cacheKey() = buildString {
            val provider = explicitProviderName ?: SuuntoMaps.defaultProvider!!.name

            val uniqueKey = snapshotInfo.uniqueKey
            append("dashboardMapWidget-$${uniqueKey}-$width-$height-$provider")

            if (explicitMapType != null) {
                append("-${explicitMapType.name}")
            }
        }
    }

    data class PopularRoute(
        val route: TopRoute,
        val segmentsModifiedDateMillis: Long,
        val showTurnByTurnWaypoints: Boolean,
        override val width: Int,
        override val height: Int,
        override val explicitProviderName: String? = null,
        override val explicitMapType: MapType? = null,
        override val requiresLogoOverlay: Boolean = false
    ) : MapSnapshotSpec(width, height, explicitProviderName, explicitMapType, requiresLogoOverlay) {
        override fun cacheKey() = buildString {
            val provider = explicitProviderName ?: SuuntoMaps.defaultProvider!!.name

            append("popularRoute-${route.topRouteId}-${route.activityId}-$segmentsModifiedDateMillis-$width-$height-$provider-$showTurnByTurnWaypoints")

            if (explicitMapType != null) {
                append("-${explicitMapType.name}")
            }
        }
    }
}
