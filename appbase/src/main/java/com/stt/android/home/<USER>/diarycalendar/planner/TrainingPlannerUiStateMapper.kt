package com.stt.android.home.diary.diarycalendar.planner

import com.soy.algorithms.impact.WorkoutImpact
import com.stt.android.R
import com.stt.android.core.domain.workouts.CoreActivityGroup
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.data.TimeUtils
import com.stt.android.domain.user.METERS_TO_KILOMETERS
import com.stt.android.domain.user.METERS_TO_MILES
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.diary.diarycalendar.planner.composables.WeeklyEntries
import com.stt.android.home.diary.diarycalendar.planner.composables.WorkoutEntry
import com.stt.android.home.diary.diarycalendar.planner.domain.models.PlannedWorkout
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerCatalogue
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerProgramCategory
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerProgramDetails
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerProgramDetailsEventInfo
import com.stt.android.home.diary.diarycalendar.planner.domain.models.TrainingPlannerProgramPlan
import com.stt.android.home.diary.diarycalendar.planner.domain.models.WeeklyProgram
import com.stt.android.home.diary.diarycalendar.planner.domain.models.nameRes
import com.stt.android.home.diary.diarycalendar.planner.domain.toLocalDate
import com.stt.android.home.diary.diarycalendar.planner.models.CatalogueUiState
import com.stt.android.home.diary.diarycalendar.planner.models.CategoryUiState
import com.stt.android.home.diary.diarycalendar.planner.models.EventInfoUiState
import com.stt.android.home.diary.diarycalendar.planner.models.Legend
import com.stt.android.home.diary.diarycalendar.planner.models.PlannedWorkoutUiState
import com.stt.android.home.diary.diarycalendar.planner.models.ProgramDetailsUiState
import com.stt.android.home.diary.diarycalendar.planner.models.ProgramUiState
import com.stt.android.home.diary.diarycalendar.planner.models.Question
import com.stt.android.home.diary.diarycalendar.planner.models.WeekTargetUiState
import com.stt.android.home.diary.diarycalendar.planner.models.WeekTargetsUiState
import com.stt.android.home.diary.diarycalendar.planner.models.WeekUiState
import com.stt.android.home.diary.diarycalendar.planner.models.WeeklyChartType
import com.stt.android.home.diary.diarycalendar.planner.models.WeeklyLegend
import com.stt.android.home.diary.diarycalendar.planner.models.WorkoutOverViewUiState
import com.stt.android.home.diary.diarycalendar.planner.models.WorkoutSessionUiState
import com.stt.android.home.diary.diarycalendar.planner.models.WorkoutTargetsUiState
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.takeIfNotEmpty
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableSet
import kotlinx.collections.immutable.toPersistentList
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import javax.inject.Inject
import kotlin.math.roundToInt
import com.stt.android.core.R as CoreR

class TrainingPlannerUiStateMapper @Inject constructor(
    private val infoModelFormatter: InfoModelFormatter,
) {
    private val trainingDayFormatter = DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT)
    private val trainingDayHeaderFormatter = DateTimeFormatter.ofPattern("EEEE ")
    private val isMetric = infoModelFormatter.unit == MeasurementUnit.METRIC

    fun toWeekUiState(
        weeklyProgram: WeeklyProgram,
        weeklyPrograms: List<WeeklyProgram>,
        planStartDate: LocalDate,
        weekStartsOnSunday: Boolean,
    ): WeekUiState {
        val weekNumber = weeklyProgram.weekNumber
        val weekRange = calculateWeekRange(planStartDate, weekNumber, weekStartsOnSunday)
        return WeekUiState(
            weekNumber = weekNumber,
            isNextEnabled = weeklyPrograms.any { it.weekNumber > weekNumber },
            isPreviousEnabled = weeklyPrograms.any { it.weekNumber < weekNumber },
            weekRange = weekRange,
        )
    }

    private fun calculateWeekRange(
        planStartDate: LocalDate,
        weekNumber: Int,
        weekStartsOnSunday: Boolean
    ): ClosedRange<LocalDate> {
        // Calculate the start date of the given week number
        val weekStartShift = if (weekStartsOnSunday) DayOfWeek.SUNDAY else DayOfWeek.MONDAY
        val weekStartDate = planStartDate
            .plusWeeks((weekNumber - 1).toLong())
            .with(TemporalAdjusters.previousOrSame(weekStartShift))

        // Calculate the end date of the given week (6 days after start)
        val weekEndDate = weekStartDate.plusDays(6)

        return weekStartDate..weekEndDate
    }

    fun toWeekTargetsUiState(
        weeklyProgram: WeeklyProgram,
        completedWorkouts: List<WorkoutHeader>,
        workoutImpacts: Map<WorkoutImpact, Int>,
        weekRange: ClosedRange<LocalDate>,
    ): WeekTargetsUiState {
        val tssTarget = weeklyProgram.weeklyTargets.trainingLoad.toDouble()
        val durationTarget = weeklyProgram.weeklyTargets.duration.toDouble()
        val runningTarget = weeklyProgram.weeklyTargets.distanceInMeters
            ?.takeIf { it > 0 }
            ?.toDouble()

        val tssTotal = completedWorkouts.sumOf { it.tss?.trainingStressScore?.toDouble() ?: 0.0 }
        val durationTotal = completedWorkouts.sumOf { it.totalTime }
        val runningTotal = completedWorkouts
            .filter {
                CoreActivityGroup.RUN.activityTypes.contains(
                    CoreActivityType.valueOf(it.activityTypeId)
                )
            }
            .sumOf { it.totalDistance }

        return WeekTargetsUiState(
            tss = WeekTargetUiState(
                value = tssTotal,
                target = tssTarget,
                formattedValue = tssTotal.roundToInt().toString(),
                formattedTarget = tssTarget.roundToInt().toString(),
                unit = null,
            ),
            duration = WeekTargetUiState(
                value = durationTotal,
                target = durationTarget,
                formattedValue = infoModelFormatter.formatAccumulatedTotalDuration(durationTotal),
                formattedTarget = infoModelFormatter.formatAccumulatedTotalDuration(durationTarget),
                unit = null,
            ),
            distance = runningTarget?.let { target ->
                WeekTargetUiState(
                    value = runningTotal,
                    target = target,
                    formattedValue = infoModelFormatter.unit.toDistanceUnit(runningTotal)
                        .roundToInt()
                        .toString(),
                    formattedTarget = infoModelFormatter.unit.toDistanceUnit(target).roundToInt()
                        .toString(),
                    unit = infoModelFormatter.unit.distanceUnit,
                )
            },
            goal = weeklyProgram.goal,
            note = weeklyProgram.note,
            compliance = calculateCompliance(
                weeklyProgram = weeklyProgram,
                completedWorkouts = completedWorkouts,
                workoutImpacts = workoutImpacts,
                weekRange = weekRange,
            )
        )
    }

    private fun calculateCompliance(
        weeklyProgram: WeeklyProgram,
        completedWorkouts: List<WorkoutHeader>,
        workoutImpacts: Map<WorkoutImpact, Int>,
        weekRange: ClosedRange<LocalDate>
    ): Double? {
        val now = LocalDate.now()
        if (now < weekRange.start) return null // No compliance calculated for upcoming weeks

        val isCurrentWeek = weekRange.contains(now)
        val plannedWorkouts = weeklyProgram.plannedWorkouts.filter {
            !isCurrentWeek || !it.trainingDate.isAfter(now)
        }
        return ComplianceCalculator.calculate(
            planned = plannedWorkouts,
            done = completedWorkouts,
            workoutImpacts = workoutImpacts
        )
    }

    fun toWorkoutOverviewUiState(
        weekStart: LocalDate,
        weekEndInclusive: LocalDate,
        weeklyProgram: WeeklyProgram,
        completedWorkouts: List<WorkoutHeader>,
        workoutImpacts: Map<WorkoutImpact, Int>,
    ): WorkoutOverViewUiState {
        val workoutsGroups: Map<LocalDate?, List<PlannedWorkout>> = weeklyProgram
            .plannedWorkouts
            .groupBy { it.trainingDate }

        val dateMap = generateSequence(weekStart) { it.plusDays(1) }
            .takeWhile { !it.isAfter(weekEndInclusive) }
            .map { date -> date to (workoutsGroups[date] ?: emptyList()) }
            .toMap()

        val completedWorkoutsMap = completedWorkouts.groupBy {
            TimeUtils.epochToLocalZonedDateTime(it.startTime).toLocalDate()
        }

        val charts = WeeklyChartType.entries.associateWith { chartType ->
            WeeklyEntries(
                range = weekStart..weekEndInclusive,
                plannedWorkouts = getPlannedEntries(
                    weeklyProgram.plannedWorkouts,
                    isMetric,
                    chartType
                ),
                doneWorkouts = getCompletedEntries(completedWorkouts, isMetric, chartType)
            )
        }

        val legends = WeeklyChartType.entries.associateWith { chartType ->
            val plannedLegends = getPlannedLegends(weeklyProgram.plannedWorkouts, chartType)
            val completedLegends = getCompletedLegends(completedWorkouts, chartType)
            val weeklyLegends = (plannedLegends.keys + completedLegends.keys).associateWith { key ->
                WeeklyLegend(
                    plannedLegend = plannedLegends[key],
                    completedLegend = completedLegends[key] ?: Legend(
                        text = infoModelFormatter.context.getString(key.nameRes) + " -",
                        color = infoModelFormatter.context.getColor(key.color),
                    ),
                )
            }
            // Show legends for not planned activities last
            val (planned, notPlanned) = weeklyLegends.values.partition { it.plannedLegend != null }
            planned + notPlanned
        }

        return WorkoutOverViewUiState(
            workoutSessionUiStates = dateMap.map { (trainingDate, plannedWorkouts) ->
                val formattedTrainingDate = trainingDate.run {
                    format(trainingDayHeaderFormatter) +
                        TimeUtils.dateWithoutYearFormatter().format(this)
                }
                val formattedWorkoutDate = trainingDate.format(trainingDayFormatter)

                WorkoutSessionUiState(
                    id = UUID.randomUUID().toString(),
                    date = trainingDate,
                    dateString = formattedTrainingDate,
                    plannedWorkouts = plannedWorkouts.toPersistentList(),
                    plannedWorkoutUiStates = plannedWorkouts.map {
                        toWorkoutUiState(
                            date = formattedWorkoutDate,
                            workout = it
                        )
                    }.toPersistentList(),
                    completedWorkouts = completedWorkoutsMap[trainingDate]?.toPersistentList()
                        ?: persistentListOf(),
                    plannedWorkoutsTotalDuration = run {
                        val totalDuration =
                            plannedWorkouts.sumOf { it.durationInSeconds.toDouble() }
                        if (totalDuration > 0.0) {
                            infoModelFormatter.context.getString(
                                R.string.workout_planner_planned_value,
                                infoModelFormatter.formatAccumulatedTotalDuration(totalDuration)
                            )
                        } else {
                            infoModelFormatter.context.getString(R.string.workout_planner_no_planned_workouts)
                        }
                    },
                    completedWorkoutsTotalDuration = infoModelFormatter.formatValueAsString(
                        SummaryItem.DURATION,
                        completedWorkoutsMap[trainingDate]?.sumOf { it.totalTime } ?: 0.0
                    ),
                )
            }.toPersistentList(),
            workoutImpacts = workoutImpacts,
            charts = charts,
            legends = legends,
        )
    }

    private fun getPlannedEntries(
        plannedWorkouts: List<PlannedWorkout>,
        isMetric: Boolean,
        chartType: WeeklyChartType
    ): List<WorkoutEntry> = plannedWorkouts.map { workout ->
        WorkoutEntry(
            date = workout.trainingDate,
            value = when (chartType) {
                WeeklyChartType.TSS -> workout.trainingStressScore.toFloat()
                WeeklyChartType.DURATION -> workout.durationInSeconds.toFloat() / 3600f
                WeeklyChartType.RUNNING_KM -> if (isMetric) {
                    (workout.estimatedDistanceInMeters * METERS_TO_KILOMETERS).toFloat()
                } else {
                    (workout.estimatedDistanceInMeters * METERS_TO_MILES).toFloat()
                }
            },
            activityType = workout.activityType,
        )
    }

    private fun getPlannedLegends(
        plannedWorkouts: List<PlannedWorkout>,
        chartType: WeeklyChartType
    ): Map<CoreActivityType, Legend> = plannedWorkouts.groupBy { it.activityType }
        .mapValues { (activityType, list) ->
            val sum = list.sumOf { workout ->
                when (chartType) {
                    WeeklyChartType.TSS -> workout.trainingStressScore.toDouble()
                    WeeklyChartType.DURATION -> workout.durationInSeconds.toDouble()
                    WeeklyChartType.RUNNING_KM -> workout.estimatedDistanceInMeters.toDouble()
                }
            }
            val formattedSum = formatTotal(chartType, sum)
            Legend(
                text = infoModelFormatter.context.getString(
                    R.string.workout_planner_planned_value,
                    formattedSum
                ),
                color = infoModelFormatter.context.getColor(activityType.color),
            )
        }

    private fun getCompletedEntries(
        completedWorkouts: List<WorkoutHeader>,
        isMetric: Boolean,
        chartType: WeeklyChartType
    ): List<WorkoutEntry> {
        return completedWorkouts.map { workoutHeader ->
            WorkoutEntry(
                date = TimeUtils.epochToLocalZonedDateTime(workoutHeader.startTime)
                    .toLocalDate(),
                value = when (chartType) {
                    WeeklyChartType.TSS -> workoutHeader.tss?.trainingStressScore ?: 0f
                    WeeklyChartType.DURATION -> workoutHeader.totalTime.toFloat() / 3600f
                    WeeklyChartType.RUNNING_KM -> if (isMetric) {
                        (workoutHeader.totalDistance * METERS_TO_KILOMETERS).toFloat()
                    } else {
                        (workoutHeader.totalDistance * METERS_TO_MILES).toFloat()
                    }
                },
                activityType = CoreActivityType.valueOf(workoutHeader.activityTypeId),
            )
        }
    }

    private fun getCompletedLegends(
        completedWorkouts: List<WorkoutHeader>,
        chartType: WeeklyChartType
    ): Map<CoreActivityType, Legend> {

        return completedWorkouts.groupBy { CoreActivityType.valueOf(it.activityTypeId) }
            .mapValues { (activityType, list) ->
                val sum = list.sumOf { workout ->
                    when (chartType) {
                        WeeklyChartType.TSS -> workout.tss?.trainingStressScore?.toDouble() ?: 0.0
                        WeeklyChartType.DURATION -> workout.totalTime.toDouble()
                        WeeklyChartType.RUNNING_KM -> workout.totalDistance
                    }
                }
                val formattedSum = formatTotal(chartType, sum)
                Legend(
                    text = "${infoModelFormatter.context.getString(activityType.nameRes)} $formattedSum",
                    color = infoModelFormatter.context.getColor(activityType.color),
                )
            }
    }

    private fun formatTotal(
        chartType: WeeklyChartType,
        total: Double
    ): String? {
        val formattedSum = when (chartType) {
            WeeklyChartType.TSS -> infoModelFormatter.formatValue(
                SummaryItem.TRAININGSTRESSSCORE,
                total,
            ).value

            WeeklyChartType.DURATION -> infoModelFormatter.formatValueAsString(
                SummaryItem.DURATION,
                total,
            )

            WeeklyChartType.RUNNING_KM -> {
                val workoutValue = infoModelFormatter.formatDistance(total)
                    .getOrNull() ?: return ""

                workoutValue.unitResId?.let { unitResId ->
                    "${workoutValue.value} ${infoModelFormatter.context.getString(unitResId)}"
                } ?: ""
            }
        }
        return formattedSum
    }

    fun toCatalogueUiState(trainingPlannerCatalogue: TrainingPlannerCatalogue): CatalogueUiState {
        return CatalogueUiState(
            categories = trainingPlannerCatalogue.categories
                .filter { it.plans.isNotEmpty() } // don't show empty categories
                .map { category: TrainingPlannerProgramCategory ->
                    CategoryUiState(
                        id = category.id,
                        title = category.name,
                        iconUrl = category.iconUrl,
                        programs = category.plans.map(::toProgramUiState).toPersistentList()
                    )
                }.toPersistentList()
        )
    }

    fun toProgramUiState(plan: TrainingPlannerProgramPlan): ProgramUiState = ProgramUiState(
        id = plan.id,
        title = plan.name,
        numberOfWeeks = plan.durationWeeks,
        imageUrl = plan.thumbnailUrl.orEmpty(),
        sports = plan.sports.toImmutableSet(),
    )

    private fun toWorkoutUiState(date: String, workout: PlannedWorkout): PlannedWorkoutUiState {
        val formattedDistance = workout.estimatedDistanceInMeters.takeIf { it > 0.0 }?.let {
            infoModelFormatter.formatDistance(it.toDouble()).getOrNull()
        }
        val formattedTss = infoModelFormatter.formatValue(
            SummaryItem.TRAININGSTRESSSCORE,
            workout.trainingStressScore
        )

        val formattedHrZone = workout.targetHeartRate?.let {
            formatRange(
                min = it.min?.roundToInt().toString(),
                max = it.max?.roundToInt().toString(),
                intensityZone = workout.intensityZone
            )
        }

        val formattedPowerZone = workout.targetPower?.let {
            formatRange(
                min = it.min?.roundToInt().toString(),
                max = it.max?.roundToInt().toString(),
                intensityZone = workout.intensityZone
            )
        }

        val formattedPaceZone = workout.targetPace?.let {
            formatRange(
                min = it.min?.run {
                    infoModelFormatter.formatValue(
                        SummaryItem.AVGPACE,
                        1000 / (this * 60) // backend returns min/km
                    ).value
                },
                max = it.max?.run {
                    infoModelFormatter.formatValue(
                        SummaryItem.AVGPACE,
                        1000 / (this * 60) // backend returns min/km
                    ).value
                },
                intensityZone = workout.intensityZone,
                isInverted = true,
            )
        }

        val avgPaceTarget = workout.avgSpeed?.takeIf { it > 0.0 }?.run {
            infoModelFormatter.formatValue(
                SummaryItem.AVGPACE,
                this
            )
        }

        return PlannedWorkoutUiState(
            id = workout.id,
            date = date,
            activityNameRes = workout.activityType.nameRes,
            activityTypeId = workout.activityType.id,
            note = workout.notes,
            tss = workout.trainingStressScore.toString(),
            workoutTargetsUiState = WorkoutTargetsUiState(
                duration = infoModelFormatter.formatValueAsString(
                    SummaryItem.DURATION,
                    workout.durationInSeconds.toDouble()
                ),
                distance = formattedDistance?.value,
                distanceUnit = formattedDistance?.unitResId,
                avgPace = avgPaceTarget?.value,
                avgPaceUnit = avgPaceTarget?.unit,
                targetHrZone = formattedHrZone,
                targetHrZoneUnit = CoreR.string.bpm,
                targetPaceZone = formattedPaceZone,
                targetPaceZoneUnit = infoModelFormatter.unit.paceUnit,
                trainingLoad = formattedTss.value ?: "",
                trainingLoadUnit = formattedTss.unit ?: R.string.workout_values_headline_tss,
                intensityZone = workout.intensityZone,
                impacts = workout.impacts,
                targetPowerZone = formattedPowerZone,
                targetPowerZoneUnit = CoreR.string.watt
            )
        )
    }

    private fun formatRange(
        min: String?,
        max: String?,
        intensityZone: Int,
        isInverted: Boolean = false
    ): String? {
        return when (intensityZone) {
            1 -> max?.let { "${if (isInverted) ">" else "<"}$it" }
            5 -> min?.let { "${if (isInverted) "<" else ">"}$it" }
            else -> if (min != null && max != null) {
                if (isInverted) {
                    "$max-$min"
                } else {
                    "$min-$max"
                }
            } else null
        }
    }

    fun toProgramDetailsUiState(
        details: TrainingPlannerProgramDetails,
        enablePersonalize: Boolean,
        sportsInThePlan: List<CoreActivityType> = emptyList(),
    ): ProgramDetailsUiState {
        return ProgramDetailsUiState(
            id = details.id,
            version = details.version,
            title = details.name,
            numberOfWeeks = details.durationWeeks,
            imageUrl = details.bannerUrl,
            sports = sportsInThePlan.takeIfNotEmpty()?.toImmutableSet()
                ?: details.sports.toImmutableSet(),
            focus = details.focus,
            level = details.level?.run {
                infoModelFormatter.context.getString(nameRes)
            },
            description = details.description,
            richInfo = details.richInfo,
            eventInfoUiState = details.eventInfo?.run {
                toEventInfoUiState(this)
            },
            enablePersonalize = enablePersonalize,
            isSportsCustomizable = details.questionnaire.questions.any { it is Question.Sports },
            questions = details.questionnaire.questions,
        )
    }

    fun toEventInfoUiState(
        eventInfo: TrainingPlannerProgramDetailsEventInfo,
    ): EventInfoUiState {
        val ascentFormatResult = eventInfo.ascent?.run {
            infoModelFormatter.formatValue(
                SummaryItem.ASCENTALTITUDE,
                this
            )
        }

        return EventInfoUiState(
            name = eventInfo.name,
            date = eventInfo.date.toLocalDate(),
            richInfo = eventInfo.richInfo,
            ascentValue = ascentFormatResult?.value,
            ascentUnit = ascentFormatResult?.unit,
            distance = eventInfo.distance?.run {
                TextFormatter.formatDistance(
                    infoModelFormatter.unit.toDistanceUnit(this.toDouble())
                )
            },
            distanceUnit = infoModelFormatter.unit.distanceUnit,
            terrain = eventInfo.terrain,
            weather = eventInfo.weather,
        )
    }
}
