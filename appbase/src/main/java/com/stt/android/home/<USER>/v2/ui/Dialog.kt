package com.stt.android.home.settings.v2.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing

@Composable
fun InputTextDialog(
    title: String,
    value: String,
    onDismiss: () -> Unit,
    onSave: (String) -> Unit,
    modifier: Modifier = Modifier,
    range: IntRange = 2..30,
    placeholder: String? = null,
    regexString: String? = null,
) {
    var inputTextFieldValue by remember {
        mutableStateOf(TextFieldValue(text = value, selection = TextRange(value.length)))
    }
    val regex = remember { if (regexString != null) Regex(regexString) else null }
    var isNameValid by remember {
        mutableStateOf(value.length in range && (regex?.matches(value) != false))
    }

    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
        keyboardController?.show()
    }

    AlertDialog(
        modifier = modifier,
        containerColor = MaterialTheme.colorScheme.surface,
        onDismissRequest = onDismiss,
        title = {
            Column(
                modifier = Modifier
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    text = buildAnnotatedString {
                        append(title)
                        append("\n")
                        withStyle(
                            style = SpanStyle(
                                fontSize = MaterialTheme.typography.body.fontSize,
                                fontWeight = FontWeight.Normal,
                            )
                        ) {
                            append(
                                if (range.first == 0) {
                                    stringResource(id = R.string.maximum_characters, range.last)
                                } else {
                                    stringResource(
                                        id = R.string.please_enter_to_characters, range.first, range.last)
                                }
                            )
                        }
                    },
                    style = MaterialTheme.typography.bodyLargeBold,
                    textAlign = TextAlign.Center,
                )
            }

        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 200.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                OutlinedTextField(
                    isError = !isNameValid,
                    modifier = Modifier
                        .focusRequester(focusRequester),
                    value = inputTextFieldValue,
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                    keyboardActions = KeyboardActions(onDone = { keyboardController?.hide() }),
                    placeholder = {
                        if (!placeholder.isNullOrEmpty()) {
                            Text(
                                text = placeholder,
                                style = MaterialTheme.typography.bodyLarge,
                                color = MaterialTheme.colorScheme.mediumGrey
                            )
                        }
                    },
                    shape = MaterialTheme.shapes.small,
                    onValueChange = { newTextFieldValue: TextFieldValue ->
                        val newText = newTextFieldValue.text
                        inputTextFieldValue = if (newText.length > range.last) {
                            newTextFieldValue.copy(text = newText.substring(0, range.last))
                        } else {
                            newTextFieldValue
                        }
                        isNameValid = inputTextFieldValue.text.length in range && (regex?.matches(
                            inputTextFieldValue.text
                        ) != false)
                    },
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedTextColor = MaterialTheme.colorScheme.onSurface,
                        unfocusedTextColor = MaterialTheme.colorScheme.onSurface,
                        focusedBorderColor = Color.Transparent,
                        unfocusedBorderColor = Color.Transparent,
                        focusedContainerColor = MaterialTheme.colorScheme.nearWhite,
                        unfocusedContainerColor = MaterialTheme.colorScheme.nearWhite,
                        cursorColor = MaterialTheme.colorScheme.primary,
                        errorCursorColor = MaterialTheme.colorScheme.error,
                        errorBorderColor = MaterialTheme.colorScheme.error,
                        errorTextColor = MaterialTheme.colorScheme.onSurface,
                        errorContainerColor = MaterialTheme.colorScheme.nearWhite,
                        disabledContainerColor = MaterialTheme.colorScheme.nearWhite,
                    ),
                )
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = MaterialTheme.spacing.small)
                        .align(Alignment.CenterHorizontally),
                ) {
                    if (!isNameValid) {
                        val errorText = if (inputTextFieldValue.text.trim().length !in range) {
                            stringResource(R.string.minimum_2_characters)
                        } else if (regex != null && !regex.matches(inputTextFieldValue.text)) {
                            stringResource(R.string.name_regex_not_matches)
                        } else {
                            ""
                        }
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.widthIn(max = 200.dp)
                        ) {
                            Text(
                                text = errorText,
                                style = MaterialTheme.typography.bodySmall,
                                color = colorResource(R.color.alert_color_darker),
                                modifier = Modifier
                            )
                        }
                    }
                    Text(
                        text = "${inputTextFieldValue.text.length}/${range.last}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.darkGrey,
                        modifier = Modifier.align(Alignment.CenterEnd)
                    )
                }

            }
        },
        confirmButton = {
            TextButton(
                enabled = isNameValid,
                onClick = {
                    if (isNameValid) {
                        onSave(inputTextFieldValue.text)
                        onDismiss()
                    }
                }) {
                Text(stringResource(id = R.string.save))
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss
            ) {
                Text(stringResource(id = R.string.cancel))
            }
        }
    )
}

@Preview
@Composable
private fun InputTextDialogPreview() {
    M3AppTheme {
        InputTextDialog(title = "Edit name", value = "AAA", onDismiss = {}, onSave = {})
    }
}


@Preview
@Composable
private fun InputTextDialogPreview2() {
    M3AppTheme {
        InputTextDialog(title = "Edit name", value = "A", onDismiss = {}, onSave = {})
    }
}
