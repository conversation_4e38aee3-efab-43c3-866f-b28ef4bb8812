package com.stt.android.home.marketing

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import androidx.core.net.toUri
import androidx.fragment.app.FragmentManager
import com.squareup.moshi.Moshi
import com.stt.android.BuildConfig
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.MessageSource
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.di.VersionName
import com.stt.android.home.marketing.data.LinkUrlData
import com.stt.android.home.marketing.data.MetaData
import com.stt.android.home.marketing.data.SharingData
import com.stt.android.launcher.DeepLinkIntentBuilder
import com.stt.android.sharing.AnalysisData
import com.stt.android.sharing.AppSharingAnalysisImpl
import com.stt.android.sharing.ShareLinkInfo
import com.stt.android.sharing.SharingHelper
import com.stt.android.sharing.SharingInfo
import com.stt.android.sharing.SharingType
import com.stt.android.utils.CustomTabsUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class MarketingH5ViewModel @Inject constructor(
    private val currentUserController: CurrentUserController,
    private val deepLinkIntentBuilder: DeepLinkIntentBuilder,
    private val sharingHelper: SharingHelper,
    private val appSharingAnalysis: AppSharingAnalysisImpl,
    @VersionName private val version: String,
    dispatchers: CoroutinesDispatchers,
) : CoroutineViewModel(dispatchers) {
    private val moshi = Moshi.Builder().build()

    fun initAppSharingAnalysis(
        messageSource: MessageSource
    ) {
        launch {
            appSharingAnalysis.init(messageSource)
        }
    }

    fun getLanguage(): String {
        return with(Locale.getDefault()) {
            when {
                // Simplified Chinese
                language == "zh" && country == "CN" -> "zh-cn"
                // Traditional Chinese for other Chinese-speaking regions
                language == "zh" -> "zh-tw"
                else -> this.language
            }
        }
    }

    fun getBaseData(): String {
        val sessionKey = currentUserController.session?.sessionKey ?: ""
        val metaData = MetaData(sessionKey, version, getBrand())
        return moshi.adapter(MetaData::class.java).toJson(metaData)
    }

    /**
     * to use same brand name with iOS
     */
    private fun getBrand(): String {
        val appFlavor = BuildConfig.FLAVOR
        return when (appFlavor) {
            "suuntoChina" -> "suuntoChina"
            "suuntoPlaystore" -> "Suunto"
            "sportstrackerPlaystore" -> "SportsTracker"
            else -> ""
        }
    }

    fun handleDeepLink(deepLinkJson: String, context: Context) {
        val deepLinkUri = getLinkUrl(deepLinkJson)?.toUri() ?: run {
            Timber.w("get deep link url failed: $deepLinkJson")
            return
        }
        val fragmentOrPathParts = deepLinkIntentBuilder.getFragmentsOrPathParts(deepLinkUri)
        val type = fragmentOrPathParts.getOrNull(1)?.lowercase(Locale.ROOT) ?: ""
        deepLinkIntentBuilder.getDeepLinkIntent(
            context = context,
            uri = deepLinkUri,
            currentUserController = currentUserController,
            fragmentOrPathParts = fragmentOrPathParts,
            type = type
        )?.let {
            context.startActivity(it)
        } ?: run {
            Timber.w("can't find deep link intent: $deepLinkUri")
        }
    }

    fun handleExternalLink(linkUrlJson: String, context: Context) {
        val externalLinkUri = getLinkUrl(linkUrlJson)?.toUri() ?: run {
            Timber.w("get external link url failed: $linkUrlJson")
            return
        }
        CustomTabsUtils.launchCustomTab(context, externalLinkUri)
    }

    private fun getLinkUrl(linkUrlJson: String): String? {
        return try {
            moshi.adapter(LinkUrlData::class.java).fromJson(linkUrlJson)?.url
        } catch (e: Exception) {
            Timber.w(e, "parse linkUrlJson failed: ${e.message}")
            null
        }
    }

    fun shareLinkBySystem(activity: Activity, sharingInfo: String) {
        val sharingData = getSharingData(sharingInfo)
        sharingData?.let {
            sharingHelper.shareBySystem(
                SharingInfo(
                    sharingType = SharingType.LINK,
                    shareLinkInfo = ShareLinkInfo(it.link, it.title, it.message),
                    shareSourceName = it.linkName
                ),
                activity,
                analysisData = AnalysisData(
                    eventName = AnalyticsEvent.H5SHARE,
                    analyticsProperties = appSharingAnalysis.geSharingLinkAnalysis(
                        it.linkName
                    )
                )
            )
        }
    }

    fun shareLinkByApp(fragmentManager: FragmentManager, sharingInfo: String, linkIcon: Bitmap? = null) {
        val sharingData = getSharingData(sharingInfo)
        sharingData?.let {
            sharingHelper.shareByApp(
                SharingInfo(
                    sharingType = SharingType.LINK,
                    shareLinkInfo = ShareLinkInfo(it.link, it.title, it.message, linkIcon),
                    shareSourceName = it.linkName
                ), fragmentManager
            )
        }
    }

    private fun getSharingData(sharingInfo: String): SharingData? {
        return try {
            moshi.adapter(SharingData::class.java).fromJson(sharingInfo)
        } catch (e: Exception) {
            Timber.w(e, "parse sharingInfo failed: ${e.message}")
            null
        }
    }
}
