package com.stt.android.home.diary.diarycalendar.month

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.lifecycle.ViewModelProvider
import com.stt.android.common.ui.observeNotNull
import com.stt.android.home.diary.diarycalendar.BaseDiaryCalendarFragment
import com.stt.android.home.diary.diarycalendar.DiaryCalendarConstants.DIARY_CALENDAR_DISPLAY_MODE
import dagger.hilt.android.AndroidEntryPoint
import java.time.YearMonth

@AndroidEntryPoint
class DiaryCalendarMonthFragment : BaseDiaryCalendarFragment<DiaryCalendarMonthViewModel>() {

    private val yearMonth: YearMonth?
        get() = arguments?.getSerializable(ARG_MONTH) as? YearMonth

    /**
     * Calendar month view creates and keeps the current, next (if not in the future) and the
     * previous month view models. Here we are assigning each view model a unique key so that they
     * don't get mixed up.
     */
    override val viewModel: DiaryCalendarMonthViewModel by lazy(LazyThreadSafetyMode.NONE) {
        ViewModelProvider(this)["${yearMonth?.year}.${yearMonth?.month}", DiaryCalendarMonthViewModel::class.java]
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        yearMonth?.run {
            viewModel.load(this)
        }

        setupLiveDataObservers()
    }

    private fun setupLiveDataObservers() {
        viewModel.dateClickedEvent.observeNotNull(viewLifecycleOwner) {
            navigateToDayView(requireContext(), it.date)
        }
    }

    override fun onResume() {
        super.onResume()
        yearMonth?.let {
            calendarContainerViewModel.selectedYearMonth = it
            viewModel.load(it)
        }
    }

    companion object {
        fun newInstance(
            month: YearMonth,
            showActivitiesList: Boolean,
            displayMode: Int,
        ) = DiaryCalendarMonthFragment().apply {
            arguments = bundleOf(
                ARG_MONTH to month,
                ARG_SHOW_ACTIVITIES_LIST to showActivitiesList,
                DIARY_CALENDAR_DISPLAY_MODE to displayMode,
            )
        }

        const val ARG_MONTH = "com.stt.android.home.diary.calendar.MONTH"
    }
}
