package com.stt.android.home.settings.v2.ui

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.provider.MediaStore
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.content.res.AppCompatResources
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.graphics.drawable.toDrawable
import coil3.asImage
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import coil3.request.placeholder
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.R
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.STTErrorCodes
import com.stt.android.home.settings.v2.UserSettingsViewModel
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.ui.crop.CropImageActivity
import kotlinx.coroutines.launch
import java.io.File
import com.stt.android.core.R as CR

private const val PROFILE_IMAGE_WIDTH = 512
private const val PROFILE_IMAGE_HEIGHT = 512
private const val COVER_IMAGE_WIDTH = 1080
private const val COVER_IMAGE_HEIGHT = 400
private const val NAME_REGEX = "^[\\p{L}\\p{N}_\\s]+$"

@Composable
fun ScreenBody(
    viewModel: UserSettingsViewModel,
    settingTypes: List<SettingItemType>,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val profileImageUrl by viewModel.profileImageUrl.collectAsState()
    val tempProfilePictureFile = viewModel.tempProfilePictureFile
    val tempCoverPictureFile = viewModel.tempCoverPictureFile
    val userName by viewModel.userName.collectAsState()
    val realName by viewModel.realName.collectAsState()
    val showLocale by viewModel.showLocale.collectAsState()
    val locationDisplayName by viewModel.locationDisplayName.collectAsState()
    val bio by viewModel.bio.collectAsState()
    val coverPhoto by viewModel.coverPhoto.collectAsState()
    var editRealNameDialogState by remember { mutableStateOf(false) }
    var editBioDialogState by remember { mutableStateOf(false) }
    val uploadStatus by viewModel.uploadImageStatus.collectAsState()
    val snackbarHostState = remember { SnackbarHostState() }

    Box(modifier = modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.surface)
        ) {
            ProfileAvatar(profileImageUrl, tempProfilePictureFile, {
                coroutineScope.launch {
                    viewModel.updateProfilePicture(
                        it,
                        context,
                        tempProfilePictureFile,
                        PROFILE_IMAGE_WIDTH,
                        PROFILE_IMAGE_HEIGHT
                    )
                }
            })
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
            PersonalInfoSection(
                userName = userName,
                realName = realName ?: "",
                showLocale = showLocale,
                bio = bio,
                coverPhoto = coverPhoto,
                tempCoverPictureFile = tempCoverPictureFile,
                settingTypes = settingTypes,
                locationDisplayName = locationDisplayName,
                onUpdateCoverPhotoClicked = {
                    coroutineScope.launch {
                        viewModel.updateProfilePicture(
                            data = it,
                            applicationContext = context,
                            file = tempCoverPictureFile,
                            width = COVER_IMAGE_WIDTH,
                            height = COVER_IMAGE_HEIGHT,
                            isCoverPhoto = true,
                        )
                    }
                },
                onClickBioItem = {
                    editBioDialogState = true
                },
                updateShowLocaleCallback = {
                    viewModel.updateUserProfileShowLocale(it)
                },
                onClickRealNameItem = {
                    editRealNameDialogState = true
                },
                onClickLocationItem = {
                    viewModel.openLocationSelection(context)
                },
            )
        }
        if (editBioDialogState) {
            InputTextDialog(
                range = 0..256,
                title = stringResource(R.string.profileDescription),
                value = if (bio.isNullOrEmpty()) "" else bio ?: "",
                placeholder = stringResource(R.string.profile_description_default),
                onDismiss = { editBioDialogState = false },
                onSave = {
                    viewModel.updateUserProfileDescription(it.trim())
                    editBioDialogState = false
                },
            )
        }
        if (editRealNameDialogState) {
            InputTextDialog(
                title = stringResource(R.string.user_settings_edit_name),
                value = realName ?: "",
                onDismiss = { editRealNameDialogState = false },
                onSave = {
                    viewModel.updateUserProfileRealName(it.trim())
                    editRealNameDialogState = false
                },
                regexString = NAME_REGEX,
            )
        }

        if (uploadStatus.isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .align(Alignment.BottomCenter),
            ) {
                CircularProgressIndicator(
                    modifier = Modifier
                        .align(Alignment.Center)
                        .size(48.dp),
                    color = MaterialTheme.colorScheme.primary,
                )
            }
        }
        if (uploadStatus.errorCode != null) {
            val errorMessage = when (uploadStatus.errorCode) {
                STTErrorCodes.PROFILE_IMAGE_AUDIT_FAILED.code -> {
                    stringResource(R.string.review_failed)
                }

                STTErrorCodes.FILE_SIZE_EXCEEDS_LIMIT.code -> {
                    stringResource(R.string.image_out_of_size)
                }

                else -> {
                    stringResource(R.string.error_0)
                }
            }
            LaunchedEffect(uploadStatus.errorCode) {
                snackbarHostState.showSnackbar(errorMessage)
                viewModel.resetUploadImageStatus()
            }
        }
        SnackbarHost(
            hostState = snackbarHostState,
            modifier = Modifier.fillMaxWidth(),
        ) { data ->
            Snackbar(
                snackbarData = data,
            )
        }
    }
}

@Composable
private fun RealNameItem(
    realName: String,
    onClickRealNameItem: () -> Unit,
    modifier: Modifier = Modifier,
) {
    InfoItemText(
        label = stringResource(R.string.profileRealName),
        value = realName,
        valueColor = MaterialTheme.colorScheme.primary,
        onClick = {
            onClickRealNameItem()
        },
        modifier = modifier,
    )
}

@Composable
private fun BioItem(bio: String?, onClickBioItem: () -> Unit, modifier: Modifier = Modifier) {
    InfoItemText(
        label = stringResource(R.string.user_settings_bio),
        value = if (bio.isNullOrEmpty()) stringResource(R.string.profile_description_default) else bio,
        valueColor = MaterialTheme.colorScheme.primary,
        onClick = onClickBioItem,
        modifier = modifier,
    )
}

@Composable
private fun LocationItem(
    locationDisplayName: String,
    onClickLocationItem: () -> Unit,
    modifier: Modifier = Modifier,
) {
    InfoItemText(
        label = stringResource(R.string.location),
        value = locationDisplayName.ifBlank { stringResource(R.string.select_location) },
        valueColor = MaterialTheme.colorScheme.primary,
        onClick = {
            onClickLocationItem()
        },
        modifier = modifier,
    )
}

@Composable
private fun ShowLocationSwitchItem(
    showLocale: Boolean, updateShowLocaleCallback: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    InfoItemSwitch(
        label = stringResource(R.string.user_settings_display_location),
        isChecked = showLocale,
        onCheckedChange = {
            updateShowLocaleCallback.invoke(it)
        },
        modifier = modifier,
    )
}

@Composable
private fun PersonalInfoSection(
    userName: String,
    realName: String,
    showLocale: Boolean,
    bio: String?,
    coverPhoto: String?,
    tempCoverPictureFile: File,
    settingTypes: List<SettingItemType>,
    locationDisplayName: String,
    onUpdateCoverPhotoClicked: (Intent) -> Unit,
    onClickBioItem: () -> Unit,
    updateShowLocaleCallback: (Boolean) -> Unit,
    onClickRealNameItem: () -> Unit,
    onClickLocationItem: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        settingTypes.forEach {
            when (it) {
                SettingItemType.REAL_NAME -> RealNameItem(
                    realName,
                    onClickRealNameItem = onClickRealNameItem
                )

                SettingItemType.USER_NAME -> InfoItemText(
                    isEnabled = false,
                    label = stringResource(R.string.user_settings_username),
                    value = userName,
                    valueColor = MaterialTheme.colorScheme.onSurface,
                    onClick = {},
                )

                SettingItemType.LOCATION -> LocationItem(
                    locationDisplayName,
                    onClickLocationItem = onClickLocationItem
                )

                SettingItemType.LOCATION_SWITCH -> if (locationDisplayName.isNotBlank()) {
                    ShowLocationSwitchItem(
                        showLocale,
                        updateShowLocaleCallback,
                    )
                }

                SettingItemType.BIO -> BioItem(bio, onClickBioItem)

                SettingItemType.COVER_PHOTO -> CoverPhotoItem(
                    coverPhoto,
                    tempCoverPictureFile,
                    onUpdateCoverPhotoClicked,
                )

                else -> {}
            }
        }
    }
}

@Composable
private fun InfoItemSwitch(
    label: String,
    isChecked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    showDivider: Boolean = true,
) {
    Column(modifier = modifier) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.medium)
                .height(56.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
            )
            // Current UI is still M2 style, so we use M2 Switch for now
            androidx.compose.material.Switch(
                checked = isChecked,
                onCheckedChange = onCheckedChange,
                colors = androidx.compose.material.SwitchDefaults.colors(
                    checkedThumbColor = MaterialTheme.colorScheme.primary,
                    uncheckedThumbColor = MaterialTheme.colorScheme.lightGrey,
                    uncheckedTrackColor = MaterialTheme.colorScheme.secondary,
                ),
            )
        }
        if (showDivider) {
            HorizontalDivider()
        }
    }
}

@Composable
private fun InfoItemText(
    label: String,
    value: String,
    valueColor: Color,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    showDivider: Boolean = true,
    isEnabled: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
) {
    InfoItem(
        onClick = onClick,
        modifier = modifier,
        isEnabled = isEnabled,
        label = label,
        showDivider = showDivider,
        endView = {
            Text(
                maxLines = maxLines,
                overflow = TextOverflow.Ellipsis,
                text = value,
                style = MaterialTheme.typography.bodyLarge,
                color = valueColor,
                textAlign = TextAlign.End,
            )
        })
}

@Composable
private fun InfoItemCover(
    label: String,
    imageUrl: String,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
) {
    InfoItem(
        onClick = onClick,
        modifier = modifier,
        label = label,
        showDivider = false,
        endView = {
            val imageData = imageUrl.takeIf { it.isNotBlank() } ?: R.drawable.profile_cove_photo_default
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(imageData)
                    .placeholder(MaterialTheme.colorScheme.lightGrey.toArgb().toDrawable())
                    .crossfade(true)
                    .fallback(
                        AppCompatResources.getDrawable(
                            LocalContext.current,
                            R.drawable.profile_cove_photo_default
                        )?.asImage(),
                    ).build(),
                contentDescription = null,
                modifier = Modifier
                    .size(width = 78.dp, height = 40.dp)
                    .border(
                        width = 1.dp,
                        color = MaterialTheme.colorScheme.dividerColor,
                        shape = MaterialTheme.shapes.small,
                    )
                    .clip(
                        MaterialTheme.shapes.small
                    ),
                contentScale = ContentScale.Crop,
            )
        })
}

@Composable
private fun InfoItem(
    label: String,
    modifier: Modifier = Modifier,
    showDivider: Boolean = true,
    isEnabled: Boolean = true,
    onClick: (() -> Unit)? = null,
    endView: @Composable (() -> Unit)? = null,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .widthIn(min = 56.dp)
                .alpha(if (isEnabled) 1f else 0.5f)
                .clickableThrottleFirst(enabled = isEnabled, onClick = onClick ?: {})
                .padding(MaterialTheme.spacing.medium),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
            )
            Box(
                modifier = Modifier.fillMaxWidth(0.7f),
                contentAlignment = Alignment.CenterEnd,
            ) {
                endView?.invoke()
            }

        }
        if (showDivider) {
            HorizontalDivider()
        }
    }
}

fun buildImageSelectIntent(
    width: Int,
    height: Int,
    aspectX: Int,
    aspectY: Int,
    tempProfilePictureFile: File,
): Intent {

    return Intent(Intent.ACTION_GET_CONTENT).addCategory(Intent.CATEGORY_OPENABLE)
        .addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION)
        .putExtra("aspectX", aspectX)
        .putExtra("aspectY", aspectY)
        .putExtra("outputX", width)
        .putExtra("outputY", height)
        .putExtra(MediaStore.EXTRA_OUTPUT, Uri.fromFile(tempProfilePictureFile))
//        .putExtra("outputFormat", toString())
        .putExtra(Intent.EXTRA_MIME_TYPES, MediaStoreUtils.SUPPORTED_IMAGE_MIME_TYPES)
        // exclude gif images
        .setType("*/*")
}

@Composable
private fun CoverPhotoItem(
    coverPhoto: String?,
    tempCoverPictureFile: File,
    onUpdateCoverPhotoClicked: (Intent) -> Unit,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val launcherCrop = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { activityResult ->
        if (activityResult.resultCode == Activity.RESULT_OK) {
            val data = activityResult.data
            data?.let {
                onUpdateCoverPhotoClicked.invoke(it)
            }
        }
    }
    val launcherImageSelect = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { activityResult ->
        if (activityResult.resultCode == Activity.RESULT_OK) {
            val data = activityResult.data
            data?.data?.let {
                launcherCrop.launch(
                    CropImageActivity.newStartIntent(
                        context,
                        it,
                        COVER_IMAGE_WIDTH,
                        COVER_IMAGE_HEIGHT,
                        false,
                        tempCoverPictureFile,
                    )
                )
            }
        }
    }
    InfoItemCover(
        label = stringResource(R.string.user_settings_cover_photo),
        imageUrl = coverPhoto ?: "",
        onClick = {
            launcherImageSelect.launch(
                buildImageSelectIntent(
                    width = COVER_IMAGE_WIDTH,
                    height = COVER_IMAGE_HEIGHT,
                    aspectX = 4,
                    aspectY = 2,
                    tempProfilePictureFile = tempCoverPictureFile,
                )
            )
        },
        modifier = modifier,
    )
}

@Composable
private fun ProfileAvatar(
    profileImageUrl: String?,
    tempProfilePictureFile: File,
    onUpdateProfilePictureClicked: (Intent) -> Unit,
    modifier: Modifier = Modifier,
) {
    val onClick = function(onUpdateProfilePictureClicked, tempProfilePictureFile)

    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .fillMaxWidth()
            .padding(top = MaterialTheme.spacing.large),
    ) {
        Box(
            modifier = Modifier
                .size(114.dp)
                .padding(MaterialTheme.spacing.xsmall)
                .clickableThrottleFirst(onClick = onClick),
        ) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current).data(profileImageUrl)
                    .crossfade(true)
                    .placeholderWithFallback(
                        LocalContext.current,
                        CR.drawable.ic_default_profile_image_light,
                    ).transformations(CircleCropTransformation()).build(),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxSize()
                    .clip(CircleShape)
                    .padding(MaterialTheme.spacing.xsmaller),
                contentScale = ContentScale.Crop,
            )
            Surface(
                shape = CircleShape,
                color = MaterialTheme.colorScheme.surface,
                tonalElevation = 0.dp,
                shadowElevation = 4.dp,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = MaterialTheme.spacing.medium),
            ) {
                IconButton(
                    modifier = Modifier.size(MaterialTheme.iconSizes.small),
                    onClick = onClick,
                ) {
                    Icon(
                        painter = painterResource(R.drawable.ic_svg_edit),
                        tint = Color.Unspecified,
                        contentDescription = stringResource(R.string.back),
                    )
                }
            }
        }
    }
}

@Composable
private fun function(
    onUpdateProfilePictureClicked: (Intent) -> Unit,
    tempProfilePictureFile: File
): () -> Unit {
    val context = LocalContext.current
    val launcherCrop = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { activityResult ->
        if (activityResult.resultCode == Activity.RESULT_OK) {
            val data = activityResult.data
            data?.let {
                onUpdateProfilePictureClicked.invoke(it)
            }
        }
    }

    val launcherImageSelect = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { activityResult ->
        if (activityResult.resultCode == Activity.RESULT_OK) {
            val data = activityResult.data
            data?.data?.let {
                launcherCrop.launch(
                    CropImageActivity.newStartIntent(
                        context,
                        it,
                        PROFILE_IMAGE_WIDTH,
                        PROFILE_IMAGE_HEIGHT,
                        true,
                        tempProfilePictureFile
                    )
                )
            }
        }
    }

    val onClick = {
        launcherImageSelect.launch(
            buildImageSelectIntent(
                width = PROFILE_IMAGE_WIDTH,
                height = PROFILE_IMAGE_HEIGHT,
                aspectX = 1,
                aspectY = 1,
                tempProfilePictureFile = tempProfilePictureFile
            )
        )
    }
    return onClick
}

@Preview
@Composable
private fun ScreenBodyPreview() {
    val commonItems = listOf(
        SettingItemType.REAL_NAME,
        SettingItemType.USER_NAME,
        SettingItemType.LOCATION,
        SettingItemType.LOCATION_SWITCH,
        SettingItemType.BIO,
        SettingItemType.COVER_PHOTO,
    )
    M3AppTheme {
        val scrollState = rememberScrollState()
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
        ) {
            ProfileAvatar("", File(""), {})
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
            PersonalInfoSection(
                userName = "userName",
                realName = "realName",
                showLocale = true,
                bio = "bio",
                coverPhoto = "",
                tempCoverPictureFile = File(""),
                settingTypes = commonItems,
                locationDisplayName = "United States\nFlorida",
                onUpdateCoverPhotoClicked = {},
                onClickBioItem = {},
                updateShowLocaleCallback = {},
                onClickRealNameItem = {},
                onClickLocationItem = {},
            )
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.xlarge))
        }
    }
}

@Preview
@Composable
private fun ProfileAvatarPreview() {
    M3AppTheme {
        ProfileAvatar("", File(""), {})
    }
}

@Preview
@Composable
private fun BioItemPreview() {
    M3AppTheme {
        Column {
            BioItem("bio", {})
            BioItem(
                "Very long bio, Very long bio, Very long bio, Very long bio, Very long bio, Very long bio, Very long bio, Very long bio, ",
                {})
        }
    }
}


