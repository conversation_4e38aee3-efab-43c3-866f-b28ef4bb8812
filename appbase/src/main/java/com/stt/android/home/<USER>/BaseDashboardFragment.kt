package com.stt.android.home.dashboard

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.map
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.airbnb.epoxy.EpoxyVisibilityTracker
import com.airbnb.epoxy.stickyheader.StickyHeaderLinearLayoutManager
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.analytics.MessageSource
import com.stt.android.appversion.AppVersionViewModel
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.common.ui.SimpleProgressDialogFragment
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateListFragment2
import com.stt.android.controllers.CurrentUserController
import com.stt.android.databinding.DashboardFragmentBinding
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.diveplanner.DivePlannerNavigator
import com.stt.android.domain.marketing.MarketingBannerInfo
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.combineLatestAllowNulls
import com.stt.android.home.HomeActivityActions
import com.stt.android.home.HomeTab
import com.stt.android.home.InsertMenstrualCycleViewModel
import com.stt.android.home.WorkoutBroadcastActionListener
import com.stt.android.home.dashboard.toolbar.DashboardToolbarPresenter
import com.stt.android.home.dashboard.toolbar.MenstrualCycleCallback
import com.stt.android.home.marketing.MarketingH5Activity
import com.stt.android.inappreview.InAppReviewSource
import com.stt.android.inappreview.InAppReviewTrigger
import com.stt.android.launcher.DeepLinkIntentBuilder
import com.stt.android.maps.MapSnapshotSpec
import com.stt.android.maps.MapSnapshotter
import com.stt.android.menstrualcycle.LoggedFrom
import com.stt.android.menstrualcycle.MenstrualCycleOnboardingNavigator
import com.stt.android.menstrualcycle.MenstrualCycleOnboardingNavigator.Companion.EXTRA_TRY_LOG_MENSTRUAL_CYCLE
import com.stt.android.menstrualcycle.OnboardingDoneReason
import com.stt.android.menstrualcycle.log.LogMenstrualCycleFragment
import com.stt.android.menstrualcycle.log.OnLogMenstrualCycleDoneListener
import com.stt.android.menstrualcycle.regularity.MenstrualCycleRegularitySheetCreator
import com.stt.android.multimedia.gallery.MediaGalleryActivity
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.newfeed.FeedDataContainer
import com.stt.android.newfeed.FeedEpoxyController
import com.stt.android.newfeed.FeedMapSizeCalculator
import com.stt.android.newfeed.FeedTopBannerNavigator
import com.stt.android.session.SignInFlowHook
import com.stt.android.social.following.PeopleActivity
import com.stt.android.tags.TagsNavigator
import com.stt.android.ui.activities.WorkoutEditDetailsActivity
import com.stt.android.ui.components.workout.WorkoutShareInfo
import com.stt.android.ui.fragments.login.terms.OnTermsListener
import com.stt.android.utils.PermissionUtils
import com.stt.android.watch.WorkoutPlannerNavigator
import com.stt.android.workoutdetail.comments.CommentsDialogFragment
import com.stt.android.workouts.sharepreview.WorkoutSharePreviewActivity
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import pub.devrel.easypermissions.EasyPermissions
import timber.log.Timber
import java.time.LocalDate
import java.util.Locale
import javax.inject.Inject

// Note: derived classes should use @AndroidEntryPoint annotation
abstract class BaseDashboardFragment :
    ViewStateListFragment2<FeedDataContainer, DashboardViewModel>(),
    SwipeRefreshLayout.OnRefreshListener,
    EasyPermissions.PermissionCallbacks,
    HomeTab, MenstrualCycleCallback {

    protected val binding: DashboardFragmentBinding get() = requireBinding()

    @Inject
    lateinit var toolbarPresenter: DashboardToolbarPresenter

    @Inject
    lateinit var divePlannerNavigator: DivePlannerNavigator

    @Inject
    lateinit var workoutPlannerNavigator: WorkoutPlannerNavigator

    @Inject
    lateinit var workoutBroadcastActionListener: WorkoutBroadcastActionListener

    @Inject
    lateinit var signInFlowHook: SignInFlowHook

    @Inject
    lateinit var workoutShareHelper: WorkoutShareHelper

    @Inject
    lateinit var mapSnapshotter: MapSnapshotter

    @Inject
    lateinit var tagsNavigator: TagsNavigator

    @Inject
    lateinit var inAppReviewTrigger: InAppReviewTrigger

    @Inject
    lateinit var feedTopBannerNavigator: FeedTopBannerNavigator

    @Inject
    lateinit var firebaseAnalyticisTracker: FirebaseAnalyticsTracker

    @Inject
    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    @Inject
    lateinit var menstrualCycleOnboardingNavigator: MenstrualCycleOnboardingNavigator

    @Inject
    lateinit var menstrualCycleRegularitySheetCreator: MenstrualCycleRegularitySheetCreator

    @Inject
    lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    @Inject
    lateinit var deepLinkIntentBuilder: DeepLinkIntentBuilder

    @Inject
    lateinit var currentUserController: CurrentUserController

    private var homeActivityActions: HomeActivityActions? = null

    private val gridViewModel: DashboardGridViewModel by activityViewModels()

    private val insertMenstrualCycleViewModel: InsertMenstrualCycleViewModel by activityViewModels()

    private val appVersionViewModel: AppVersionViewModel by activityViewModels()

    private val swipeContainer: SwipeRefreshLayout
        get() = binding.swipeContainer

    override val viewModel: DashboardViewModel by viewModels()

    override val layoutId = R.layout.dashboard_fragment

    private var workoutHeaderForAddingPhotos: WorkoutHeader? = null

    private val epoxyVisibilityTracker by lazy { EpoxyVisibilityTracker() }

    private val listScrollListener = object : RecyclerView.OnScrollListener() {
        private var isTracked = false

        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            // remove the red dot when the list on scroll state changed
            viewModel.viewState.value?.data?.filterTagData?.defaultSelected?.let {
                viewModel.markTagRead(it)
            }
            (controller as? FeedEpoxyController)?.setScrollingState(newState)

            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                if (!isTracked) {
                    isTracked = true
                    amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.FEED_SCROLLED)
                    firebaseAnalyticisTracker.trackEvent(AnalyticsEvent.FEED_SCROLLED)
                }
            }
        }
    }

    private val onboardingResultLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
            if (it.resultCode == Activity.RESULT_OK) {
                openLogMenstrualCyclePicker()
            }
        }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.dashboardToolbar.setLifecycle(lifecycle)
        binding.dashboardToolbar.setPresenter(toolbarPresenter)
        binding.dashboardToolbar.setDivePlannerNavigator(divePlannerNavigator)
        binding.dashboardToolbar.setWorkoutPlannerNavigator(workoutPlannerNavigator)
        binding.dashboardToolbar.setMenstrualCycleCallback(this)
        viewModel.mapSize = FeedMapSizeCalculator.calculate(resources)
        swipeContainer.setOnRefreshListener(this)
        swipeContainer.setColorSchemeColors(
            ContextCompat.getColor(requireContext(), R.color.accent),
            ContextCompat.getColor(requireContext(), R.color.green),
            ContextCompat.getColor(requireContext(), R.color.blue)
        )

        (controller as? FeedEpoxyController)?.let {
            it.lifecycleOwner = this.viewLifecycleOwner
            it.fragmentManager = childFragmentManager
            it.rewriteNavigator = rewriteNavigator
        }

        initializeRecyclerView()

        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.isRefreshing
                    .collect { swipeContainer.isRefreshing = it }
            }
        }

        viewModel.needsToAcceptTerms.observeNotNull(viewLifecycleOwner) { needAccept ->
            if (needAccept) {
                showTerms()
            }
        }

        viewModel.needLogin.observeNotNull(viewLifecycleOwner) { needLogin ->
            if (needLogin) {
                showNeedLoginDialog()
            }
        }

        viewModel.workoutClicked.observeNotNull(viewLifecycleOwner) { workoutHeader ->
            homeActivityActions?.navigateToWorkoutDetails(workoutHeader.id, showComments = false)
            inAppReviewTrigger.incNumberOfVisitsForSource(InAppReviewSource.WORKOUT_DETAILS)
            inAppReviewTrigger.scheduleInAppReviewIfPossible()
        }

        viewModel.addCommentClicked.observeNotNull(viewLifecycleOwner) { workoutHeader ->
            showCommentsDialogOrNavigateToWorkoutDetailsIfFailed(workoutHeader)
        }

        viewModel.addPhotoClicked.observeNotNull(viewLifecycleOwner) { workoutHeader ->

            if (PermissionUtils.requestPermissionsIfNeeded(
                    this,
                    PermissionUtils.STORAGE_PERMISSIONS,
                    getString(R.string.storage_permission_rationale_picker),
                    STORAGE_PERMISSION_REQUIRED_FOR_GALLERY_REQUEST_CODE
                )
            ) {
                // Keep hold of workout header while requesting permissions
                workoutHeaderForAddingPhotos = workoutHeader
            } else {
                launchGalleryForAddingPhoto(workoutHeader)
            }
        }

        viewModel.openImageSharePreview.observeNotNull(viewLifecycleOwner) { (workoutHeader, imageIndex) ->
            val (intent, options) = WorkoutSharePreviewActivity.newStartIntent(
                workoutHeader,
                requireContext(),
                imageIndex,
                SportieShareSource.FEED
            )
            requireContext().startActivity(intent, options.toBundle())
        }

        viewModel.shareLinkLoading.observeNotNull(viewLifecycleOwner) { isLoading ->
            val fm = requireActivity().supportFragmentManager
            if (isLoading) {
                val dialog =
                    SimpleProgressDialogFragment.newInstance(getString(R.string.creating_share_link))
                dialog.show(fm, SimpleProgressDialogFragment.FRAGMENT_TAG)
            } else {
                val fragment =
                    fm.findFragmentByTag(SimpleProgressDialogFragment.FRAGMENT_TAG) as? DialogFragment
                fragment?.dismiss()
            }
        }
        viewModel.shareLinkErrorEvent.observeNotNull(viewLifecycleOwner) {
            Snackbar.make(
                binding.root,
                R.string.error_generic_try_again,
                Snackbar.LENGTH_LONG
            ).show()
        }

        viewModel.openLinkShare.observeNotNull(viewLifecycleOwner) { workoutHeader ->
            workoutShareHelper.sendImplicitWorkoutLinkShareIntent(
                requireActivity(),
                workoutHeader,
                SportieShareSource.FEED
            )
        }

        viewModel.openWorkoutMapGraphAnalysisClicked.observeNotNull(viewLifecycleOwner) {
            homeActivityActions?.navigateToWorkoutMapGraphAnalysis(it)
        }

        viewModel.openWorkoutDetails.observeNotNull(viewLifecycleOwner) {
            val intent = WorkoutEditDetailsActivity.newStartIntentForEditing(
                activity,
                it.id,
                AnalyticsPropertyValue.SourceProperty.ADD_DESCRIPTION_AND_TAGS
            )
            startActivity(intent)
        }

        viewModel.findPeopleClicked.observeNotNull(viewLifecycleOwner) {
            startActivity(PeopleActivity.newIntent(requireActivity()))
        }

        viewModel.openSummariesForTagClicked.observeNotNull(viewLifecycleOwner) {
            tagsNavigator.openDiaryForTag(requireContext(), it)
        }

        viewModel.openFullscreenGraphAnalysisClicked.observeNotNull(viewLifecycleOwner) {
            homeActivityActions?.navigateToFullscreenGraphAnalysis(it)
        }

        viewModel.openTopBannerContents.observeNotNull(viewLifecycleOwner) {
            feedTopBannerNavigator.navigateToContent(requireContext(), it)
        }

        viewModel.marketingBannerClicked.observeNotNull(viewLifecycleOwner, ::onBannerClick)

        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                mapSnapshotter.runSnapshotterEngine(requireContext().applicationContext)
            }
        }

        gridViewModel.viewState
            .map { it.data?.widgetCustomizationModeEnabled }
            .distinctUntilChanged()
            .observeNotNull(viewLifecycleOwner) {
                val customizationModeEnabled = it == true

                binding.swipeContainer.isEnabled = !customizationModeEnabled
                if (customizationModeEnabled) {
                    binding.list.scrollToPosition(0)
                }
            }
        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.multipleWorkoutShareFlow
                    .collect { (workoutHeader, workoutShareInfo, summaryExtension) ->
                        val imageIndex = when (val selected = workoutShareInfo.selected) {
                            is WorkoutShareInfo.Selected.None,
                            is WorkoutShareInfo.Selected.Map -> 0
                            is WorkoutShareInfo.Selected.Image -> if (workoutHeader.isPolylineEmpty) {
                                selected.index
                            } else {
                                selected.index + 1
                            }
                        }
                        val watchName = summaryExtension?.displayName.orEmpty()
                        workoutShareHelper.toMultipleWorkoutShareWays(
                            requireContext(),
                            workoutHeader,
                            imageIndex,
                            watchName,
                        )
                    }
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                insertMenstrualCycleViewModel.menstrualCycleRegularityChanged.collectLatest {
                    menstrualCycleRegularitySheetCreator.create(it)
                        ?.show(parentFragmentManager, it.name)
                }
            }
        }

        handleActivityIntent(activity?.intent)

        combineLatestAllowNulls(
            appVersionViewModel.newVersionLive,
            getHeadsetRedDotLiveData(),
        ).observeNotNull(viewLifecycleOwner) { result ->
            val showRedDot = result.first != null || result.second == true
            binding.dashboardToolbar.setRedDotVisible(showRedDot)
        }
    }

    protected open fun getHeadsetRedDotLiveData(): LiveData<Boolean> = MutableLiveData(false)

    private fun handleActivityIntent(intent: Intent?) {
        if (intent?.getBooleanExtra(EXTRA_LOG_MENSTRUAL_CYCLE, false) == true) {
            intent.removeExtra(EXTRA_LOG_MENSTRUAL_CYCLE)
            logMenstrualCycle()
        }
    }

    private fun showCommentsDialogOrNavigateToWorkoutDetailsIfFailed(
        workoutHeader: WorkoutHeader,
    ) {
        if (isAdded) {
            CommentsDialogFragment.newInstance(workoutHeader, true)
                .show(parentFragmentManager, null)
        } else {
            homeActivityActions?.navigateToWorkoutDetails(workoutHeader.id, showComments = true)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        homeActivityActions = requireActivity() as HomeActivityActions

        workoutBroadcastActionListener.listener = object : WorkoutBroadcastActionListener.Listener {
            override fun onSyncFinished() {
                viewModel.fullyLoadFeedContents(
                    updateRemoteCards = true,
                    showRefreshIndicator = true
                )
            }

            override fun onUserStatusChanged() {
                viewModel.fullyLoadFeedContents(
                    updateRemoteCards = true,
                    showRefreshIndicator = true
                )
            }

            override fun onWorkoutSavedOrUpdated() {
                viewModel.fullyLoadFeedContents(
                    updateRemoteCards = false,
                    showRefreshIndicator = false
                )
            }

            override fun onWorkoutSynced() {
                viewModel.fullyLoadFeedContents(
                    updateRemoteCards = false,
                    showRefreshIndicator = false
                )
            }

            override fun onPictureStored() {
                viewModel.loadFeedContentsUnlessAlreadyLoading(
                    updateRemoteCards = false,
                    showRefreshIndicator = false
                )
            }
        }

        workoutBroadcastActionListener.startListening()
        feedTopBannerNavigator.register(this)
    }

    override fun onDestroyView() {
        binding.list.clearOnScrollListeners()
        epoxyVisibilityTracker.detach(binding.list)
        binding.dashboardToolbar.setMenstrualCycleCallback(null)
        homeActivityActions = null
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        workoutBroadcastActionListener.stopListening()
        workoutBroadcastActionListener.listener = null
    }

    override fun onStart() {
        super.onStart()
        viewModel.checkTermsAccepted()
        viewModel.fullyLoadFeedContents(
            updateRemoteCards = true,
            showRefreshIndicator = !viewModel.hasDoneInitialSync()
        )
    }

    override fun onRefresh() {
        homeActivityActions?.refreshAppData(force = true)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        // Forward results to EasyPermissions
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this)
    }

    override fun onPermissionsGranted(requestCode: Int, perms: List<String>) {
        if (requestCode == STORAGE_PERMISSION_REQUIRED_FOR_GALLERY_REQUEST_CODE) {
            launchGalleryForAddingPhoto(workoutHeaderForAddingPhotos)
            workoutHeaderForAddingPhotos = null
        }
    }

    override fun onPermissionsDenied(requestCode: Int, perms: MutableList<String>) = Unit

    private fun launchGalleryForAddingPhoto(workoutHeader: WorkoutHeader?) {
        workoutHeader?.let {
            startActivity(MediaGalleryActivity.newIntentForDirectAddToWorkout(requireContext(), it))
        }
    }

    override fun moveTo(position: Int) {
        recyclerView.scrollToPosition(position)
    }

    private fun showTerms() {
        (activity as? OnTermsListener)?.onShowTerms()
    }

    private fun showNeedLoginDialog() {
        viewModel.resetNeedLogin()

        val fm = if (isAdded) parentFragmentManager else return
        if (fm.findFragmentByTag(LOGIN_REQUIRED_DIALOG_FRAGMENT_TAG) != null) {
            return
        }

        val simpleDialogFragment = SimpleDialogFragment.newInstance(
            getString(R.string.login_required),
            getString(R.string.login),
            getString(R.string.ok),
            getString(R.string.cancel)
        )

        simpleDialogFragment.setTargetFragment(this, LOGIN_REQUIRED_REQUEST_CODE)
        simpleDialogFragment.show(fm, LOGIN_REQUIRED_DIALOG_FRAGMENT_TAG)
    }

    private fun initializeRecyclerView() {
        binding.list.apply {
            addOnScrollListener(listScrollListener)
            layoutManager = StickyHeaderLinearLayoutManager(context)
            itemAnimator = null

            epoxyVisibilityTracker.attach(this)
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == LOGIN_REQUIRED_REQUEST_CODE && resultCode == Activity.RESULT_OK) {
            val context = requireContext()
            context.startActivity(
                signInFlowHook.newStartIntent(context, null)
            )
        }
    }

    override fun onStateChanged(state: ViewState<FeedDataContainer?>) {
        super.onStateChanged(state)

        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                val specs = state.data
                    ?.workoutCards
                    ?.mapNotNull { cardData ->
                        cardData.cardInfo
                            .takeIf { it.polylineHashCode != "".hashCode() }
                            ?.let { cardInfo ->
                                MapSnapshotSpec.Workout(
                                    workoutId = cardInfo.workoutHeader.id,
                                    width = viewModel.mapSize.width,
                                    height = viewModel.mapSize.height,
                                )
                            }
                    } ?: emptyList()
                mapSnapshotter.setMapSnapshotSpecsForBackgroundGeneration(specs, lifecycleScope)
            }
        }
    }

    override fun logMenstrualCycle() {
        if (insertMenstrualCycleViewModel.menstrualCycleEnabled) {
            openLogMenstrualCyclePicker()
        } else {
            onboardingResultLauncher.launch(
                menstrualCycleOnboardingNavigator.newOnboardingActivityIntent(
                    requireContext(),
                    OnboardingDoneReason.FIRST_PERIOD_LOGGED
                ).apply { putExtra(EXTRA_TRY_LOG_MENSTRUAL_CYCLE, true) }
            )
        }
    }

    private fun openLogMenstrualCyclePicker() {
        viewLifecycleOwner.lifecycleScope.launch {
            val periodDuration = insertMenstrualCycleViewModel.getPeriodDuration()
            LogMenstrualCycleFragment.create(
                LocalDate.now(),
                periodDuration,
                object : OnLogMenstrualCycleDoneListener {
                    override fun onDone(startDate: LocalDate, endDate: LocalDate) {
                        insertMenstrualCycleViewModel.insertMenstrualCycle(
                            startDate,
                            endDate,
                            LoggedFrom.HOME
                        )
                    }
                }).show(childFragmentManager, "LogMenstrualCycle")
        }
    }

    private fun onBannerClick(banner: MarketingBannerInfo) {
        val link = banner.link?.takeIf { it.isNotBlank() }?.toUri() ?: return
        when (banner.linkType) {
            MarketingBannerInfo.LinkType.DEEPLINK -> {
                val fragmentOrPathParts = deepLinkIntentBuilder.getFragmentsOrPathParts(link)
                val type = fragmentOrPathParts.getOrNull(1)?.lowercase(Locale.ROOT) ?: ""
                deepLinkIntentBuilder.getDeepLinkIntent(
                    context = requireContext(),
                    uri = link,
                    currentUserController = currentUserController,
                    fragmentOrPathParts = fragmentOrPathParts,
                    type = type
                )?.let {
                    startActivity(it)
                }
            }

            MarketingBannerInfo.LinkType.EXTERNAL_URL -> {
                try {
                    @Suppress("UnsafeImplicitIntentLaunch")
                    startActivity(Intent(Intent.ACTION_VIEW, link))
                } catch (e: ActivityNotFoundException) {
                    Timber.d(e, "No activity found to handle the link: $link")
                }
            }

            MarketingBannerInfo.LinkType.H5 -> {
                startActivity(
                    MarketingH5Activity.newIntent(
                        context = requireContext(),
                        url = link.toString(),
                        MessageSource.BANNER,
                    )
                )
            }

            null -> Unit
        }
    }

    companion object {
        const val FRAGMENT_TAG = "com.stt.android.home.dashboard.DashboardFragment.FRAGMENT_TAG"
        private const val LOGIN_REQUIRED_DIALOG_FRAGMENT_TAG =
            "com.stt.android.home.dashboard.DashboardFragment.LOGIN_REQ_TAG"
        private const val LOGIN_REQUIRED_REQUEST_CODE = 301
        private const val STORAGE_PERMISSION_REQUIRED_FOR_GALLERY_REQUEST_CODE = 302

        const val EXTRA_LOG_MENSTRUAL_CYCLE = "com.stt.android.EXTRA_LOG_MENSTRUAL_CYCLE"

        @JvmStatic
        fun newInstance() = DashboardFragment()
    }
}
