package com.stt.android.home.dashboardv2.ui.banners

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.stt.android.R
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.marketing.MarketingBannerInfo
import kotlinx.coroutines.delay
import java.util.concurrent.TimeUnit

@Composable
internal fun BannerPager(
    banners: List<MarketingBannerInfo>,
    onBannerClick: (MarketingBannerInfo) -> Unit,
    onBannerClose: (MarketingBannerInfo) -> Unit,
    onBannerExposure: (MarketingBannerInfo) -> Unit,
    modifier: Modifier = Modifier,
    paddingStart: Dp = MaterialTheme.spacing.medium,
    paddingEnd: Dp = MaterialTheme.spacing.medium,
    paddingTop: Dp = MaterialTheme.spacing.medium,
    paddingBottom: Dp = MaterialTheme.spacing.medium,
) {
    val pagerState = rememberPagerState { banners.size }

    Box(
        modifier = modifier
            .padding(
                start = paddingStart,
                end = paddingEnd,
                top = paddingTop,
                bottom = paddingBottom,
            )
            .border(
                width = 1.dp,
                color = MaterialTheme.colorScheme.lightGrey,
                shape = RoundedCornerShape(16.dp),
            )
            .clip(shape = RoundedCornerShape(17.dp)),
    ) {
        HorizontalPager(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            state = pagerState,
        ) { page ->
            BannerView(
                banner = banners[page],
                onClick = onBannerClick,
                onClose = onBannerClose,
            )
        }
        if (pagerState.pageCount > 1) {
            PageIndicator(
                modifier = Modifier
                    .padding(bottom = 6.dp)
                    .align(Alignment.BottomCenter),
                pagerState = pagerState,
            )
        }
    }

    LaunchedEffect(banners) {
        while (true) {
            banners.getOrNull(pagerState.currentPage)?.let { onBannerExposure(it) }
            delay(TimeUnit.SECONDS.toMillis(10))
            pagerState.animateScrollToPage((pagerState.currentPage + 1) % pagerState.pageCount)
        }
    }
}

@Composable
private fun BannerView(
    banner: MarketingBannerInfo,
    onClick: (MarketingBannerInfo) -> Unit,
    onClose: (MarketingBannerInfo) -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .clickable(onClick = { onClick(banner) })
            .fillMaxWidth()
            .aspectRatio(2f),
    ) {
        AsyncImage(
            modifier = Modifier.fillMaxSize(),
            model = banner.previewUrl.phone,
            contentDescription = null,
            contentScale = ContentScale.Crop,
        )
        Box(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .background(
                    Brush.verticalGradient(
                        listOf(Color.Transparent, Color.Black.copy(alpha = 0.75f))
                    )
                )
                .fillMaxWidth()
                .aspectRatio(343f / 98f),
        )
        Text(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .fillMaxWidth()
                .padding(
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                    bottom = MaterialTheme.spacing.medium,
                ),
            text = banner.title,
            style = MaterialTheme.typography.bodyXLargeBold,
            color = Color.White,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
        )
        IconButton(
            modifier = Modifier
                .padding(MaterialTheme.spacing.small)
                .align(Alignment.TopEnd)
                .size(MaterialTheme.iconSizes.large),
            onClick = { onClose(banner) },
        ) {
            Image(
                painter = painterResource(R.drawable.ic_action_close_shadow),
                contentDescription = null,
            )
        }
    }
}

@Composable
private fun PageIndicator(pagerState: PagerState, modifier: Modifier = Modifier) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
    ) {
        repeat(pagerState.pageCount) { page ->
            Box(
                modifier = Modifier
                    .clip(CircleShape)
                    .background(color = Color.White.copy(alpha = if (pagerState.currentPage == page) 1f else 0.5f))
                    .size(8.dp),
            )
        }
    }
}
