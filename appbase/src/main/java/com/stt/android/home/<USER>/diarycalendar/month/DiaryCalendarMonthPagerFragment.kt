package com.stt.android.home.diary.diarycalendar.month

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy.DisposeOnLifecycleDestroyed
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment
import androidx.fragment.compose.AndroidFragment
import androidx.navigation.fragment.navArgs
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.data.RELEASED_YEAR
import com.stt.android.home.diary.diarycalendar.DiaryCalendarConstants.DIARY_CALENDAR_DISPLAY_MODE
import com.stt.android.home.diary.diarycalendar.DiaryCalendarConstants.DIARY_CALENDAR_DISPLAY_MODE_CALENDAR
import com.stt.android.home.diary.diarycalendar.DiaryCalendarConstants.DIARY_CALENDAR_DISPLAY_MODE_MAP
import com.stt.android.home.diary.diarycalendar.DiaryCalendarConstants.DIARY_CALENDAR_SCROLL_TO_ACTIVITIES_LIST
import com.stt.android.home.diary.diarycalendar.DiaryCalendarPagerNavigation
import com.stt.android.home.diary.diarycalendar.DiaryCalendarPagerNavigationImpl
import com.stt.android.home.diary.diarycalendar.DiaryCalendarPagerNavigationOwner
import com.stt.android.home.diary.diarycalendar.nextYearDecember
import com.stt.android.home.diary.diarycalendar.parseMonth
import com.stt.android.home.diary.diarycalendar.sendDiaryCalendarGranularityAnalytics
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.time.YearMonth
import java.time.temporal.ChronoUnit
import javax.inject.Inject

@AndroidEntryPoint
class DiaryCalendarMonthPagerFragment : Fragment(), DiaryCalendarPagerNavigationOwner {

    @Inject
    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    private val args: DiaryCalendarMonthPagerFragmentArgs by navArgs()

    override val pagerNavigation: DiaryCalendarPagerNavigation = DiaryCalendarPagerNavigationImpl()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(inflater.context).apply {
        setViewCompositionStrategy(DisposeOnLifecycleDestroyed(viewLifecycleOwner))

        val displayMode = arguments?.getInt(
            DIARY_CALENDAR_DISPLAY_MODE,
            DIARY_CALENDAR_DISPLAY_MODE_CALENDAR,
        ) ?: DIARY_CALENDAR_DISPLAY_MODE_CALENDAR
        val (maxMonth, maxPages) = if (displayMode != DIARY_CALENDAR_DISPLAY_MODE_MAP) {
            val nextYearDecember = nextYearDecember()
            nextYearDecember to ChronoUnit.MONTHS.between(
                YearMonth.of(RELEASED_YEAR, 1),
                nextYearDecember.plusMonths(1), // Include the next year December
            ).toInt()
        } else {
            val currentMonth = YearMonth.now()
            currentMonth to ChronoUnit.MONTHS.between(
                YearMonth.of(RELEASED_YEAR, 1),
                currentMonth.plusMonths(1),
            ).toInt()
        }

        fun calcMonthIndex(month: YearMonth): Int {
            val monthsBetween = ChronoUnit.MONTHS.between(month, maxMonth).coerceAtLeast(0L)
            return maxPages - 1 - monthsBetween.toInt()
        }

        // Set initial index based on the number of months between initial month and the
        // next year December
        val currentMonthIndex = calcMonthIndex(getInitialMonth())

        pagerNavigation.setDefaultPage(calcMonthIndex(YearMonth.now()), maxPages)

        setContentWithM3Theme {
            val pagerState = rememberPagerState(initialPage = currentMonthIndex) { maxPages }

            val targetPage by pagerNavigation.currentPageIndex.collectAsState()

            HorizontalPager(
                state = pagerState,
                userScrollEnabled = displayMode != DIARY_CALENDAR_DISPLAY_MODE_MAP,
                beyondViewportPageCount = 1,
            ) { page ->
                val currentMonth = maxMonth.minusMonths(maxPages - page - 1L)
                AndroidFragment<DiaryCalendarMonthFragment>(
                    modifier = Modifier.fillMaxSize(),
                    arguments = bundleOf(
                        DiaryCalendarMonthFragment.ARG_MONTH to currentMonth,
                        DIARY_CALENDAR_SCROLL_TO_ACTIVITIES_LIST to args.showActivitiesList,
                        DIARY_CALENDAR_DISPLAY_MODE to displayMode,
                    ),
                )
            }

            val coroutineScope = rememberCoroutineScope()
            LaunchedEffect(targetPage) {
                if (pagerState.currentPage != targetPage) {
                    coroutineScope.launch {
                        pagerState.animateScrollToPage(targetPage)
                    }
                }
            }

            var firstTime by remember { mutableStateOf(true) }
            DisposableEffect(pagerState.currentPage) {
                pagerNavigation.updateCurrentPage(pagerState.currentPage)
                if (!firstTime) {
                    sendDiaryCalendarGranularityAnalytics(
                        amplitudeAnalyticsTracker,
                        AnalyticsPropertyValue.DiaryCalendarGranularity.MONTHLY
                    )
                } else {
                    firstTime = false
                }
                onDispose {}
            }
        }
    }

    private fun getInitialMonth(): YearMonth {
        args.month?.run {
            return parseMonth(this)
        }

        // If initial month not given, show current month
        return YearMonth.now()
    }

    companion object {
        const val ARG_MONTH = "month"
    }
}
