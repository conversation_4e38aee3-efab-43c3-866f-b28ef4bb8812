package com.stt.android.home.dashboardv2

import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.workoutUpdated
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dashboard.card.ExploreCardLoader
import com.stt.android.home.dashboard.card.SportieCardLoader
import com.stt.android.home.dashboard.card.WorkoutCardLoader
import com.stt.android.home.dashboardv2.usecase.GenerateShareWorkoutUseCase
import com.stt.android.home.dashboardv2.usecase.ReactToWorkoutUseCase
import com.stt.android.newfeed.ExploreCardData
import com.stt.android.newfeed.FeedCardData
import com.stt.android.newfeed.FilterTag
import com.stt.android.newfeed.SportieCardData
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.newfeed.getFilterTagByCode
import com.stt.android.refreshable.Refreshables
import com.stt.android.ui.components.workout.WorkoutShareInfo
import com.stt.android.utils.STTConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class ActivitiesTabViewModel @Inject constructor(
    workoutHeaderController: WorkoutHeaderController,
    private val refreshables: Refreshables,
    private val reactToWorkoutUseCase: ReactToWorkoutUseCase,
    private val generateShareWorkoutUseCase: GenerateShareWorkoutUseCase,
    private val workoutCardLoader: WorkoutCardLoader,
    private val sportieCardLoader: SportieCardLoader,
    private val exploreCardLoader: ExploreCardLoader,
    private val sharedPreferences: SharedPreferences,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    sealed class ShareWorkout(open val workoutHeader: WorkoutHeader) {
        // TODO Figure out what the name means.
        data class MultipleWorkout(
            override val workoutHeader: WorkoutHeader,
            val imageIndex: Int,
            val watchName: String,
        ) : ShareWorkout(workoutHeader)

        data class Workout(
            override val workoutHeader: WorkoutHeader,
            val imageIndex: Int,
        ) : ShareWorkout(workoutHeader)

        data class Link(
            override val workoutHeader: WorkoutHeader,
        ) : ShareWorkout(workoutHeader)

        data class Error(
            override val workoutHeader: WorkoutHeader,
        ) : ShareWorkout(workoutHeader)
    }

    sealed class ViewData(open val selectedFilterTag: FilterTag) {
        data class Loading(
            override val selectedFilterTag: FilterTag,
            val showLoadingSpinner: Boolean,
        ) : ViewData(selectedFilterTag)

        data class Activities(
            override val selectedFilterTag: FilterTag,
            val cards: List<FeedCardData>,
            val shouldScrollToTop: Boolean,
        ) : ViewData(selectedFilterTag)

        data class NoActivity(
            override val selectedFilterTag: FilterTag,
        ) : ViewData(selectedFilterTag)

        data object NoFollowing : ViewData(FilterTag.FOLLOWING)

        data class Error(
            override val selectedFilterTag: FilterTag,
        ) : ViewData(selectedFilterTag)
    }

    private var selectedFilterTag: FilterTag
        get() {
            val filterCode = sharedPreferences
                .getInt(STTConstants.DefaultPreferences.KEY_LAST_FILTER_TAG, FilterTag.ALL.filterCode)
            return getFilterTagByCode(filterCode)
        }
        set(value) {
            sharedPreferences.edit {
                putInt(STTConstants.DefaultPreferences.KEY_LAST_FILTER_TAG, value.filterCode)
            }
        }

    private val _shareWorkout: MutableSharedFlow<ShareWorkout> = MutableSharedFlow()
    val shareWorkout: SharedFlow<ShareWorkout> = _shareWorkout.asSharedFlow()

    private val _viewState: MutableStateFlow<ViewData> = MutableStateFlow(
        ViewData.Loading(
            selectedFilterTag = selectedFilterTag,
            showLoadingSpinner = false,
        )
    )
    val viewState: StateFlow<ViewData> = _viewState.asStateFlow()

    private var loadWorkoutsJob: Job? = null

    init {
        workoutHeaderController.workoutUpdated
            .onEach { load() }
            .launchIn(viewModelScope)

        load()
    }

    fun load() {
        loadWorkoutsJob?.cancel()
        loadWorkoutsJob = viewModelScope.launch(coroutinesDispatchers.io) {
            val hasDataLoaded = (_viewState.value as? ViewData.Activities)
                ?.selectedFilterTag == selectedFilterTag
            if (!hasDataLoaded) {
                _viewState.value = ViewData.Loading(
                    selectedFilterTag = selectedFilterTag,
                    showLoadingSpinner = refreshables.isRefreshing.value.not(),
                )
            }

            val myFirstWorkoutStartTime =
                ((_viewState.value as? ViewData.Activities)?.cards?.firstOrNull() as? WorkoutCardInfo)
                    ?.workoutHeader
                    ?.startTime
                    ?.takeIf { selectedFilterTag == FilterTag.Me }
                    ?: 0L

            _viewState.value = runSuspendCatching {
                // Let's load workout cards, Sportie card, and explore card (local cache only) together,
                // otherwise the list would flash if Sportie or explore card is loaded later.
                val loadSportieCardAsync = async { loadSportieCard() }
                val loadExploreCardAsync = async { loadExploreCard(localCacheOnly = true) }
                val workoutCards = loadWorkoutCards()
                val sportieCard = loadSportieCardAsync.await()
                val exploreCard = loadExploreCardAsync.await()

                when {
                    workoutCards.isNotEmpty() -> {
                        val cards = buildList {
                            addAll(workoutCards)
                            sportieCard?.let { add(SPORTIE_CARD_POSITION.coerceAtMost(size), it) }
                            exploreCard?.let { add(EXPLORE_CARD_POSITION.coerceAtMost(size), it) }
                        }

                        val newMyFirstWorkoutStartTime =
                            (cards.firstOrNull() as? WorkoutCardInfo)
                                ?.workoutHeader
                                ?.startTime
                                ?.takeIf { selectedFilterTag == FilterTag.Me }
                                ?: 0L
                        val shouldScrollToTop = newMyFirstWorkoutStartTime > myFirstWorkoutStartTime

                        ViewData.Activities(
                            selectedFilterTag = selectedFilterTag,
                            cards = cards,
                            shouldScrollToTop = shouldScrollToTop,
                        )
                    }

                    selectedFilterTag == FilterTag.FOLLOWING -> ViewData.NoFollowing

                    else -> ViewData.NoActivity(
                        selectedFilterTag = selectedFilterTag,
                    )
                }
            }.getOrElse { e ->
                Timber.w(e, "Error while loading activities")

                ViewData.Error(
                    selectedFilterTag = selectedFilterTag,
                )
            }

            // We should load Explore card together with others to avoid the list from flashing.
            // However, this might trigger remote calls, so do it later after others are loaded.
            loadExploreCard(localCacheOnly = false)?.let { exploreCard ->
                _viewState.update { current ->
                    (current as? ViewData.Activities)
                        ?.copy(
                            cards = buildList {
                                current.cards
                                    .forEach { card ->
                                        if (card !is ExploreCardData) {
                                            add(card)
                                        }
                                    }
                                add(EXPLORE_CARD_POSITION.coerceAtMost(size), exploreCard)
                            },
                        )
                        ?: current
                }
            }
        }
    }

    private suspend fun loadWorkoutCards(): List<WorkoutCardInfo> = withContext(coroutinesDispatchers.io) {
        when (_viewState.value.selectedFilterTag) {
            FilterTag.ALL -> workoutCardLoader.loadOwnWorkouts(WORKOUTS_TO_LOAD) + workoutCardLoader.loadFolloweeWorkouts(WORKOUTS_TO_LOAD)
            FilterTag.Me -> workoutCardLoader.loadOwnWorkouts(WORKOUTS_TO_LOAD)
            FilterTag.FOLLOWING -> workoutCardLoader.loadFolloweeWorkouts(WORKOUTS_TO_LOAD)
            FilterTag.SUUNTO -> emptyList()
        }.sortedByDescending { workoutCardInfo ->
            workoutCardInfo.workoutHeader.stopTime
        }
    }

    private suspend fun loadSportieCard(): SportieCardData? = withContext(coroutinesDispatchers.io) {
        runSuspendCatching {
            when (_viewState.value.selectedFilterTag) {
                FilterTag.ALL,
                FilterTag.Me -> sportieCardLoader.loadSportieCard()
                FilterTag.FOLLOWING,
                FilterTag.SUUNTO -> null
            }
        }.getOrElse { e ->
            Timber.w(e, "Error while loading Sportie card")
            null
        }
    }

    private suspend fun loadExploreCard(localCacheOnly: Boolean): ExploreCardData? = withContext(coroutinesDispatchers.io) {
        runSuspendCatching {
            when (_viewState.value.selectedFilterTag) {
                FilterTag.ALL -> exploreCardLoader.loadExploreCard(localCacheOnly)
                FilterTag.Me,
                FilterTag.FOLLOWING,
                FilterTag.SUUNTO -> null
            }
        }.getOrElse { e ->
            Timber.w(e, "Error while loading explore card")
            null
        }
    }

    fun selectFilterTag(toSelect: FilterTag) {
        selectedFilterTag = toSelect

        load()
    }

    fun reactToWorkout(workoutHeader: WorkoutHeader) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            reactToWorkoutUseCase(workoutHeader)
        }
    }

    fun shareWorkout(workoutHeader: WorkoutHeader, workoutShareInfo: WorkoutShareInfo) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            _shareWorkout.emit(
                generateShareWorkoutUseCase.generate(workoutHeader, workoutShareInfo)
            )
        }
    }

    fun onScrolledToTop() {
        viewModelScope.launch {
            _viewState.update { current ->
                (current as? ViewData.Activities)?.copy(
                    shouldScrollToTop = false,
                ) ?: current
            }
        }
    }

    private companion object {
        const val WORKOUTS_TO_LOAD = 50L
        const val SPORTIE_CARD_POSITION = 2
        const val EXPLORE_CARD_POSITION = 7
    }
}
