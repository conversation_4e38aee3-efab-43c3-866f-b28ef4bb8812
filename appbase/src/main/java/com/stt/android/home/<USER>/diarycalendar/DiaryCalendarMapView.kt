package com.stt.android.home.diary.diarycalendar

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ColorFilter
import android.graphics.Path
import android.graphics.PixelFormat
import android.graphics.PorterDuff
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.widget.Button
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.graphics.applyCanvas
import androidx.core.graphics.createBitmap
import androidx.core.graphics.withClip
import androidx.core.util.TypedValueCompat
import androidx.core.view.isVisible
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.coroutineScope
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.R
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.domain.diarycalendar.LocationWithActivityType
import com.stt.android.domain.user.HeatmapType
import com.stt.android.home.diary.diarycalendar.bubbles.DiaryBubbleData
import com.stt.android.home.mytracks.MyTracksUtils
import com.stt.android.maps.MapType
import com.stt.android.maps.MapTypeHelper
import com.stt.android.maps.SuuntoBitmapDescriptorFactory
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapView
import com.stt.android.maps.SuuntoMaps
import com.stt.android.maps.SuuntoTileOverlay
import com.stt.android.maps.mapbox.MapboxMapsProvider
import com.stt.android.maps.newLatLngBounds
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.map.MapHelper
import com.stt.android.ui.map.selection.MyTracksGranularity
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.ZoneOffset

class DiaryCalendarMapView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr), DefaultLifecycleObserver {

    var locations: List<LocationWithActivityType> = emptyList()

    var routes: List<RouteAndActivityType> = emptyList()

    var bounds: LatLngBounds? = null

    var granularity: DiaryCalendarListContainer.Granularity? = null

    var mapSelectionModel: MapSelectionModel? = null

    var bubbleData: List<DiaryBubbleData>? = null

    var onMapClicked: OnClickListener? = null

    var roundedCorners: Boolean = true

    var gestureEnabled: Boolean = false

    lateinit var lifecycle: Lifecycle

    lateinit var myTracksUtils: MyTracksUtils

    lateinit var amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker

    private val bitmapDescriptorFactory = SuuntoBitmapDescriptorFactory(context)
    private var heatmapOverlay: SuuntoTileOverlay? = null
    private var map: SuuntoMap? = null
    private val mapView: SuuntoMapView by lazy {
        findViewById(R.id.diary_calendar_map)
    }

    private val mapTouchArea: Button by lazy {
        findViewById(R.id.diary_calendar_touch_area)
    }

    private val mapboxIcon: ImageView by lazy {
        findViewById(R.id.diary_calendar_mapbox_icon)
    }

    private val mapCredit: TextView by lazy {
        findViewById(R.id.diary_calendar_map_credit)
    }

    init {
        inflate(context, R.layout.modelview_diary_calendar_map_view, this)
    }

    fun onPropsSet() {
        foreground = if (roundedCorners) {
            MaskDrawable(context)
        } else null

        if (map == null) {
            mapView.setInitialMapTypeHint(getMapType().name)
            mapView.getMapAsync { map ->
                updateTileOverlays(map)
                <EMAIL> = map

                trackScrolledToMapAmplitudeEvent()
                updateMarkers()
                map.enableGestureIfNeeded()
            }
        } else {
            updateMarkers()
            map?.enableGestureIfNeeded()
        }

        if (onMapClicked != null) {
            mapTouchArea.isVisible = true
            mapTouchArea.setOnClickListener {
                updateMyTracksGranularity()
                onMapClicked?.onClick(this)
            }
        } else {
            mapTouchArea.isVisible = false
            mapTouchArea.setOnClickListener(null)
        }

        mapboxIcon.isVisible = SuuntoMaps.defaultProvider?.name == MapboxMapsProvider.NAME
        MapHelper.updateCreditText(getMapType(), mapCredit)
    }

    private fun getMapType(): MapType = mapSelectionModel
        ?.selectedMapTypeUserHasAccessTo
        ?: MapTypeHelper.DEFAULT_MAP_TYPE

    private fun getHeatmapType(): HeatmapType? = mapSelectionModel?.selectedHeatmapOrNullIfAccessDenied

    private fun trackScrolledToMapAmplitudeEvent() {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.DIARY_CALENDAR_TAB_SCROLLED_TO_WORKOUT_MAP,
            AnalyticsProperties()
                .put(
                    AnalyticsEventProperty.CALENDAR_GRANULARITY,
                    granularity?.analyticsPropertyValue
                )
                .put(AnalyticsEventProperty.MAP_TYPE, getMapType().name)
                .put(
                    AnalyticsEventProperty.MAP_HEATMAP_TYPE,
                    getHeatmapType()?.name ?: AnalyticsPropertyValue.MAP_NO_HEATMAP
                )
        )
    }

    private fun updateMyTracksGranularity() {
        val bubbleData = bubbleData ?: return
        val startDate = bubbleData.firstOrNull()?.startDate ?: return
        val endDate = bubbleData.lastOrNull()?.endDate ?: return
        val now = LocalDate.now()
        val areWeeks = bubbleData.first().bubbles.size == 7

        val isThisYear = bubbleData.size == 12 && now.year == endDate.year
        val isThisMonth = bubbleData.size == 1 && !areWeeks &&
            now.year == endDate.year && now.month == endDate.month
        val isThisWeek = bubbleData.size == 1 && areWeeks &&
            (now.isEqual(startDate) || now.isAfter(startDate)) &&
            now.isEqual(endDate) || now.isBefore(endDate)

        mapSelectionModel?.selectedMyTracksGranularity = when {
            granularity == DiaryCalendarListContainer.Granularity.YEAR && isThisYear -> MyTracksGranularity(
                MyTracksGranularity.Type.THIS_YEAR
            )

            granularity == DiaryCalendarListContainer.Granularity.MONTH && isThisMonth -> MyTracksGranularity(
                MyTracksGranularity.Type.THIS_MONTH
            )

            granularity == DiaryCalendarListContainer.Granularity.WEEK && isThisWeek -> MyTracksGranularity(
                MyTracksGranularity.Type.THIS_WEEK
            )

            granularity === DiaryCalendarListContainer.Granularity.LAST_30_DAYS -> MyTracksGranularity(
                MyTracksGranularity.Type.LAST_30_DAYS
            )

            else -> MyTracksGranularity(
                MyTracksGranularity.Type.CUSTOM_DATES,
                startDate.atStartOfDay(ZoneOffset.UTC) // MaterialDatePicker requires UTC
                    .toInstant()
                    .toEpochMilli(),
                endDate.atStartOfDay(ZoneOffset.UTC) // MaterialDatePicker requires UTC
                    .toInstant()
                    .toEpochMilli()
            )
        }
    }

    private fun updateMarkers() {
        val map = map ?: return
        lifecycle.coroutineScope.launch {
            myTracksUtils.removePolylines(map)
            myTracksUtils.removeMarkers(map)
            myTracksUtils.drawMyTracks(
                context,
                map,
                routes,
                simplifyRoutes = true
            )

            myTracksUtils.drawMyTracksMarkers(
                locations,
                map,
                bitmapDescriptorFactory
            )
        }
        zoomMap()
    }

    private fun zoomMap() {
        val map = map ?: return

        mapView.setOnMapLoadedCallback(object : SuuntoMap.OnMapLoadedCallback {
            override fun onMapLoaded() {
                if (mapView.width > 0 && mapView.height > 0) {
                    if (bounds != null) {
                        map.animateCamera(
                            newLatLngBounds(
                                bounds!!,
                                resources.getDimensionPixelOffset(R.dimen.size_spacing_xlarge)
                            )
                        )
                    } else if (locations.size == 1) {
                        MapHelper.moveCameraToLatLng(
                            map,
                            LatLng(locations[0].latitude, locations[0].longitude),
                            true
                        )
                    } else {
                        MapHelper.moveCameraToBoundLatLngs(
                            map,
                            mapView.width,
                            mapView.height,
                            locations.map { LatLng(it.latitude, it.longitude) },
                            resources.getDimensionPixelOffset(R.dimen.size_spacing_xlarge)
                        )

                        map.getCameraPosition()?.run {
                            // Don't zoom in too close if locations are close to one another
                            if (zoom > STTConstants.MapPreferences.DEFAULT_ZOOM_LEVEL) {
                                MapHelper.moveCameraToLatLng(map, target, false)
                            }
                        }
                    }
                }
            }
        })
    }

    private fun SuuntoMap.enableGestureIfNeeded() =
        getUiSettings().setAllGesturesEnabled(gestureEnabled)

    override fun onCreate(owner: LifecycleOwner) {
        // instance state is not saved, map is always initialized from scratch
        mapView.onCreate(savedInstanceState = null)
    }

    override fun onResume(owner: LifecycleOwner) {
        map?.let(::updateTileOverlays)
        mapView.onResume()
    }

    private fun updateTileOverlays(map: SuuntoMap) {
        MapHelper.updateMapType(map, getMapType(), null)
        heatmapOverlay?.remove()
        heatmapOverlay = getHeatmapType()?.let { heatmapType ->
            MapHelper.addHeatmapOverlay(map, heatmapType)
        }
    }

    override fun onPause(owner: LifecycleOwner) {
        mapView.onPause()
    }

    override fun onStart(owner: LifecycleOwner) {
        mapView.onStart()
    }

    override fun onStop(owner: LifecycleOwner) {
        mapView.onStop()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        mapView.onDestroy()
    }

    class MaskDrawable(private val context: Context) : Drawable() {
        private var mask: Bitmap? = null

        override fun draw(canvas: Canvas) {
            mask?.let { canvas.drawBitmap(it, 0f, 0f, null) }
        }

        override fun onBoundsChange(bounds: Rect) {
            super.onBoundsChange(bounds)
            mask = createBitmap(bounds.width(), bounds.height()).applyCanvas {
                val path = Path().apply {
                    val radius = TypedValueCompat.dpToPx(16f, context.resources.displayMetrics)
                    addRoundRect(
                        RectF(0f, 0f, bounds.right.toFloat(), bounds.bottom.toFloat()),
                        radius,
                        radius,
                        Path.Direction.CW,
                    )
                }
                drawColor(Color.WHITE)
                withClip(path) {
                    drawColor(Color.BLACK, PorterDuff.Mode.CLEAR)
                }
            }
        }

        override fun setAlpha(alpha: Int) {
        }

        override fun setColorFilter(colorFilter: ColorFilter?) {
        }

        @Deprecated("Deprecated in Java")
        override fun getOpacity(): Int {
            return PixelFormat.UNKNOWN
        }
    }
}
