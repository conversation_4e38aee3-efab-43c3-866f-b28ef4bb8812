package com.stt.android.di.maps

import com.stt.android.maps.MAP_TYPE_AVALANCHE
import com.stt.android.maps.MAP_TYPE_DARK
import com.stt.android.maps.MAP_TYPE_FINLAND_BELECTRO_RASTER_TERRAIN_CONTOUR
import com.stt.android.maps.MAP_TYPE_FINLAND_MAANMITTAUSLAITOS_TERRAIN
import com.stt.android.maps.MAP_TYPE_HYBRID
import com.stt.android.maps.MAP_TYPE_LIGHT
import com.stt.android.maps.MAP_TYPE_MAPBOX_LANDSCAPE_MAP
import com.stt.android.maps.MAP_TYPE_MAPBOX_NORWAY_KARTVERKET
import com.stt.android.maps.MAP_TYPE_MAPBOX_NORWAY_SEA_AND_LAKE
import com.stt.android.maps.MAP_TYPE_MAPBOX_OPEN_CYCLE_MAP
import com.stt.android.maps.MAP_TYPE_MAPBOX_OPEN_STREET_MAP
import com.stt.android.maps.MAP_TYPE_MAPBOX_OUTDOORS_MAP
import com.stt.android.maps.MAP_TYPE_MAPBOX_SPAIN_INSTITUTO_GEOGRAFICO
import com.stt.android.maps.MAP_TYPE_NORMAL
import com.stt.android.maps.MAP_TYPE_SATELLITE
import com.stt.android.maps.MAP_TYPE_SKI
import com.stt.android.maps.MAP_TYPE_SUUNTO_HILLSHADING
import com.stt.android.maps.MAP_TYPE_TERRAIN
import com.stt.android.maps.MapTypeHelper
import com.stt.android.maps.MapsProvider
import com.stt.android.maps.mapbox.MapboxMapsProvider
import com.stt.android.maps.mapbox.MapboxMapsProviderOptions
import com.stt.android.remote.di.BaseUrlConfiguration
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.multibindings.IntoSet

@Module
abstract class MapboxMapsAbstractionModule {
    @Binds
    @IntoSet
    abstract fun bindMapboxMapsProvider(mapboxMapsProvider: MapboxMapsProvider): MapsProvider

    companion object {
        @Provides
        fun provideMapboxMapsOptions(
            baseUrlConfiguration: BaseUrlConfiguration,
        ): MapboxMapsProviderOptions {
            val styleMap = mapOf(
                createMapTypeToStyleUrlMap(MAP_TYPE_NORMAL, baseUrlConfiguration, "suunto-offroad"),
                createMapTypeToStyleUrlMap(MAP_TYPE_TERRAIN, baseUrlConfiguration, "suunto-offroad"),
                createMapTypeToStyleUrlMap(MAP_TYPE_SATELLITE, baseUrlConfiguration, "suunto-satellite"),
                createMapTypeToStyleUrlMap(MAP_TYPE_HYBRID, baseUrlConfiguration, "suunto-satellite"), // Satellite used on purpose
                createMapTypeToStyleUrlMap(MAP_TYPE_DARK, baseUrlConfiguration, "suunto-dark"),
                createMapTypeToStyleUrlMap(MAP_TYPE_LIGHT, baseUrlConfiguration, "suunto-light"),
                createMapTypeToStyleUrlMap(MAP_TYPE_SKI, baseUrlConfiguration, "suunto-ski"),
                createMapTypeToStyleUrlMap(MAP_TYPE_FINLAND_MAANMITTAUSLAITOS_TERRAIN, baseUrlConfiguration, "mll-with-outdoor"),
                createMapTypeToStyleUrlMap(MAP_TYPE_AVALANCHE, baseUrlConfiguration, "suunto-avalanche"),
                createMapTypeToStyleUrlMap(MAP_TYPE_MAPBOX_OPEN_STREET_MAP, baseUrlConfiguration, "open-street-map-normal"),
                createMapTypeToStyleUrlMap(MAP_TYPE_MAPBOX_OPEN_CYCLE_MAP, baseUrlConfiguration, "open-cycle-map-cycling"),
                createMapTypeToStyleUrlMap(MAP_TYPE_MAPBOX_OUTDOORS_MAP, baseUrlConfiguration, "open-street-map-outdoors"),
                createMapTypeToStyleUrlMap(MAP_TYPE_MAPBOX_LANDSCAPE_MAP, baseUrlConfiguration, "open-street-map-landscape"),
                createMapTypeToStyleUrlMap(MAP_TYPE_MAPBOX_NORWAY_KARTVERKET, baseUrlConfiguration, "norway-kartverket-terrain-with-outdoor"),
                createMapTypeToStyleUrlMap(MAP_TYPE_MAPBOX_NORWAY_SEA_AND_LAKE, baseUrlConfiguration, "norway-kartverket-sea-and-lake-with-outdoor"),
                createMapTypeToStyleUrlMap(MAP_TYPE_MAPBOX_SPAIN_INSTITUTO_GEOGRAFICO, baseUrlConfiguration, "spain-instituto-geografico-terrain"),
                createMapTypeToStyleUrlMap(MAP_TYPE_FINLAND_BELECTRO_RASTER_TERRAIN_CONTOUR, baseUrlConfiguration, "finland-belectro-raster-terrain"),
                createMapTypeToStyleUrlMap(MAP_TYPE_SUUNTO_HILLSHADING, baseUrlConfiguration, "suunto-hillshading"),
            )

            return MapboxMapsProviderOptions(
                customStyleUrls = styleMap,
                cacheSizeBytes = 100000000L // 100 MB
            )
        }

        private fun createMapTypeToStyleUrlMap(
            mapType: String,
            baseUrlConfiguration: BaseUrlConfiguration,
            styleName: String,
        ): Pair<String, String> {
            // We prefer to use style URL read from remote for dynamic map types, and eventually should stop using hard-coded ones.
            val styleUrl = MapTypeHelper.find(mapType)
                ?.styleUrl
                ?.takeUnless(String::isEmpty)
                ?: "${baseUrlConfiguration.assetsUrl}mobilemapstyles/stylesheet/$styleName/$styleName.json"

            return mapType to styleUrl
        }
    }
}
