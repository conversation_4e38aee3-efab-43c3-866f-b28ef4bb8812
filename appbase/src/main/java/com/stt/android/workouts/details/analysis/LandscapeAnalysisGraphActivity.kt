package com.stt.android.workouts.details.analysis

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import androidx.activity.viewModels
import androidx.core.content.IntentCompat
import androidx.lifecycle.lifecycleScope
import com.github.mikephil.charting.utils.Utils
import com.stt.android.R
import com.stt.android.common.ui.ViewModelActivity2
import com.stt.android.controllers.UserSettingsController
import com.stt.android.core.domain.GraphType
import com.stt.android.databinding.ActivityLandscapeAnalysisGraphBinding
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.workout.WorkoutHrEvent
import com.stt.android.domain.workout.filterEventsAndHrEvents
import com.stt.android.domain.workout.filterPausesAndAdjustMillisecondsInWorkout
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.toDomainWindow
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.fragments.workout.analysis.WorkoutAnalysisHelper
import com.stt.android.utils.RxUtils
import com.stt.android.window.setFlagsAndColors
import com.stt.android.workouts.details.values.isSuuntoRun
import com.suunto.algorithms.data.HeartRate.Companion.hz
import dagger.hilt.android.AndroidEntryPoint
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.rxkotlin.plusAssign
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.roundToInt

/**
 * Used only for dives, for other workout types use FullscreenGraphAnalysisActivity
 */
@AndroidEntryPoint
class LandscapeAnalysisGraphActivity : ViewModelActivity2() {

    @Inject
    lateinit var userSettingsController: UserSettingsController

    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    @Inject
    lateinit var workoutAnalysisHelper: WorkoutAnalysisHelper

    private val compositeDisposable = CompositeDisposable()

    override val viewModel: LandscapeAnalysisGraphViewModel by viewModels()

    private val viewDataBinding: ActivityLandscapeAnalysisGraphBinding get() = requireBinding()

    override fun getLayoutResId() = R.layout.activity_landscape_analysis_graph

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setSupportActionBar(viewDataBinding.toolbar)
        supportActionBar?.apply {
            setDisplayShowTitleEnabled(false)
            setDisplayHomeAsUpEnabled(true)
        }

        window.setFlagsAndColors(
            showStatusBar = false,
            showNavigationBar = false,
            layoutUnderStatusBar = false
        )

        val largeGraphView = viewDataBinding.largeGraphView
        largeGraphView.setInfoModelFormatter(infoModelFormatter)
        val graphType = IntentCompat.getParcelableExtra(intent, GRAPH_TYPE, GraphType::class.java)
            ?: throw IllegalArgumentException("Missing GraphType")
        val workoutHeader = IntentCompat.getParcelableExtra(intent, WORKOUT_HEADER, WorkoutHeader::class.java)
        val multisportPartActivity = IntentCompat.getParcelableExtra(intent, MULTISPORT_PART_ACTIVITY, MultisportPartActivity::class.java)
        val measurementUnit = userSettingsController.settings.measurementUnit
        viewDataBinding.analysisTitle.text = WorkoutAnalysisHelper.getGraphNameTitle(this, graphType)
        // Setting extra offsets instead of defining margins in the layout gives the chart room to
        // draw icons fully at start and end points
        viewDataBinding.largeGraphView.setExtraOffsets(
            Utils.convertPixelsToDp(resources.getDimension(R.dimen.size_spacing_medium)),
            Utils.convertPixelsToDp(resources.getDimension(R.dimen.size_spacing_small)),
            Utils.convertPixelsToDp(resources.getDimension(R.dimen.size_spacing_medium)),
            Utils.convertPixelsToDp(resources.getDimension(R.dimen.size_spacing_small))
        )

        if (workoutHeader == null) {
            return
        }
        if (workoutHeader.activityType.isDiving &&
            graphType != GraphType.Summary(SummaryGraph.HEARTRATE) &&
            graphType != GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS)
        ) {
            lifecycleScope.launch {
                val (diveExtension, sml) = viewModel.loadDiveExtensionData(workoutHeader)
                viewDataBinding.avgInfo.text = workoutAnalysisHelper.getGraphNameSubtitle(
                    this@LandscapeAnalysisGraphActivity,
                    graphType,
                    workoutHeader,
                    diveExtension,
                    sml,
                    sml.getActivityWindow(multisportPartActivity)?.toDomainWindow()
                )
                compositeDisposable += largeGraphView.drawDiveGraph(
                    graphType,
                    diveExtension,
                    sml,
                    measurementUnit,
                    graphType == GraphType.Summary(SummaryGraph.DEPTH)
                ).subscribeBy(onError = { e -> Timber.w(e, "Failed to draw graph") })
            }
        } else {
            lifecycleScope.launch {
                val (workoutData, sml) = viewModel.loadWorkoutData(workoutHeader)
                viewDataBinding.avgInfo.text = workoutAnalysisHelper.getGraphNameSubtitle(
                    this@LandscapeAnalysisGraphActivity,
                    graphType,
                    workoutHeader,
                    null,
                    sml,
                    sml.getActivityWindow(multisportPartActivity)?.toDomainWindow()
                )
                compositeDisposable += when (graphType) {
                    GraphType.Summary(SummaryGraph.HEARTRATE) -> {
                        val (events, hrEvents) = workoutData.filterEventsAndHrEvents(multisportPartActivity)
                        largeGraphView
                            .drawHeartRateGraph(
                                hrEvents = filterPausesAndAdjustMillisecondsInWorkout(
                                    events,
                                    hrEvents,
                                    multisportPartActivity?.startTime
                                ),
                                multisportPartActivity = multisportPartActivity
                            )
                            .subscribe(
                                { RxUtils.actionNone() },
                                { e -> Timber.w(e, "Failed to draw graph") }
                            )
                    }
                    GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS) -> {
                        largeGraphView
                            .drawHeartRateGraph(
                                hrEvents = sml.streamData.recoveryHeartInThreeMins.map { streamPoint ->
                                    WorkoutHrEvent(
                                        streamPoint.timestamp,
                                        streamPoint.value.hz.inBpm.roundToInt(),
                                        streamPoint.timestamp,
                                    )
                                },
                                multisportPartActivity = multisportPartActivity
                            )
                            .subscribe(
                                { RxUtils.actionNone() },
                                { e -> Timber.w(e, "Failed to draw graph") }
                            )
                    }
                    else -> {
                        largeGraphView.drawGraph(
                            viewModel.loadSummaryExtensionData(workoutHeader).isSuuntoRun(),
                            graphType,
                            workoutData.routePoints,
                            workoutHeader,
                            sml,
                            infoModelFormatter.unit,
                            sml.getActivityWindow(multisportPartActivity),
                            multisportPartActivity
                        ).subscribeBy(onError = { e -> Timber.w(e, "Failed to draw graph") })
                    }
                }
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressedDispatcher.onBackPressed()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onDestroy() {
        compositeDisposable.clear()
        super.onDestroy()
    }

    companion object {
        private const val GRAPH_TYPE = "graphType"
        private const val WORKOUT_HEADER = "workoutHeader"
        private const val MULTISPORT_PART_ACTIVITY = "multisportPartActivity"

        fun newStartIntent(
            context: Context,
            workoutHeader: WorkoutHeader,
            graphType: GraphType
        ): Intent {
            return Intent(context, LandscapeAnalysisGraphActivity::class.java).apply {
                putExtra(GRAPH_TYPE, graphType)
                putExtra(WORKOUT_HEADER, workoutHeader)
            }
        }
    }
}
