package com.stt.android.data.workout.tss

import androidx.annotation.StringRes
import com.soy.algorithms.tss.TSSCalculationMethod
import com.stt.android.R
import com.stt.android.domain.user.workout.tss.toDomain
import com.stt.android.domain.workouts.tss.TSS
import com.soy.algorithms.tss.TSS as AlgTSS

fun AlgTSS.toDomain(): TSS = TSS(
    trainingStressScore = trainingStressScore,
    calculationMethod = calculationMethod.toDomain(),
    intensityFactor = intensityFactor,
    normalizedPower = normalizedPower,
    averageGradeAdjustedPace = averageGradeAdjustedPace
)

@StringRes
fun TSSCalculationMethod.getNameStringResId(callManualTSS: Boolean): Int = when (this) {
    TSSCalculationMethod.POWER -> R.string.workout_values_headline_tss_power
    TSSCalculationMethod.PACE -> R.string.workout_values_headline_tss_pace
    TSSCalculationMethod.HR -> R.string.workout_values_headline_tss_hr
    TSSCalculationMethod.SWIM_PACE -> R.string.workout_values_headline_tss_swim_pace
    TSSCalculationMethod.MET -> R.string.workout_values_headline_tss_met
    TSSCalculationMethod.MANUAL ->
        if (callManualTSS) {
            R.string.workout_values_headline_tss
        } else {
            R.string.tss_calculation_method_selector_manual
        }
    TSSCalculationMethod.DYNAMIC_DFA -> R.string.workout_values_headline_tss_zonesense
}

fun TSSCalculationMethod.getAnalyticsName(): String = when (this) {
    TSSCalculationMethod.POWER -> "Power"
    TSSCalculationMethod.PACE -> "Pace"
    TSSCalculationMethod.HR -> "HR"
    TSSCalculationMethod.SWIM_PACE -> "Swimming"
    TSSCalculationMethod.MET -> "MET"
    TSSCalculationMethod.MANUAL -> "Manual"
    TSSCalculationMethod.DYNAMIC_DFA -> "ZoneSense"
}
