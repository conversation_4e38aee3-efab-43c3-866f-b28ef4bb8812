package com.stt.android.domain.user;

import android.content.Context;
import android.net.Uri;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.j256.ormlite.field.DataType;
import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;
import com.stt.android.domain.Point;
import com.stt.android.domain.review.ReviewState;
import com.stt.android.domain.workouts.pictures.Picture;
import com.stt.android.network.interfaces.ANetworkProvider;
import com.stt.android.utils.FileUtils;
import com.stt.android.utils.STTConstants;
import java.io.FileNotFoundException;
import java.lang.reflect.Type;
import java.util.Locale;
import java.util.Objects;
import java.util.Random;
import timber.log.Timber;

/**
 * Holds basic information of a picture taken during a workout. Used in local DB
 * to keep track of pictures taken locally. Also used when fetching information
 * from backend (Although in that case is not stored in DB)
 */
@DatabaseTable(tableName = ImageInformation.TABLE_NAME)
public class ImageInformation implements Parcelable {
    public static final String TABLE_NAME = "imageinformation";

    public static final Parcelable.Creator<ImageInformation> CREATOR =
        new Creator<ImageInformation>() {
            @Override
            public ImageInformation[] newArray(int size) {
                return new ImageInformation[size];
            }

            @Override
            public ImageInformation createFromParcel(Parcel source) {
                int id = source.readInt();
                String key = source.readString();
                ClassLoader classLoader = ImageInformation.class.getClassLoader();
                Point location = source.readParcelable(classLoader);
                long timestamp = source.readLong();
                double totalTime = source.readDouble();
                String fileName = source.readString();
                Integer workoutId = (Integer) source.readValue(classLoader);
                String workoutKey = source.readString();
                String md5Hash = source.readString();
                boolean locallyChanged = (Boolean) source.readValue(classLoader);
                String description = source.readString();
                String username = source.readString();
                int width = source.readInt();
                int height = source.readInt();
                ReviewState reviewState = source.readParcelable(ReviewState.class.getClassLoader());
                return new ImageInformation(id, key, location, timestamp, totalTime, fileName,
                    workoutId, workoutKey, md5Hash, description, locallyChanged, username, width,
                    height, 0, reviewState);
            }
        };
    @DatabaseField(id = true, columnName = DbFields.ID)
    private final int id;

    @DatabaseField(columnName = DbFields.KEY)
    private final String key;

    @DatabaseField(dataType = DataType.SERIALIZABLE, canBeNull = true, columnName = DbFields
        .LOCATION)
    private final Point location;

    @DatabaseField(columnName = DbFields.TIMESTAMP)
    private final long timestamp;

    @DatabaseField(columnName = DbFields.TOTAL_TIME)
    private final double totalTime;

    @DatabaseField(columnName = DbFields.FILE_NAME)
    private final String fileName;

    @DatabaseField(columnName = DbFields.WORKOUT_ID)
    private final Integer workoutId;

    @DatabaseField(canBeNull = true, columnName = DbFields.WORKOUT_KEY)
    private final String workoutKey;

    @DatabaseField(columnName = DbFields.MD5_HASH)
    private final String md5Hash;

    @DatabaseField(columnName = DbFields.LOCALLY_CHANGED)
    private final boolean locallyChanged;

    @DatabaseField(columnName = DbFields.DESCRIPTION)
    private final String description;

    @DatabaseField(columnName = DbFields.USER_NAME)
    private final String username;

    @DatabaseField(columnName = DbFields.WIDTH)
    private final int width;

    @DatabaseField(columnName = DbFields.HEIGHT)
    private final int height;

    @DatabaseField(columnName = DbFields.REVIEW_STATE, dataType = DataType.ENUM_STRING)
    private final ReviewState reviewState;

    private final int indexInWorkoutHeader;

    // Actually is used by OrmLite
    @SuppressWarnings("unused")
    private ImageInformation() {
        this(0, null, null, 0, 0, null, null, null, null, null, false, null, 0, 0, 0, ReviewState.PASS);
    }

    /**
     * Constructor used when picture is still local.
     */
    public ImageInformation(Point location, long timestamp, double totalTime, String fileName,
        String md5Hash, String username, int width, int height) {
        this(generateId(), null, location, timestamp, totalTime, fileName,
            null, null, md5Hash, null, true, username, width, height, 0, ReviewState.PASS);
    }

    @SuppressWarnings("WeakerAccess")
    ImageInformation(int id, String key, Point location, long timestamp, double totalTime,
        String fileName, Integer workoutId, String workoutKey, String md5Hash, String description,
        boolean locallyChanged, String username, int width, int height, int indexInWorkoutHeader, ReviewState reviewState) {
        this.id = id;
        this.key = key;
        this.location = location;
        this.timestamp = timestamp;
        this.totalTime = totalTime;
        this.fileName = fileName;
        this.workoutId = workoutId;
        this.workoutKey = workoutKey;
        this.md5Hash = md5Hash;
        this.description = description;
        this.locallyChanged = locallyChanged;
        this.username = username;
        this.width = width;
        this.height = height;
        this.indexInWorkoutHeader = indexInWorkoutHeader;
        this.reviewState = reviewState;
    }

    public ImageInformation(Point location, long timestamp, double totalTime, String fileName,
        String md5Hash, Integer workoutId, String workoutKey, String username, int width,
        int height) {
        this(generateId(), null, location, timestamp, totalTime, fileName,
            workoutId, workoutKey, md5Hash, null, true, username, width, height, 0, ReviewState.PASS);
    }

    public ImageInformation(Point location, long timestamp, double totalTime, String fileName,
        String md5Hash, Integer workoutId, String workoutKey,boolean locallyChanged, String username, int width,
        int height) {
        this(generateId(), null, location, timestamp, totalTime, fileName,
            workoutId, workoutKey, md5Hash, null, locallyChanged, username, width, height, 0, ReviewState.PASS);
    }

    // Generate a random ID if we don't have an ID already.
    // This may theoretically cause conflicts since the ID space is only 32 bits. At least it is
    // much more unique than getting hash of current timestamp like this used to work before.
    // TODO: use a proper ID (e.g. UUID) when migrating to Room
    private static int generateId() {
        return new Random().nextInt();
    }

    public ImageInformation linkWithWorkout(int workoutId) {
        return new ImageInformation(id, key, location, timestamp, totalTime, fileName, workoutId,
            workoutKey, md5Hash, description, locallyChanged, username, width, height,
            indexInWorkoutHeader, reviewState);
    }

    @NonNull
    public Uri getFeedUri(@NonNull Context context) {
        try {
            if (TextUtils.isEmpty(key)) {
                return localFileUri(context);
            }
            if (width > height) {
                // landscape picture, load at 720p resolution
                return getMediumResUri(context);

            } else {
                // portrait picture, load at 1080p resolution
                return getHighResUri(context);
            }
        } catch (Exception e) {
            Timber.w(e, "Error with getFeedUri");
            return Uri.EMPTY;
        }
    }

    @SuppressWarnings("SuspiciousNameCombination")
    @NonNull
    public Uri getThumbnailUri(@NonNull Context context) {
        try {
            if (TextUtils.isEmpty(key)) {
                return localFileUri(context);
            }
            return getImageUri(key, ImageScale.SMALL);
        } catch (Exception e) {
            Timber.w(e, "Error with getHighResUri");
            return Uri.EMPTY;
        }
    }

    @NonNull
    @SuppressWarnings("SuspiciousNameCombination")
    public Uri getMediumResUri(@NonNull Context context) {
        try {
            if (TextUtils.isEmpty(key)) {
                return localFileUri(context);
            }
            return getImageUri(key, ImageScale.MEDIUM);
        } catch (Exception e) {
            Timber.w(e, "Error with getMediumResUri");
            return Uri.EMPTY;
        }
    }

    @SuppressWarnings("SuspiciousNameCombination")
    @NonNull
    public Uri getHighResUri(@NonNull Context context) {
        try {
            if (TextUtils.isEmpty(key)) {
                return localFileUri(context);
            }
            return getImageUri(key, ImageScale.LARGE);
        } catch (Exception e) {
            Timber.w(e, "Error with getHighResUri");
            return Uri.EMPTY;
        }
    }

    private Uri getImageUri(String key, ImageScale scale) {
        String requestPath = String.format(Locale.US, "/image/%s/%s", key, scale.key);
        Uri uri = Uri.parse(ANetworkProvider.buildSecureBackendUrl(requestPath, null, 2));
        Timber.v("Requesting load for image at path: %s", uri.getPath());
        return uri;
    }

    private Uri localFileUri(Context context) throws FileNotFoundException {
        return Uri.parse("file://" + FileUtils.getInternalFilePath(context,
            STTConstants.DIRECTORY_PICTURES, fileName).getAbsolutePath());
    }

    public String getWorkoutKey() {
        return workoutKey;
    }

    public Integer getWorkoutId() {
        return workoutId;
    }

    public Point getLocation() {
        return location;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public double getTotalTime() {
        return totalTime;
    }

    public String getFileName() {
        return fileName;
    }

    public String getMd5Hash() {
        return md5Hash;
    }

    public int getId() {
        return id;
    }

    public String getKey() {
        return key;
    }

    public String getUsername() {
        return username;
    }

    public boolean isLocallyChanged() {
        return locallyChanged;
    }

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    public int getIndexInWorkoutHeader() {
        return indexInWorkoutHeader;
    }

    public String getDescription() {
        return description;
    }

    @Nullable
    public ReviewState getReviewState() { return reviewState; }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(id);
        dest.writeString(key);
        dest.writeParcelable(location, flags);
        dest.writeLong(timestamp);
        dest.writeDouble(totalTime);
        dest.writeString(fileName);
        dest.writeValue(workoutId);
        dest.writeString(workoutKey);
        dest.writeString(md5Hash);
        dest.writeValue(locallyChanged);
        dest.writeString(description);
        dest.writeString(username);
        dest.writeInt(width);
        dest.writeInt(height);
        dest.writeParcelable(reviewState, flags);
    }

    public ImageInformation synced() {
        return new ImageInformation(id, key, location, timestamp, totalTime, fileName, workoutId,
            workoutKey, md5Hash, description, false, username, width, height, indexInWorkoutHeader, reviewState);
    }

    public ImageInformation updateUserName(String userName) {
        return new ImageInformation(id, key, location, timestamp, totalTime, fileName, workoutId,
            workoutKey, md5Hash, description, true, userName, width, height, indexInWorkoutHeader, reviewState);
    }

    public ImageInformation updateWithIndexInWorkoutHeader(int indexInWorkoutHeader) {
        return new ImageInformation(id, key, location, timestamp, totalTime, fileName, workoutId,
            workoutKey, md5Hash, description, locallyChanged, username, width, height,
            indexInWorkoutHeader, reviewState);
    }

    @NonNull
    public Picture toPicture() {
        return new Picture(
            id,
            key,
            location,
            timestamp,
            totalTime,
            fileName,
            workoutId,
            workoutKey,
            md5Hash,
            locallyChanged,
            description,
            username,
            width,
            height,
            indexInWorkoutHeader,
            reviewState != null ? reviewState : ReviewState.PASS
        );
    }

    @NonNull
    public static ImageInformation fromPicture(Picture picture) {
        return new ImageInformation(
            picture.getId() == null ? generateId() : picture.getId(),
            picture.getKey(),
            picture.getLocation(),
            picture.getTimestamp(),
            picture.getTotalTime(),
            picture.getFileName(),
            picture.getWorkoutId(),
            picture.getWorkoutKey(),
            picture.getMd5Hash(),
            picture.getDescription(),
            picture.getLocallyChanged(),
            picture.getUsername(),
            picture.getWidth(),
            picture.getHeight(),
            picture.getIndexInWorkoutHeader(),
            picture.getReviewState()
        );
    }

    public abstract static class DbFields {
        public static final String DESCRIPTION = "description";
        public static final String FILE_NAME = "fileName";
        public static final String WORKOUT_KEY = "workoutKey";
        public static final String KEY = "key";
        public static final String WORKOUT_ID = "workoutId";
        public static final String MD5_HASH = "md5Hash";
        public static final String ID = "id";
        public static final String TOTAL_TIME = "totalTime";
        public static final String TIMESTAMP = "timestamp";
        public static final String LOCALLY_CHANGED = "locallyChanged";
        public static final String LOCATION = "location";
        public static final String USER_NAME = "username";
        public static final String WIDTH = "width";
        public static final String HEIGHT = "height";

        public static final String REVIEW_STATE = "reviewState";
    }

    /**
     * Custom Gson deserializer to get the proper value for key
     */
    public static class Deserializer implements JsonDeserializer<ImageInformation> {
        @Override
        public ImageInformation deserialize(JsonElement json, Type typeOfT,
            JsonDeserializationContext context) throws JsonParseException {
            JsonObject jsonObject = json.getAsJsonObject();
            Point location = context.deserialize(jsonObject.get("location"), Point.class);
            long timestamp = context.deserialize(jsonObject.get("timestamp"), long.class);
            double totalTime = context.deserialize(jsonObject.get("totalTime"), double.class);
            String username = context.deserialize(jsonObject.get("username"), String.class);
            int width = context.deserialize(jsonObject.get("width"), int.class);
            int height = context.deserialize(jsonObject.get("height"), int.class);

            JsonElement contentReviewStatus = jsonObject.get("contentReviewStatus");
            int reviewStatus = contentReviewStatus != null ? context.deserialize(contentReviewStatus, int.class) : -1;

            JsonElement idElement = jsonObject.get("id");
            if (idElement == null) {
                // the image info is fetched from backend
                String key = context.deserialize(jsonObject.get("key"), String.class);
                String workoutKey = context.deserialize(jsonObject.get("workoutKey"), String.class);
                String description =
                    context.deserialize(jsonObject.get("description"), String.class);
                return new ImageInformation(key.hashCode(), key, location, timestamp, totalTime,
                    null, null, workoutKey, null, description, false, username, width, height, 0, ConvertToReviewState.INSTANCE.getReviewState(reviewStatus));
            } else {
                // the image info recovered from auto save
                int id = context.deserialize(idElement, int.class);
                String fileName = context.deserialize(jsonObject.get("fileName"), String.class);
                String md5Hash = context.deserialize(jsonObject.get("md5Hash"), String.class);
                return new ImageInformation(id, null, location, timestamp, totalTime, fileName,
                    null, null, md5Hash, null, true, username, width, height, 0, ConvertToReviewState.INSTANCE.getReviewState(reviewStatus));
            }
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ImageInformation that = (ImageInformation) o;

        if (id != that.id) return false;
        if (timestamp != that.timestamp) return false;
        if (Double.compare(that.totalTime, totalTime) != 0) return false;
        if (locallyChanged != that.locallyChanged) return false;
        if (!Objects.equals(key, that.key)) return false;
        if (!Objects.equals(location, that.location)) return false;
        if (!Objects.equals(fileName, that.fileName)) return false;
        if (!Objects.equals(workoutId, that.workoutId)) return false;
        if (!Objects.equals(workoutKey, that.workoutKey)) return false;
        if (!Objects.equals(md5Hash, that.md5Hash)) return false;
        if (!Objects.equals(description, that.description)) return false;
        if (!Objects.equals(username, that.username)) return false;
        if (width != that.width) return false;
        if (height != that.height) return false;
        if (reviewState != that.reviewState) return false;
        return true;
    }

    @Override
    public int hashCode() {
        int result;
        result = id;
        result = 31 * result + (key != null ? key.hashCode() : 0);
        result = 31 * result + (location != null ? location.hashCode() : 0);
        result = 31 * result + Long.hashCode(timestamp);
        result = 31 * result + Double.hashCode(totalTime);
        result = 31 * result + (fileName != null ? fileName.hashCode() : 0);
        result = 31 * result + (workoutId != null ? workoutId.hashCode() : 0);
        result = 31 * result + (workoutKey != null ? workoutKey.hashCode() : 0);
        result = 31 * result + (md5Hash != null ? md5Hash.hashCode() : 0);
        result = 31 * result + (locallyChanged ? 1 : 0);
        result = 31 * result + (description != null ? description.hashCode() : 0);
        result = 31 * result + (username != null ? username.hashCode() : 0);
        result = 31 * result + Integer.hashCode(width);
        result = 31 * result + Integer.hashCode(height);
        result = 31 * result + (reviewState != null ? reviewState.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "ImageInformation{" +
            "id=" + id +
            ", key='" + key + '\'' +
            ", fileName='" + fileName + '\'' +
            ", workoutId=" + workoutId +
            ", workoutKey='" + workoutKey + '\'' +
            ", username='" + username + '\'' +
            ", width=" + width +
            ", height=" + height +
            ", reviewState=" + reviewState +
            '}';
    }

    private enum ImageScale {
        LARGE("l"),
        MEDIUM("m"),
        SMALL("s");

        final String key;

        ImageScale(String key) {
            this.key = key;
        }
    }
}
