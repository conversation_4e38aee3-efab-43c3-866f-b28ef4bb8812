package com.stt.android.domain.user

import com.stt.android.data.source.local.user.LocalUser
import com.stt.android.data.source.local.user.LocalUserSession
import com.stt.android.data.source.local.user.UserDao
import com.stt.android.domain.session.DomainUserSession
import com.stt.android.domain.session.EmailVerificationState
import com.stt.android.remote.user.RemoteUser
import com.stt.android.remote.user.RemoteUserGearLatest
import com.stt.android.remote.user.UserRemoteApi
import javax.inject.Inject

class UserDataSourceImpl @Inject constructor(
    private val userDao: UserDao,
    private val userRemoteApi: UserRemoteApi,
) : UserDataSource {
    override suspend fun findLocalUser(username: String): User? =
        userDao.findByUsername(username)?.toDomain()

    override suspend fun findLocalUsers(usernames: Set<String>): Map<String, User> =
        userDao.findByUsernames(usernames)
            .associate { localUser -> localUser.username to localUser.toDomain() }

    override suspend fun fetchUserFromRemote(username: String): User =
        userRemoteApi.fetchUserByUsername(username).toDomain()

    override suspend fun getLatestGearFromRemote(username: String): List<UserGearLatest> =
        userRemoteApi.getLatestGear(username).map {
            it.toDomain()
        }

    override suspend fun getAllOtherUsers(): List<User> =
        userDao.getAllOtherUsers().map(LocalUser::toDomain)

    override suspend fun getAllOtherUsernames(): List<String> =
        userDao.getAllOtherUsernames()

    // Keep the currently logged in user, but replace all other users in the database
    override suspend fun replaceOtherUsers(users: List<User>) =
        userDao.replaceOtherUsers(users.map(User::toLocal))
}

fun RemoteUser.toDomain() = User(
    id = null,
    key = key,
    username = username,
    description = description,
    website = website,
    city = city,
    country = country,
    profileImageUrl = profileImageUrl,
    profileImageKey = profileImageKey,
    realName = realName,
    session = null,
    followModel = followModel,
    coverImageUrl = coverImageUrl,
    showLocale = showLocale,
    blocked = blocked,
)

fun LocalUser.toDomain() = User(
    id = id,
    key = key,
    username = username,
    website = website,
    city = city,
    country = country,
    profileImageUrl = profileImageUrl,
    profileImageKey = profileImageKey,
    realName = realName,
    description = description,
    session = session?.toDomain(),
    followModel = followModel,
    createdDate = createdDate,
    lastLogin = lastLogin,
    roles = roles
)

fun User.toLocal(): LocalUser = LocalUser(
    id = id ?: 0, // ID of 0 indicates SQLite will auto-generate an unique ID
    key = key,
    username = username,
    website = website,
    city = city,
    country = country,
    profileImageUrl = profileImageUrl,
    profileImageKey = profileImageKey,
    realName = realName,
    description = description,
    session = session?.toLocal(),
    followModel = followModel,
    fieldTester = isFieldTester,
    createdDate = createdDate,
    lastLogin = lastLogin,
    roles = roles,
    coverImageUrl = null,
    showLocale = null,
    blocked = null,
)

fun DomainUserSession.toLocal(): LocalUserSession = LocalUserSession(
    sessionKey = sessionKey,
    watchKey = watchKey,
    facebookConnected = facebookConnected,
    emailVerified = when (emailVerificationState) {
        EmailVerificationState.VERIFIED -> true
        EmailVerificationState.NOT_VERIFIED -> false
        EmailVerificationState.UNKNOWN -> null
    },
)

fun LocalUserSession.toDomain(): DomainUserSession = DomainUserSession(
    sessionKey = sessionKey,
    watchKey = watchKey,
    facebookConnected = facebookConnected,
    emailVerificationState = when (emailVerified) {
        null -> EmailVerificationState.UNKNOWN
        true -> EmailVerificationState.VERIFIED
        false -> EmailVerificationState.NOT_VERIFIED
    },
)

fun RemoteUserGearLatest.toDomain() = UserGearLatest(
    serialNumber = serialNumber,
    displayName = displayName,
    productType = productType
)
