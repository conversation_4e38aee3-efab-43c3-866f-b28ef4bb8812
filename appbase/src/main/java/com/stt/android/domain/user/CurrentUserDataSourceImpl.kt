package com.stt.android.domain.user

import android.app.Application
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.core.content.ContextCompat
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.userSettings
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.source.local.user.UserDao
import com.stt.android.utils.STTConstants
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.util.concurrent.atomic.AtomicReference
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CurrentUserDataSourceImpl @Inject constructor(
    private val userDao: UserDao,
    private val userSettingsController: UserSettingsController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    application: Application,
) : CurrentUserDataSource {
    private val coroutineScope: CoroutineScope = CoroutineScope(SupervisorJob() + coroutinesDispatchers.io)

    // TODO: first startup, do we need to store anon user to DB? why would we?
    // Block current thread at app start-up here even if it is the UI thread. We don't have much
    // choice since many places assume direct access to current user.
    private var currentUser: AtomicReference<User> = runBlocking(coroutinesDispatchers.io) {
        AtomicReference(getCurrentUserOrNull() ?: User.ANONYMOUS)
    }

    private val userChangedReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            @OptIn(DelicateCoroutinesApi::class)
            coroutineScope.launch(coroutinesDispatchers.io) {
                runSuspendCatching {
                    currentUser.set(getCurrentUserOrNull() ?: User.ANONYMOUS)
                }
            }
        }
    }

    init {
        userSettingsController.userSettings()
            .onEach { userSettings ->
                currentUser.getAndUpdate { user -> user.combine(userSettings) }
            }
            .launchIn(coroutineScope)

        ContextCompat.registerReceiver(
            application,
            userChangedReceiver,
            IntentFilter(STTConstants.BroadcastActions.USER_STATUS_CHANGED),
            ContextCompat.RECEIVER_EXPORTED,
        )
    }

    override fun getCurrentUser(): User = currentUser.get()

    override suspend fun getCurrentUserOrNull(): User? = userDao.findCurrentUser()
        ?.toDomain()
        ?.combine(userSettingsController.settings)

    override fun storeCurrentUser(user: User): User = runBlocking(coroutinesDispatchers.io) {
        val combinedUser = user.combine(userSettingsController.settings)
        userDao.upsert(combinedUser.toLocal())
        currentUser.set(combinedUser)
        combinedUser
    }

    private companion object {
        // For historical reasons, we update current user's real name and description through
        // user settings, so we need to combine them here.
        fun User.combine(userSettings: UserSettings): User = copy(
            realName = userSettings.realName,
            description = userSettings.description,
        )
    }
}
