package com.stt.android.hr;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothDevice;
import android.text.TextUtils;

import com.stt.android.bluetooth.BluetoothDeviceManager;

public class BluetoothHeartRateDeviceManager extends BluetoothDeviceManager {
    @Override
    protected boolean isSupportedDevice(BluetoothDevice device) {
        @SuppressLint("MissingPermission") String deviceName = device.getName();
        return !TextUtils.isEmpty(deviceName) && HeartRateMonitorType.fromName(deviceName) != null;
    }
}
