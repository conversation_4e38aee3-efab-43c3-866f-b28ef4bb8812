package com.stt.android.social.workoutlist

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.BackendController
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.PicturesController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.UserProfilePreferences
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.follow.IsFolloweeUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.social.userprofile.UserProfileViewModel.Companion.PROFILE_IMAGES_COUNT
import com.stt.android.utils.STTConstants.UserProfilePreferences.*
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.format.DateTimeFormatter
import java.util.Locale
import javax.inject.Inject
import com.stt.android.core.R as CR
import androidx.core.content.edit
import androidx.lifecycle.SavedStateHandle
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.domain.review.ReviewState
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.User
import com.stt.android.social.userprofileV2.BaseUserProfileActivity.Companion.KEY_USER
import com.stt.android.social.userprofileV2.BaseUserProfileActivity.Companion.KEY_USER_NAME
import kotlinx.coroutines.flow.onStart

@HiltViewModel
class AllWorkoutViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    @ApplicationContext private val context: Context,
    private val workoutHeaderController: WorkoutHeaderController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    private val currentUserController: CurrentUserController,
    private val picturesController: PicturesController,
    private val backendController: BackendController,
    private val isFolloweeUseCase: IsFolloweeUseCase,
    private val userSettingsController: UserSettingsController,
    @UserProfilePreferences private val sharedPreferences: SharedPreferences,
) : ViewModel() {
    sealed class ViewData {
        data object Loading : ViewData()

        data class Loaded(
            val showFilterAndDate: Boolean,
            // The key is 'All' or the activity type id
            val dateAndWorkouts: Map<String, List<Any>>,
            val images: List<ImageInformation>,
        ) : ViewData()

        data object Error : ViewData()
    }

    private data class CombinedData(
        val workouts: Map<String, List<Any>> = emptyMap(),
        val images: List<ImageInformation> = emptyList()
    )

    val username: String = savedStateHandle.get<String>(KEY_USER_NAME)
        ?: savedStateHandle.get<User>(KEY_USER)?.username
        ?: currentUserController.username
    private val isCurrentUser: Boolean
        get() = username == currentUserController.username

    private val _viewState: MutableStateFlow<ViewData> = MutableStateFlow(ViewData.Loading)
    val viewState: StateFlow<ViewData> = _viewState.asStateFlow()
    val measurementUnit: MeasurementUnit
        get() = userSettingsController.settings.measurementUnit

    var lastOperationHistory: OperationHistory
        get() = OperationHistory(
            lastTabIndex = sharedPreferences.getInt(KEY_ALL_WORKOUT_LAST_TAB, 0),
            lastWorkoutFilter = sharedPreferences.getString(KEY_ALL_WORKOUT_LAST_FILTER, "") ?: "",
        )
        set(value) {
            sharedPreferences.edit {
                putInt(
                    KEY_ALL_WORKOUT_LAST_TAB,
                    value.lastTabIndex,
                )
                putString(
                    KEY_ALL_WORKOUT_LAST_FILTER,
                    value.lastWorkoutFilter,
                )
            }
        }

    private var job: Job? = null

    init {
        workoutHeaderController.currentUserWorkoutUpdated
            .onStart { emit(Unit) }
            .onEach { load() }
            .launchIn(viewModelScope)
    }

    private fun getUserWorkoutFlow(): Flow<Map<String, List<Any>>> = flow {
        emit(loadUserWorkout())
    }.catch { e ->
        Timber.w(e, "Failed to load workouts")
        emit(emptyMap())
    }.flowOn(coroutinesDispatchers.io)

    private fun getImagesFlow(): Flow<List<ImageInformation>> = flow {
        val local = picturesController.findByUserName(username, PROFILE_IMAGES_COUNT.toLong())
        emit(local)

        if (isCurrentUser) {
            return@flow
        }

        // Not current user, also fetch from backend.
        val session = currentUserController.session

        // backend consider since as ">="
        val since = (local.firstOrNull()?.timestamp ?: -1L) + 1L
        val remote = backendController.fetchUserPictures(session, username, since)
            .also { saveImagesIfNeeded(it) }
        emit(local + remote)
    }.catch { e ->
        Timber.w(e, "Unable to load images for user profile view.")
        emit(emptyList())
    }.flowOn(coroutinesDispatchers.io)


    fun load() {
        job?.cancel()
        job = viewModelScope.launch {
            combine(
                getUserWorkoutFlow(),
                getImagesFlow()
            ) { workouts, images ->
                CombinedData(workouts, images.filter { it.reviewState == ReviewState.PASS })
            }
            .catch { e ->
                Timber.w(e, "Failed to load combined data")
                emit(CombinedData())
                _viewState.value = ViewData.Error
            }
            .collect { (workouts, images) ->
                _viewState.update { currentState ->
                    if (currentState is ViewData.Loaded) {
                        currentState.copy(
                            dateAndWorkouts = workouts,
                            images = images
                        )
                    } else {
                        ViewData.Loaded(
                            isCurrentUser,
                            workouts,
                            images,
                        )
                    }
                }
            }
        }
    }

    fun onTabSelected(tabIndex: Int) {
        lastOperationHistory = lastOperationHistory.copy(
            lastTabIndex = tabIndex,
        )
    }

    fun onFilterSelected(filter: String) {
        lastOperationHistory = lastOperationHistory.copy(
            lastWorkoutFilter = filter,
        )
    }

    private suspend fun loadUserWorkout(): Map<String, List<Any>> =
        withContext(coroutinesDispatchers.io) {
            val allWorkouts = workoutHeaderController.findAllWhereOwner(username, false)
                .takeUnless { it.isEmpty() }
                ?.sortedByDescending(WorkoutHeader::startTime)
                ?: emptyList()
            if (!isCurrentUser) {
                return@withContext buildMap {
                    put(context.getString(CR.string.all_filter_tag), allWorkouts)
                }
            }
            val dateFormatter = DateTimeFormatter.ofPattern(
                "MMMM yyyy",
                Locale(context.getString(R.string.language_code)),
            )
            val allWorkoutsWithDates = allWorkouts.groupBy {
                dateFormatter.format(it.startTime.toLocalDate())
            }.flatMap { (date, workouts) ->
                listOf(DateHeader(date, workouts.size)) + workouts
            }
            val groupedByActivityType = allWorkouts
                .groupBy { it.activityType.id.toString() }
                .toList()
                .sortedByDescending { (_, workouts) -> workouts.size }
                .take(5)
                .toMap()
                .mapValues { (_, workouts) ->
                    workouts.groupBy {
                        dateFormatter.format(it.startTime.toLocalDate())
                    }.flatMap { (date, workouts) ->
                        listOf(DateHeader(date, workouts.size)) + workouts
                    }
                }

            buildMap {
                put(context.getString(CR.string.all_filter_tag), allWorkoutsWithDates)
                putAll(groupedByActivityType)
            }
        }

    private suspend fun saveImagesIfNeeded(images: List<ImageInformation>) {
        if (!isFolloweeUseCase.isFollowee(username)) {
            return
        }
        runSuspendCatching {
            // i love my friends, so cache the data
            picturesController.store(images)
        }.onFailure { e ->
            Timber.w(e, "Error while storing followee images")
        }
    }
}
