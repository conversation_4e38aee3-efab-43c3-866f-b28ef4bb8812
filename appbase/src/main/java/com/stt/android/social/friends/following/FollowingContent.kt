package com.stt.android.social.friends.following

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.composables.EmptyView
import com.stt.android.social.friends.composables.FriendsListView

@Composable
fun FollowingContent(
    state: FollowingState,
    onFriendClick: (Friend) -> Unit,
    onStatusClick: (Friend) -> Unit,
    modifier: Modifier = Modifier,
) {
    FriendsListView(
        friends = state.friends,
        onFriendClick = onFriendClick,
        onStatusClick = onStatusClick,
        modifier = modifier,
        headerView = {
            item(key = "list_title_${state.followingCount}") {
                Text(
                    stringResource(R.string.my_following_prefix, state.followingCount),
                    style = MaterialTheme.typography.bodyLargeBold,
                    modifier = Modifier.padding(MaterialTheme.spacing.medium),
                )
            }
        },
        emptyView = {
            EmptyView(
                icon = R.drawable.ic_empty_friends,
                tips = R.string.no_following_new,
                modifier = Modifier.fillMaxSize(),
            )
        }
    )
}

@Preview
@Composable
private fun FollowingScreenPreview() {
    M3AppTheme {
        FollowingContent(
            state = FollowingState(
                friends = emptyList()
            ),
            onFriendClick = {},
            onStatusClick = {},
        )
    }
}
