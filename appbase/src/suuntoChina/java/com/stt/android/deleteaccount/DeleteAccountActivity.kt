package com.stt.android.deleteaccount

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.R
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.compose.widgets.M3ConfirmationDialog
import com.stt.android.di.AppVersionNumberForSupport
import com.stt.android.help.BaseSupportHelper
import com.stt.android.home.settings.PhoneNumberUtil
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class DeleteAccountActivity : AppCompatActivity() {
    @Inject
    @AppVersionNumberForSupport
    internal lateinit var appVersionNumberForSupport: String

    @Inject
    internal lateinit var supportHelper: BaseSupportHelper

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithM3Theme {
            DeleteAccountScreen()
        }
    }

    private fun onContactSupportClick() {
        supportHelper.showFAQ(
            this,
            getString(R.string.delete_account_helpshift_article_id),
            appVersionNumberForSupport
        )
    }

    @Composable
    private fun DeleteAccountScreen(
        modifier: Modifier = Modifier,
        onContactSupportClick: () -> Unit = ::onContactSupportClick,
        viewModel: DeleteAccountViewModel = hiltViewModel(),
    ) {
        val phoneDisplay = PhoneNumberUtil.maskPhoneNumberWithRegionCode(viewModel.phoneNumber)
        val deleteAccountState by viewModel.deleteAccountState.collectAsState()
        val realName = viewModel.realName

        val snackbarHostState = remember {
            SnackbarHostState()
        }

        val confirmDeleteRequest =
            remember { mutableStateOf<DeleteAccountEvent.RequestConfirmDelete?>(null) }

        LaunchedEffect(Unit) {
            viewModel.event.collect { event ->
                when (event) {
                    DeleteAccountEvent.NetworkError -> {
                        snackbarHostState.showSnackbar(
                            message = getString(R.string.no_network_error),
                            duration = SnackbarDuration.Short,
                        )
                    }

                    DeleteAccountEvent.GeneralHttpError -> {
                        snackbarHostState.showSnackbar(
                            message = getString(R.string.error_generic),
                            duration = SnackbarDuration.Short,
                        )
                    }

                    DeleteAccountEvent.TooManyRequestsError -> {
                        snackbarHostState.showSnackbar(
                            message = getString(R.string.delete_account_too_many_request),
                            duration = SnackbarDuration.Short,
                        )
                    }

                    is DeleteAccountEvent.RequestConfirmDelete -> {
                        confirmDeleteRequest.value = event
                    }
                }
            }
        }
        DeleteAccountContent(
            phoneDisplay = phoneDisplay,
            deleteAccountState = deleteAccountState,
            onBackClick = ::finish,
            onSendCodeClick = viewModel::onSendCodeClick,
            onInputVerificationCode = viewModel::onInputVerificationCode,
            onVerifyClick = viewModel::onVerifyClick,
            onContactSupportClick = onContactSupportClick,
            modifier = modifier,
            snackbarHostState = snackbarHostState,
        )

        confirmDeleteRequest.value?.let { event ->
            M3ConfirmationDialog(
                title = stringResource(R.string.account_settings_delete_account_title),
                text = "$realName\n${stringResource(R.string.delete_account_delete_confirm_message)}",
                cancelButtonText = stringResource(R.string.cancel),
                confirmButtonText = stringResource(R.string.delete),
                onDismissRequest = {
                    confirmDeleteRequest.value = null
                },
                onConfirm = {
                    confirmDeleteRequest.value = null
                    viewModel.onConfirmDelete(event.token, this, supportFragmentManager)
                },
            )
        }
    }

    companion object {
        fun newStartIntent(context: Context): Intent {
            return Intent(context, DeleteAccountActivity::class.java)
        }
    }
}
