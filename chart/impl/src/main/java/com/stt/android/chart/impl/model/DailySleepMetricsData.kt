package com.stt.android.chart.impl.model

import androidx.annotation.StringRes
import com.suunto.algorithms.data.HeartRate
import kotlin.time.Duration

internal data class DailySleepMetricsData(
    val sleepQuality: DailySleepQualityData?,
    val sleepDuration: DailySleepDurationData?,
    val resources: DailySleepResourcesData?,
)

internal data class DailySleepQualityData(
    val quality: Float,
    val qualityDesc: String?,
    val avgHr: HeartRate?,
    val minHr: HeartRate?,
    val avgHrv: Int?,
    val maxSpO2: Float?,
    val altitude: Double?,
    @StringRes val altitudeUnitRes: Int,
)

internal data class DailySleepDurationData(
    val longSleep: SleepPeriod?,
    val naps: List<SleepPeriod>,
    val longSleepDuration: Duration,
    val napDuration: Duration,
    val totalDuration: Duration,
    val goal: Duration,
)

internal data class SleepPeriod(
    val fellAsleep: String,
    val wokeUp: String,
    val totalDuration: Duration,
)

internal data class DailySleepResourcesData(
    val wakeUpBalance: Float,
    val gainedBalance: Float?,
)
