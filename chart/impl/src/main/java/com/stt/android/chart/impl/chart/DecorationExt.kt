package com.stt.android.chart.impl.chart

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import com.patrykandpatrick.vico.compose.common.component.rememberLineComponent
import com.patrykandpatrick.vico.compose.common.component.rememberShapeComponent
import com.patrykandpatrick.vico.compose.common.fill
import com.patrykandpatrick.vico.compose.common.shape.dashedShape
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.decoration.Decoration
import com.patrykandpatrick.vico.core.cartesian.decoration.HorizontalLine
import com.patrykandpatrick.vico.core.common.shape.CorneredShape
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.compose.theme.nearBlack
import kotlinx.collections.immutable.ImmutableMap
import kotlin.math.roundToInt

@Composable
internal fun rememberGoalDecoration(goal: Number): Decoration {
    val line = rememberLineComponent(
        fill = fill(MaterialTheme.colorScheme.nearBlack),
        shape = dashedShape(),
    )
    return remember(goal) {
        HorizontalLine(
            y = { goal.toDouble() },
            line = line,
        )
    }
}

@Composable
internal fun rememberHighlightDecorations(highlightDecorationLines: ImmutableMap<Number, Int>): List<Decoration> {
    return highlightDecorationLines.map { (value, color) ->
        val line = rememberLineComponent(
            fill = fill(Color(color)),
        )
        remember(value, color) {
            HorizontalLine(
                y = { value.toDouble() },
                line = line,
            )
        }
    }
}

@Composable
internal fun rememberBackgroundRegionDecoration(
    regions: List<ChartData.BackgroundRegionEntry>,
    color: Color,
): Decoration {
    val backgroundComponent = rememberShapeComponent(
        fill = fill(color),
        shape = CorneredShape.Pill
    )

    return remember(regions, color) {
        BackgroundRegionDecoration(
            entries = regions,
            backgroundComponent = backgroundComponent,
            color = color.toArgb(),
            verticalAxisPosition = null,
        )
    }
}

@Composable
internal fun rememberVerticalColorBar(
    thresholds: List<Int>,
    colors: List<Color>,
    labelTexts: List<String>,
    barThicknessDp: Float,
    barShiftXDp: Float,
): Decoration {
    val colorInts = colors.map { it.toArgb() }

    return remember(thresholds, colorInts, labelTexts, barThicknessDp, barShiftXDp) {
        VerticalColorBarDecoration(
            thresholds = thresholds,
            colors = colorInts,
            labelTexts = labelTexts,
            barThicknessDp = barThicknessDp,
            barShiftXDp = barShiftXDp,
            verticalAxisPosition = null,
        )
    }
}

@Composable
internal fun rememberAverageDecorations(average: Float?): Decoration? {
    if (average == null) {
        return null
    }
    val line = rememberLineComponent(
        fill = fill(MaterialTheme.colorScheme.onSurface),
        shape = dashedShape(),
    )
    val labelBackgroundColor = MaterialTheme.colorScheme.nearBlack.toArgb()
    return remember(average) {
        AverageGuideLine(
            y = { average.toDouble() },
            line = line,
            label = { average.roundToInt().toString() },
            labelColor = Color.White.toArgb(),
            labelBackgroundColor = labelBackgroundColor,
            verticalAxisPosition = Axis.Position.Vertical.End,
        )
    }
}
