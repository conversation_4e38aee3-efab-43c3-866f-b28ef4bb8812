package com.stt.android.chart.impl.chart.axis

import android.content.Context
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import com.patrykandpatrick.vico.core.cartesian.CartesianDrawingContext
import com.patrykandpatrick.vico.core.cartesian.CartesianMeasuringContext
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.axis.VerticalAxis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianValueFormatter
import com.patrykandpatrick.vico.core.common.Position
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.formatYAxisLabel
import kotlin.math.abs
import kotlin.math.max

// Copied and modified from DefaultVerticalAxisItemPlacer (unfortunately an internal class).
// We also put the goal here, because the Decoration from the library doesn't support putting label
// beyond the y-axis.
class YAxisItemPlacer(
    private val decorations: List<Number?> = emptyList(),
    private val colorIndicator: ChartData.ColorIndicator?
) : VerticalAxis.ItemPlacer {
    
    override fun getTopLayerMargin(
        context: CartesianMeasuringContext,
        verticalLabelPosition: Position.Vertical,
        maxLabelHeight: Float,
        maxLineThickness: Float,
    ): Float = (max(maxLabelHeight, maxLineThickness) + maxLineThickness) / 2.0F

    override fun getBottomLayerMargin(
        context: CartesianMeasuringContext,
        verticalLabelPosition: Position.Vertical,
        maxLabelHeight: Float,
        maxLineThickness: Float,
    ): Float = (max(maxLabelHeight, maxLineThickness) + maxLineThickness) / 2.0F

    override fun getWidthMeasurementLabelValues(
        context: CartesianMeasuringContext,
        axisHeight: Float,
        maxLabelHeight: Float,
        position: Axis.Position.Vertical,
    ): List<Double> = buildList {
        if (colorIndicator != null && colorIndicator.thresholds.isNotEmpty() && colorIndicator.useThresholdsAsAxisLines) {
            addAll(colorIndicator.thresholds.map { it.toDouble() })
        } else {
            decorations.forEach { decoration ->
                decoration?.let { add(it.toDouble()) }
            }

            val yRange = context.ranges.getYRange(position)
            add(yRange.minY)

            val extraItemCount = (axisHeight / maxLabelHeight).toInt()
                .coerceAtMost(LABELS_COUNT - 1)
            val step = yRange.length / extraItemCount

            repeat(extraItemCount) { i ->
                val y = yRange.minY + (i + 1) * step

                val tooCloseToAnyDecoration = decorations.any { decoration ->
                    decoration != null && abs(y - decoration.toDouble()) / yRange.length <= maxLabelHeight / 2.0F / axisHeight
                }

                if (!tooCloseToAnyDecoration) {
                    add(y)
                }
            }
        }
    }

    override fun getHeightMeasurementLabelValues(
        context: CartesianMeasuringContext,
        position: Axis.Position.Vertical,
    ): List<Double> {
        if (colorIndicator != null && colorIndicator.thresholds.size >= 2 && colorIndicator.useThresholdsAsAxisLines) {
            val minThreshold = colorIndicator.thresholds.first().toDouble()
            val maxThreshold = colorIndicator.thresholds.last().toDouble()
            val midThreshold = (minThreshold + maxThreshold) / 2.0
            return listOf(minThreshold, midThreshold, maxThreshold)
        }
        val yRange = context.ranges.getYRange(position)
        return listOf(yRange.minY, (yRange.minY + yRange.maxY) / 2.0, yRange.maxY)
    }

    override fun getLineValues(
        context: CartesianDrawingContext,
        axisHeight: Float,
        maxLabelHeight: Float,
        position: Axis.Position.Vertical,
    ): List<Double> = buildList {
        if (colorIndicator != null && colorIndicator.thresholds.isNotEmpty() && colorIndicator.useThresholdsAsAxisLines) {
            addAll(colorIndicator.thresholds.map { it.toDouble() })
        } else {
            val yRange = context.ranges.getYRange(position)
            add(yRange.minY)

            val extraItemCount = ((axisHeight / maxLabelHeight).toInt())
                .coerceAtMost(LABELS_COUNT - 1)
            val step = yRange.length / extraItemCount

            repeat(extraItemCount) { i ->
                val y = yRange.minY + (i + 1) * step

                val tooCloseToAnyDecoration = decorations.any { decoration ->
                    decoration != null && abs(y - decoration.toDouble()) / yRange.length <= maxLabelHeight / 2.0F / axisHeight
                }

                if (!tooCloseToAnyDecoration) {
                    add(y)
                }
            }
        }
    }

    override fun getLabelValues(
        context: CartesianDrawingContext,
        axisHeight: Float,
        maxLabelHeight: Float,
        position: Axis.Position.Vertical,
    ): List<Double> = getWidthMeasurementLabelValues(context, axisHeight, maxLabelHeight, position)


    fun createChartValueFormatter(
        highlightValues: Map<Number, Int>,
        defaultFormatter: CartesianValueFormatter = CartesianValueFormatter.decimal(),
        chartContent: ChartContent? = null,
        context: Context? = null
    ): CartesianValueFormatter {
        return ChartValueFormatter(highlightValues, defaultFormatter, chartContent, context)
    }


    private class ChartValueFormatter(
        private val highlightValues: Map<Number, Int>,
        private val defaultFormatter: CartesianValueFormatter,
        private val chartContent: ChartContent? = null,
        private val context: Context? = null
    ) : CartesianValueFormatter {
        override fun format(
            context: CartesianMeasuringContext,
            value: Double,
            verticalAxisPosition: Axis.Position.Vertical?
        ): CharSequence {
            val formattedValue = if (this.chartContent != null && this.context != null) {
                this.chartContent.formatYAxisLabel(this.context, value.toFloat())
            } else {
                defaultFormatter.format(context, value, verticalAxisPosition)
            }
            
            highlightValues.forEach { (highlightValue, color) ->
                if (value == highlightValue.toDouble()) {
                    return SpannableStringBuilder(formattedValue).apply {
                        setSpan(
                            ForegroundColorSpan(color),
                            0,
                            formattedValue.length,
                            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                        )
                    }
                }
            }

            return formattedValue
        }
    }
    
    private companion object {
        const val LABELS_COUNT: Int = 4
    }
}
