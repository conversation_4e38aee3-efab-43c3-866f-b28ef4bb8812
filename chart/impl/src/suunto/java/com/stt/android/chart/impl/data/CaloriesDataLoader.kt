package com.stt.android.chart.impl.data

import android.content.Context
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.TextUnitType
import androidx.compose.ui.unit.sp
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.R
import com.stt.android.chart.impl.model.ChartBarDisplayMode
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.screen.CurrentValueData
import com.stt.android.chart.impl.screen.GoalEditorViewData
import com.stt.android.chart.impl.screen.GoalViewData
import com.stt.android.controllers.UserSettingsController
import com.stt.android.data.TimeUtils
import com.stt.android.domain.EnergyUtil
import com.stt.android.domain.activitydata.dailyvalues.ActivityDataDailyRepository
import com.stt.android.domain.activitydata.goals.ActivityDataGoalRepository
import com.stt.android.domain.trenddata.FetchTrendDataUseCase
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.glance.ext.getDimensionValue
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.suunto.algorithms.data.Energy
import com.suunto.algorithms.data.Energy.Companion.cal
import com.suunto.algorithms.data.Energy.Companion.kcal
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import java.time.Clock
import java.time.LocalDate
import javax.inject.Inject
import kotlin.math.roundToInt
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

internal class CaloriesDataLoader @Inject constructor(
    @ApplicationContext private val appContext: Context,
    private val activityDataGoalRepository: ActivityDataGoalRepository,
    private val activityDataDailyRepository: ActivityDataDailyRepository,
    private val infoModelFormatter: InfoModelFormatter,
    private val clock: Clock,
    userSettingsController: UserSettingsController,
    fetchTrendDataUseCase: FetchTrendDataUseCase,
) : TrendDataLoader(appContext, userSettingsController, fetchTrendDataUseCase) {

    fun formatHighlightData(y: Int?): String =
        appContext.getString(BaseR.string.daily_goal_setting_value_1_energy, y.toString())

    fun loadGoalData(): GoalViewData = GoalViewData.Goal(
        icon = BaseR.drawable.ic_activity_data_calories,
        iconColor = BaseR.color.activity_data_energy,
        title = BaseR.string.daily_goal_setting_main_title_energy,
        goal = activityDataGoalRepository.fetchEnergyGoal()
            .map { energy ->
                appContext.getString(
                    BaseR.string.daily_goal_setting_value_1_energy,
                    energy.inKcal.roundToInt().toString(),
                )
            },
    )

    suspend fun loadGoalEditorData(): GoalEditorViewData = GoalEditorViewData.Editor(
        chartContent = ChartContent.CALORIES,
        requiresWatchConnection = true,
        currentGoal = activityDataGoalRepository.fetchEnergyGoal()
            .first()
            .inKcal
            .roundToInt(),
    )

    fun loadChartData(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        dateRange: ClosedRange<LocalDate>,
        comparisonDateRange: ClosedRange<LocalDate>?,
    ): Flow<ChartData> = combine(
        loadTrendData(
            chartGranularity = chartGranularity,
            dateRange = dateRange,
        ),
        comparisonDateRange?.let {
            loadTrendData(
                chartGranularity = chartGranularity,
                dateRange = comparisonDateRange,
            )
        } ?: flowOf(emptyList()),
        activityDataDailyRepository.fetchEnergy(),
        activityDataDailyRepository.fetchMetabolicEnergy(),
        activityDataGoalRepository.fetchEnergyGoal(),
    ) { trendDataList, comparisonTrendDataList, todayEnergy, bmr, energyGoal ->
        val goal = energyGoal.takeIf {
            chartStyle == ChartStyle.SINGLE && chartGranularity != ChartGranularity.DAILY
        }
        val adjustedTrendDataList =
            if (chartGranularity == ChartGranularity.DAILY) trendDataList else trendDataList.filter { it.energy > ENERGY_THRESHOLD }

        ChartData(
            chartGranularity = chartGranularity,
            series = listOfNotNull(
                createDataSeries(
                    chartStyle = chartStyle,
                    chartGranularity = chartGranularity,
                    dateRange = dateRange,
                    trendDataList = adjustedTrendDataList,
                    todayValue = todayEnergy.inKcal.toFloat(),
                    goal = goal?.inKcal,
                    color = appContext.getColor(BaseR.color.activity_data_energy),
                ),
                createComparisonDataSeries(
                    chartStyle = chartStyle,
                    chartGranularity = chartGranularity,
                    currentDateRange = dateRange,
                    comparisonDateRange = comparisonDateRange,
                    trendDataList = comparisonTrendDataList,
                ),
            ).toImmutableList(),
            highlightEnabled = chartStyle == ChartStyle.SINGLE,
            goal = goal?.inKcal,
            currentValues = persistentListOf(
                CurrentValueData(
                    value = buildAnnotatedString {
                        withStyle(SpanStyle(fontWeight = FontWeight.Bold, fontSize = 18.sp)) {
                            append(bmr.inKcal.roundToInt().toString())
                        }
                        withStyle(SpanStyle(fontSize = 12.sp)) {
                            append(" ")
                            append(appContext.getString(CR.string.kcal))
                        }
                    },
                    explanation = appContext.getString(R.string.calories_bmr),
                    icon = R.drawable.icon_bmr,
                    iconColor = 0,
                ),
                CurrentValueData(
                    value = buildAnnotatedString {
                        withStyle(SpanStyle(fontWeight = FontWeight.Bold, fontSize = 18.sp)) {
                            append(totalEnergy(
                                bmr = bmr,
                                dailyGranularity = chartGranularity == ChartGranularity.DAILY,
                                trendDataList = adjustedTrendDataList,
                            )
                                .inKcal
                                .roundToInt()
                                .toString())
                        }
                        withStyle(SpanStyle(fontSize = 12.sp)) {
                            append(" ")
                            append(appContext.getString(CR.string.kcal))
                        }
                    },
                    explanation = appContext.getString(R.string.total_calories),
                    icon = BaseR.drawable.ic_activity_data_calories,
                    iconColor = BaseR.color.suunto_dark_gray,
                ),
            ),
            chartBarDisplayMode = if (comparisonDateRange == null) {
                ChartBarDisplayMode.STACKED
            } else {
                ChartBarDisplayMode.GROUPED
            },
            chartContent = ChartContent.CALORIES,
            highlightDecorationLines = persistentMapOf(),
            colorIndicator = null,
        )
    }

    /**
     * value:unite is kcal, infoModelFormatter need joules
     */
    override fun getFormatedValue(value: Float): AnnotatedString = buildAnnotatedString {
        append(
            infoModelFormatter.formatValue(
                SummaryItem.ENERGY,
                value.kcal.inJoules,
            ).value.orEmpty()
        )
        append(" ")
        withStyle(
            SpanStyle(
                fontSize = TextUnit(
                    appContext.getDimensionValue(BaseR.dimen.text_size_small),
                    TextUnitType.Sp
                )
            )
        ) {
            append(appContext.getString(CR.string.kcal))
        }
    }

    override fun getValue(trendData: TrendData): Number = trendData.energy.inKcal

    private fun totalEnergy(
        bmr: Energy,
        dailyGranularity: Boolean,
        trendDataList: List<TrendData>,
    ): Energy = if (dailyGranularity) {
        val bmrScale =
            trendDataList.firstOrNull()?.let { getScaledBmr(it.timestamp, bmr) } ?: Energy.ZERO
        (bmrScale.inCal + trendDataList.sumOf { trendData -> trendData.energy.inCal }).cal
    } else {
        trendDataList.sumOf { (it.energy + bmr).inCal }.cal
    }

    private fun getScaledBmr(timestamp: Long, bmr: Energy): Energy {
        val itemDate = TimeUtils.epochToLocalZonedDateTime(timestamp).toLocalDate()
        return if (itemDate == LocalDate.now(clock)) {
            // For the current day, scale BMR according to the hour of day
            EnergyUtil.scaledBmrForToday(bmr, clock)
        } else {
            bmr
        }
    }

    private companion object {
        // If energy is below or equal to this value, it's considered that measurement has been off
        val ENERGY_THRESHOLD = 10.0.kcal
    }
}
