package com.stt.android.chart.impl.data

import android.content.Context
import com.stt.android.chart.api.model.ChartContent
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartStyle
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.sleep.FetchSleepUseCase
import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.trenddata.TrendData
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.utils.toEpochMilli
import com.suunto.algorithms.data.HeartRate
import com.suunto.algorithms.data.HeartRate.Companion.hz
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import timber.log.Timber
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters
import java.time.temporal.WeekFields
import java.util.Locale
import javax.inject.Inject
import kotlin.math.roundToInt

internal class MinimumHeartRateDataLoader @Inject constructor(
    @ApplicationContext appContext: Context,
    private val fetchSleepUseCase: FetchSleepUseCase,
    private val trendDataRepository: TrendDataRepository,
) : BaseHeartRateDataLoader(appContext) {
    
    private interface MinHeartRateSeriesStrategy {
        fun createSeries(
            from: LocalDate,
            to: LocalDate, 
            trendDataList: List<TrendData>,
            sleepList: List<Sleep>,
            chartColor: Int
        ): ChartData.Series
    }
    
    private val seriesStrategies: Map<ChartGranularity, MinHeartRateSeriesStrategy>
    
    init {
        seriesStrategies = mapOf(
            ChartGranularity.SEVEN_DAYS to DailyMinHeartRateSeriesStrategy(),
            ChartGranularity.THIRTY_DAYS to DailyMinHeartRateSeriesStrategy(),
            ChartGranularity.SIX_WEEKS to DailyMinHeartRateSeriesStrategy(),
            ChartGranularity.WEEKLY to DailyMinHeartRateSeriesStrategy(),
            ChartGranularity.MONTHLY to DailyMinHeartRateSeriesStrategy(),
            ChartGranularity.SIX_MONTHS to WeeklyMinHeartRateSeriesStrategy(),
            ChartGranularity.YEARLY to MonthlyMinHeartRateSeriesStrategy(),
            ChartGranularity.EIGHT_YEARS to YearlyMinHeartRateSeriesStrategy()
        )
    }

   fun loadChartData(
        chartStyle: ChartStyle,
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
    ): Flow<ChartData> {
        val fromMillis = from.atStartOfDay().toEpochMilli()
        val toMillis = to.atStartOfDay().toEpochMilli()
        
        val trendDataFlow = trendDataRepository.fetchTrendDataForDateRange(
            fromTimestamp = fromMillis,
            toTimestamp = toMillis,
            aggregated = false
        )
        
        val sleepFlow = fetchSleepUseCase.fetchSleeps(
            from = from,
            to = to
        ).catch { emit(emptyList()) }
        
        return combine(trendDataFlow, sleepFlow) { trendDataList, sleepList ->
            createMinHeartRateSeries(chartGranularity, from, to, trendDataList, sleepList)
        }.map { series ->
            val modifiedSeries = series.copy(
                chartType = ChartType.LINE,
                lineConfig = singleLineChartConfig
            )
            
            ChartData(
                chartGranularity = chartGranularity,
                series = persistentListOf(modifiedSeries),
                highlightEnabled = chartStyle == ChartStyle.SINGLE,
                goal = null,
                highlightDecorationLines = persistentMapOf(),
                currentValues = persistentListOf(),
                chartContent = ChartContent.MINIMUM_HEART_RATE,
                colorIndicator = null,
            )
        }
    }
    

    private fun createMinHeartRateSeries(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        trendDataList: List<TrendData>,
        sleepList: List<Sleep>
    ): ChartData.Series {
        val chartColor = getChartColor()
        
        val strategy = seriesStrategies[chartGranularity] 
            ?: throw UnsupportedOperationException("Unsupported chart granularity: ${chartGranularity.name}")
            
        return strategy.createSeries(from, to, trendDataList, sleepList, chartColor)
    }
    
    private fun getMinHeartRate(trendData: TrendData): HeartRate? {
        val hr = trendData.hr?.takeIf { trendData.hasHr }
        val minHr = trendData.hrMin
        return if (minHr != null && hr != null) {
            minOf(minHr, hr)
        } else {
            (minHr ?: hr)
        }?.hz
    }

    private fun getSleepTimeRanges(sleepList: List<Sleep>): List<ClosedRange<Long>> {
        return sleepList.mapNotNull { sleep ->
            sleep.longSleep?.let { it.fellAsleep..it.wokeUp }
        }
    }
    

    private fun filterNonSleepData(trendDataList: List<TrendData>, sleepTimeRanges: List<ClosedRange<Long>>): List<TrendData> {
        return trendDataList.filter { trendData ->
            sleepTimeRanges.none { range -> trendData.timestamp in range }
        }
    }
    
    private fun calculateDailyMinHrs(
        trendDataList: List<TrendData>,
        sleepTimeRanges: List<ClosedRange<Long>>,
        from: LocalDate,
        to: LocalDate
    ): List<Int> {
        val dataByDate = trendDataList.groupBy { it.timeISO8601.toLocalDate() }
        val dailyMinHrs = mutableListOf<Int>()

        var currentDate = from
        while (currentDate <= to) {
            val dataForDay = dataByDate[currentDate] ?: emptyList()
            
            val filteredData = dataForDay.filter { trendData ->
                sleepTimeRanges.none { range -> trendData.timestamp in range }
            }
            
            val minHrInBpm = filteredData
                .mapNotNull { getMinHeartRate(it) }
                .minOrNull()
                ?.inBpm
                ?.roundToInt()
                ?: NO_DATA_VALUE
            dailyMinHrs.add(minHrInBpm)
            currentDate = currentDate.plusDays(1)
        }

        return dailyMinHrs
    }
    
    private inner class DailyMinHeartRateSeriesStrategy : MinHeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            sleepList: List<Sleep>,
            chartColor: Int
        ): ChartData.Series {
            val sleepTimeRanges = getSleepTimeRanges(sleepList)
            val dailyMinHrs = calculateDailyMinHrs(trendDataList, sleepTimeRanges, from, to)
            
            val entries = mutableListOf<ChartData.Entry>()
            var currentDate = from
            
            var totalValue = 0f
            var totalDays = 0
            
            dailyMinHrs.forEach { hr ->
                val x = currentDate.toEpochDay()
                
                if (hr != NO_DATA_VALUE) {
                    entries.add(ChartData.Entry(x = x, y = hr))
                    totalValue += hr
                    totalDays++
                }
                
                currentDate = currentDate.plusDays(1)
            }
            
            val entriesImmutable = entries.toImmutableList()
            
            val averageValue = if (totalDays > 0) totalValue / totalDays else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            return ChartData.Series(
                chartType = ChartType.LINE,
                color = chartColor,
                axisRange = ChartData.AxisRange(
                    minX = from.toEpochDay().toDouble(),
                    maxX = to.toEpochDay().toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entriesImmutable,
                value = formatBpmValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
                average = averageValue,
            )
        }
    }
    
    private inner class WeeklyMinHeartRateSeriesStrategy : MinHeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            sleepList: List<Sleep>,
            chartColor: Int
        ): ChartData.Series {
            val sleepTimeRanges = getSleepTimeRanges(sleepList)
            val filteredData = filterNonSleepData(trendDataList, sleepTimeRanges)
            
            val entries = mutableListOf<ChartData.Entry>()
            
            var totalValue = 0f
            var totalDays = 0
            
            val dataByWeek = filteredData.groupBy { 
                val date = it.timeISO8601.toLocalDate()
                date.with(TemporalAdjusters.previousOrSame(WeekFields.of(Locale.getDefault()).firstDayOfWeek))
                    .toEpochDay()
            }
            
            val weeks = mutableListOf<Long>()
            var currentDate = from
            while (currentDate <= to) {
                val weekStartDate = currentDate.with(TemporalAdjusters.previousOrSame(WeekFields.of(Locale.getDefault()).firstDayOfWeek))
                val weekKey = weekStartDate.toEpochDay()
                if (!weeks.contains(weekKey)) {
                    weeks.add(weekKey)
                }
                currentDate = currentDate.plusDays(1)
            }
            
            weeks.sortedBy { it }.forEach { weekEpochDay ->
                val weekData = dataByWeek[weekEpochDay] ?: emptyList()
                
                weekData.groupBy { it.timeISO8601.toLocalDate() }
                    .mapNotNull { (_, dayData) ->
                        dayData.mapNotNull { getMinHeartRate(it) }.minOrNull()
                    }
                    .minOrNull()
                    ?.inBpm
                    ?.roundToInt()
                    ?.let { minHrInBpm ->
                        entries.add(ChartData.Entry(x = weekEpochDay, y = minHrInBpm))
                        totalValue += minHrInBpm
                        totalDays++
                    }
            }
            
            val entriesImmutable = entries.toImmutableList()
            
            val fromWeekStart = from.with(TemporalAdjusters.previousOrSame(WeekFields.of(Locale.getDefault()).firstDayOfWeek))
            val toWeekStart = to.with(TemporalAdjusters.previousOrSame(WeekFields.of(Locale.getDefault()).firstDayOfWeek))
            
            val averageValue = if (totalDays > 0) totalValue / totalDays else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            return ChartData.Series(
                chartType = ChartType.LINE,
                color = chartColor,
                axisRange = ChartData.AxisRange(
                    minX = fromWeekStart.toEpochDay().toDouble(),
                    maxX = toWeekStart.toEpochDay().toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entriesImmutable,
                value = formatBpmValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
                average = averageValue,
            )
        }
    }
    
    private inner class MonthlyMinHeartRateSeriesStrategy : MinHeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            sleepList: List<Sleep>,
            chartColor: Int
        ): ChartData.Series {
            val sleepTimeRanges = getSleepTimeRanges(sleepList)
            val filteredData = filterNonSleepData(trendDataList, sleepTimeRanges)
            
            val entries = mutableListOf<ChartData.Entry>()
            
            var totalValue = 0f
            var totalDays = 0
            
            val dataByMonth = filteredData.groupBy { 
                val date = it.timeISO8601.toLocalDate()
                date.epochMonth
            }
            
            val months = mutableListOf<Int>()
            var currentDate = from
            while (currentDate <= to) {
                val monthKey = currentDate.epochMonth
                if (!months.contains(monthKey)) {
                    months.add(monthKey)
                }
                currentDate = currentDate.plusMonths(1)
            }
            
            months.sorted().forEach { month ->
                val monthData = dataByMonth[month] ?: emptyList()
                
                monthData.groupBy { it.timeISO8601.toLocalDate() }
                    .mapNotNull { (_, dayData) ->
                        dayData.mapNotNull { getMinHeartRate(it) }.minOrNull()
                    }
                    .minOrNull()
                    ?.inBpm
                    ?.roundToInt()
                    ?.let { minHrInBpm ->
                        entries.add(ChartData.Entry(x = month.toLong(), y = minHrInBpm))
                        totalValue += minHrInBpm
                        totalDays++
                    }
            }
            
            val entriesImmutable = entries.toImmutableList()

            val averageValue = if (totalDays > 0) totalValue / totalDays else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            return ChartData.Series(
                chartType = ChartType.LINE,
                color = chartColor,
                axisRange = ChartData.AxisRange(
                    minX = from.withDayOfMonth(1).epochMonth.toDouble(),
                    maxX = to.withDayOfMonth(1).epochMonth.toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entriesImmutable,
                value = formatBpmValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
                average = averageValue,
            )
        }
    }

    private inner class YearlyMinHeartRateSeriesStrategy : MinHeartRateSeriesStrategy {
        override fun createSeries(
            from: LocalDate,
            to: LocalDate,
            trendDataList: List<TrendData>,
            sleepList: List<Sleep>,
            chartColor: Int
        ): ChartData.Series {
            val sleepTimeRanges = getSleepTimeRanges(sleepList)
            val filteredData = filterNonSleepData(trendDataList, sleepTimeRanges)
            
            val entries = mutableListOf<ChartData.Entry>()
            
            var totalValue = 0f
            var totalYears = 0
            
            val dataByYear = filteredData.groupBy { 
                it.timeISO8601.toLocalDate().year
            }
            
            val years = mutableListOf<Int>()
            var currentDate = from
            while (currentDate <= to) {
                val year = currentDate.year
                if (!years.contains(year)) {
                    years.add(year)
                }
                currentDate = currentDate.plusYears(1)
            }
            
            years.sorted().forEach { year ->
                val yearData = dataByYear[year] ?: emptyList()
                
                yearData.groupBy { it.timeISO8601.toLocalDate() }
                    .mapNotNull { (_, dayData) ->
                        dayData.mapNotNull { getMinHeartRate(it) }.minOrNull()
                    }
                    .minOrNull()
                    ?.inBpm
                    ?.roundToInt()
                    ?.let { minHrInBpm ->
                        entries.add(ChartData.Entry(x = year.toLong(), y = minHrInBpm))
                        totalValue += minHrInBpm
                        totalYears++
                    }
            }
            
            val entriesImmutable = entries.toImmutableList()

            val averageValue = if (totalYears > 0) totalValue / totalYears else 0f
            
            val heartRateValues = entriesImmutable.map { it.y.toFloat() }.filter { it > 0 }
            val minHeartRate = heartRateValues.minOrNull()?.toInt() ?: 0
            val maxHeartRate = heartRateValues.maxOrNull()?.toInt() ?: 0
            val (minY, maxY) = adjustYAxisRangeForLineChart(minHeartRate, maxHeartRate)
            
            return ChartData.Series(
                chartType = ChartType.LINE,
                color = chartColor,
                axisRange = ChartData.AxisRange(
                    minX = from.year.toDouble(),
                    maxX = to.year.toDouble(),
                    minY = minY,
                    maxY = maxY,
                ),
                entries = entriesImmutable,
                value = formatBpmValue(averageValue),
                candlestickEntries = persistentListOf(),
                lineConfig = combineLineChartConfig,
                backgroundRegion = null,
                groupStackBarStyle = null,
                average = averageValue,
            )
        }
    }

    suspend fun getMinimumDaytimeHeartRate(from: LocalDate, to: LocalDate): Int {
        val trendDataList = loadTrendData(from, to)
        val sleepList = loadSleepData(from, to)
        
        val sleepTimeRanges = getSleepTimeRanges(sleepList)
        val dailyMinHrs = calculateDailyMinHrs(trendDataList, sleepTimeRanges, from, to)
        
        return dailyMinHrs.filter { it != NO_DATA_VALUE }.minOrNull() ?: NO_DATA_VALUE
    }

    suspend fun getMinimumDaytimeHeartRateRange(from: LocalDate, to: LocalDate): Pair<Int, Int> {
        val trendDataList = loadTrendData(from, to)
        val sleepList = loadSleepData(from, to)
        
        val sleepTimeRanges = getSleepTimeRanges(sleepList)
        val dailyMinHrs = calculateDailyMinHrs(trendDataList, sleepTimeRanges, from, to)
        
        val validValues = dailyMinHrs.filter { it != NO_DATA_VALUE }
        val minHeartRate = validValues.minOrNull() ?: NO_DATA_VALUE
        val maxHeartRate = validValues.maxOrNull() ?: NO_DATA_VALUE
        
        return Pair(minHeartRate, maxHeartRate)
    }

    private suspend fun loadTrendData(from: LocalDate, to: LocalDate): List<TrendData> {
        val fromMillis = from.atStartOfDay().toEpochMilli()
        val toMillis = to.atStartOfDay().toEpochMilli()
        
        return runSuspendCatching {
            trendDataRepository.fetchTrendDataForDateRange(
                fromTimestamp = fromMillis,
                toTimestamp = toMillis,
                aggregated = false
            ).first()
        }.getOrElse { e ->
            Timber.w(e, "Failed to fetch trend data")
            emptyList()
        }
    }

    private suspend fun loadSleepData(from: LocalDate, to: LocalDate): List<Sleep> {
        return runSuspendCatching {
            fetchSleepUseCase.fetchSleeps(
                from = from,
                to = to
            ).first()
        }.getOrElse { e ->
            Timber.w(e, "Failed to fetch sleep data")
            emptyList()
        }
    }

    suspend fun createChartSeries(
        chartGranularity: ChartGranularity,
        from: LocalDate,
        to: LocalDate,
        trendDataList: List<TrendData>,
        chartColor: Int
    ): ChartData.Series {
        val sleepList = loadSleepData(from, to)
        val strategy = seriesStrategies[chartGranularity]
            ?: throw IllegalArgumentException("Unsupported granularity: $chartGranularity")
            
        return strategy.createSeries(from, to, trendDataList, sleepList, chartColor)
    }
}
