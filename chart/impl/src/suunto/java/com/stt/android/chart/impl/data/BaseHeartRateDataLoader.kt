package com.stt.android.chart.impl.data

import android.content.Context
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import com.stt.android.chart.impl.model.LineChartConfig
import com.stt.android.chart.impl.screen.GoalEditorViewData
import com.stt.android.chart.impl.screen.GoalViewData
import com.stt.android.core.R as CR
import com.stt.android.R as BaseR
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlin.math.ceil
import kotlin.math.roundToInt

internal abstract class BaseHeartRateDataLoader(
    @ApplicationContext protected val appContext: Context
) {

    companion object {
        const val NO_DATA_VALUE = -1
        
        private const val MIN_Y = 40.0
        private const val DEFAULT_MAX_Y = 100.0
        private const val LOW_HEART_RATE_THRESHOLD = 50
        
        private const val GRID_STEP_MULTIPLE = 10.0
        private const val CHART_DIVISIONS = 3.0
        
        private const val CANDLE_CHART_PADDING_FACTOR = 0.2
    }

    protected fun adjustYAxisRangeForLineChart(minHeartRate: Int, maxHeartRate: Int): Pair<Double, Double> {
        val maxY = ceil(maxHeartRate.toDouble() / GRID_STEP_MULTIPLE) * GRID_STEP_MULTIPLE
        var minY = maxY - (GRID_STEP_MULTIPLE * 3)
        if (minY > minHeartRate) {
            minY = maxY - (2 * GRID_STEP_MULTIPLE * 3)
        }
        return Pair(minY, maxY)
    }
    

    protected fun adjustYAxisRangeForCandleChart(minHeartRate: Int, maxHeartRate: Int): Pair<Double, Double> {
        if (maxHeartRate <= LOW_HEART_RATE_THRESHOLD) {
            return Pair(MIN_Y, DEFAULT_MAX_Y)
        }
        
        val adjustedMinHeartRate = minHeartRate.toDouble().coerceAtLeast(MIN_Y)
        
        val dataRange = maxHeartRate.toDouble() - adjustedMinHeartRate
        val padding = dataRange * CANDLE_CHART_PADDING_FACTOR
        
        var paddedMaxY = maxHeartRate.toDouble() + padding
        paddedMaxY = ceil(paddedMaxY / GRID_STEP_MULTIPLE) * GRID_STEP_MULTIPLE
        
        val range = paddedMaxY - MIN_Y
        var step = range / CHART_DIVISIONS
        step = ceil(step / GRID_STEP_MULTIPLE) * GRID_STEP_MULTIPLE
        
        val finalMaxY = MIN_Y + CHART_DIVISIONS * step
        
        return Pair(MIN_Y, finalMaxY)
    }
    

    protected fun formatBpmValue(value: Float): AnnotatedString {
        val unit = appContext.getString(CR.string.bpm)
        return if (value <= 0f) {
            generateWidgetTitle(appContext.getString(BaseR.string.widget_no_data_title), unit)
        } else {
            generateWidgetTitle("${value.roundToInt()}", unit)
        }
    }
    

    private fun generateWidgetTitle(value: String, unit: String): AnnotatedString {
        return buildAnnotatedString {
            append(value)
            withStyle(SpanStyle(fontSize = 12.sp)) {
                append(" ")
                append(unit)
            }
        }
    }
    

    fun formatHighlightData(y: Float?): String = y?.let(::formatBpmValue)?.toString().orEmpty()

    internal open fun loadGoalData(): GoalViewData = GoalViewData.None
    

    internal open fun loadGoalEditorData(): GoalEditorViewData = GoalEditorViewData.None
    

    protected fun getChartColor(): Int =
        appContext.getColor(BaseR.color.dashboard_widget_minimum_heart_rate)

    protected val singleLineChartConfig:LineChartConfig = LineChartConfig(
        isSmoothCurve = false,
        showPoints = true,
        pointSizeDP = 8f,
        isPointFilled = false,
        showAreaFill = true,
        areaAlpha = 0.2f
    )


    protected val combineLineChartConfig:LineChartConfig = LineChartConfig(
        isSmoothCurve = false,
        showPoints = true,
        pointSizeDP = 8f,
        isPointFilled = false,
        showAreaFill = false,
        areaAlpha = null
    )

} 
