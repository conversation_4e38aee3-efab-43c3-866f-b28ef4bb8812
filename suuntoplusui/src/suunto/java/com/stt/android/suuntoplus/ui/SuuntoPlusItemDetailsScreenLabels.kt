package com.stt.android.suuntoplus.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.luminance
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.google.accompanist.flowlayout.FlowRow
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.alertColorDarker
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.confirmation
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workout.ActivityType
import com.stt.android.suuntoplus.SuuntoPlusItemLabel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

@Composable
fun SuuntoPlusItemDetailsScreenLabels(
    labels: ImmutableList<SuuntoPlusItemLabel>,
    modifier: Modifier = Modifier
) {
    FlowRow(
        modifier = modifier,
        mainAxisSpacing = MaterialTheme.spacing.small,
        crossAxisSpacing = MaterialTheme.spacing.small
    ) {
        for (label in labels) {
            SuuntoPlusItemDetailsScreenLabel(label = label)
        }
    }
}

@Composable
fun SuuntoPlusItemDetailsScreenLabel(
    label: SuuntoPlusItemLabel,
    modifier: Modifier = Modifier,
    borderColor: Color = MaterialTheme.colors.cloudyGrey
) {
    val color = label.color
    val iconRes = label.iconRes

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        modifier = modifier
            .heightIn(min = MaterialTheme.spacing.xlarge)
            .then(
                if (color != null) {
                    Modifier
                        .clip(RoundedCornerShape(percent = 50))
                        .background(color)
                } else {
                    Modifier
                        .border(
                            width = 1.dp,
                            color = borderColor,
                            shape = RoundedCornerShape(percent = 100)
                        )
                }
            )
            .padding(
                start = if (iconRes != null) {
                    MaterialTheme.spacing.small
                } else {
                    MaterialTheme.spacing.smaller
                },
                end = MaterialTheme.spacing.smaller,
            )
    ) {
        val contentColor = if (color != null) {
            if (color.luminance() > 0.7f) {
                MaterialTheme.colors.onSurface
            } else {
                MaterialTheme.colors.onPrimary
            }
        } else if (borderColor != MaterialTheme.colors.cloudyGrey) {
            borderColor
        } else {
            Color.Unspecified
        }

        if (iconRes != null) {
            SuuntoActivityIcon(
                iconRes = iconRes,
                tint = contentColor,
                background = Color.Transparent,
                iconSize = MaterialTheme.iconSizes.small,
                modifier = Modifier.sizeIn(
                    maxWidth = MaterialTheme.iconSizes.small,
                    maxHeight = MaterialTheme.iconSizes.small,
                )
            )
        }

        Text(
            text = label.text,
            style = MaterialTheme.typography.body,
            color = contentColor,
            modifier = Modifier
        )
    }
}

@Preview(showBackground = true, backgroundColor = 0xFFFFFFFF)
@Composable
private fun PreviewSuuntoPlusItemDetailsScreenLabels() {
    AppTheme {
        SuuntoPlusItemDetailsScreenLabels(
            labels = persistentListOf(
                SuuntoPlusItemLabel(
                    "Synced",
                    iconRes = R.drawable.installed_in_watch,
                    color = MaterialTheme.colors.confirmation
                ),
                SuuntoPlusItemLabel("My apps", color = MaterialTheme.colors.primary),
                SuuntoPlusItemLabel.forActivityType(activityType = ActivityType.MOUNTAIN_BIKING),
                SuuntoPlusItemLabel("Field test", color = MaterialTheme.colors.alertColorDarker),
                SuuntoPlusItemLabel("Sports app"),
                SuuntoPlusItemLabel("Running", color = MaterialTheme.colors.lightGrey),
                SuuntoPlusItemLabel("Outdoor Exploration"),
                SuuntoPlusItemLabel("Ride"),
                SuuntoPlusItemLabel("Training Physiology")
            )
        )
    }
}
