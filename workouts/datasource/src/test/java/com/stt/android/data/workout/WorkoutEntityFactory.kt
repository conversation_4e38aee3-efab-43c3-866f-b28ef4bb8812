package com.stt.android.data.workout

import com.soy.algorithms.intensity.IntensityZones
import com.soy.algorithms.intensity.IntensityZonesData
import com.soy.algorithms.tss.TSSCalculationMethod
import com.stt.android.domain.Point
import com.stt.android.domain.user.workoutextension.FitnessExtension
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.user.workoutextension.SlopeSkiSummary
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.domain.workouts.tss.TSS
import com.stt.android.logbook.SuuntoLogbookZapp
import com.stt.android.remote.extensions.RemoteIntensityExtensionIntensityZones
import com.stt.android.remote.extensions.RemoteIntensityExtensionPhysiologicalThresholds
import com.stt.android.remote.extensions.RemoteIntensityExtensionPhysiologicalThresholdsValues
import com.stt.android.remote.extensions.RemoteIntensityExtensionZone
import com.stt.android.remote.extensions.RemoteIntensityExtensionZones
import com.stt.android.remote.extensions.RemoteSlopeSkiSummaryExtensionRun
import com.stt.android.remote.extensions.RemoteSlopeSkiSummaryExtensionStatistics
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import com.stt.android.remote.routes.RemotePoint
import com.stt.android.remote.workout.RemoteSyncedWorkout
import com.stt.android.remote.workout.RemoteSyncedWorkoutCadenceData
import com.stt.android.remote.workout.RemoteSyncedWorkoutHrData
import com.stt.android.remote.workout.RemoteTSS
import com.stt.android.remote.workout.RemoteTSSCalculationMethod

/**
 * Factory object to generate entities for testing
 */
internal object WorkoutEntityFactory {
    private var id = 0

    fun makeDomainWorkout(incrementKey: Boolean = false): WorkoutHeader =
        WorkoutHeader(
            id = 0,
            key = if (incrementKey) "key${++id}" else "key",
            totalDistance = 0.0,
            maxSpeed = 0.0,
            activityTypeId = 0,
            avgSpeed = 0.0,
            description = null,
            startPosition = Point(1.0, 2.0, 3.0),
            stopPosition = Point(1.0, 2.0, 3.0),
            centerPosition = Point(1.0, 2.0, 3.0),
            startTime = 0,
            stopTime = 0,
            totalTime = 0.0,
            energyConsumption = 0.0,
            username = "",
            heartRateAverage = 0.0,
            heartRateAvgPercentage = 0.0,
            heartRateMax = 0.0,
            heartRateMaxPercentage = 0.0,
            heartRateUserSetMax = 0.0,
            averageCadence = 0,
            maxCadence = 0,
            pictureCount = 0,
            viewCount = 0,
            commentCount = 0,
            sharingFlags = 0,
            stepCount = 0,
            polyline = null,
            manuallyAdded = false,
            reactionCount = 0,
            totalAscent = 0.0,
            totalDescent = 0.0,
            recoveryTime = 0,
            locallyChanged = false,
            deleted = false,
            seen = false,
            maxAltitude = 0.0,
            minAltitude = 0.0,
            extensionsFetched = false,
            tss = TSS(
                trainingStressScore = 1f,
                calculationMethod = TSSCalculationMethod.POWER,
                intensityFactor = 2f,
                normalizedPower = 3f,
                averageGradeAdjustedPace = 4f
            ),
            suuntoTags = listOf(),
            userTags = listOf()
        )

    fun makeDomainExtensionEntity(type: RemoteWorkoutExtension.Type): WorkoutExtension {
        return when (type) {
            RemoteWorkoutExtension.Type.SUMMARY_EXTENSION -> {
                SummaryExtension(
                    workoutId = 1,
                    pte = 1f,
                    feeling = 2,
                    avgTemperature = 3f,
                    peakEpoc = 4f,
                    avgPower = 5f,
                    avgCadence = 6f,
                    avgSpeed = 7f,
                    ascentTime = 8f,
                    descentTime = 9f,
                    performanceLevel = 10f,
                    recoveryTime = 0,
                    ascent = 0.0,
                    descent = 0.0,
                    deviceHardwareVersion = null,
                    deviceSoftwareVersion = null,
                    productType = null,
                    displayName = null,
                    deviceName = null,
                    deviceSerialNumber = null,
                    deviceManufacturer = null,
                    exerciseId = null,
                    zapps = listOf(
                        SuuntoLogbookZapp(
                            id = "zapp",
                            authorId = null,
                            externalId = null,
                            name = "a name",
                            summaryOutputs = listOf(
                                SuuntoLogbookZapp.SummaryOutput(
                                    "format",
                                    "id",
                                    "summaryOutput",
                                    "postfix",
                                    1.0
                                )
                            ),
                            channels = listOf(
                                SuuntoLogbookZapp.ZappChannel(
                                    channelId = 0,
                                    format = "format",
                                    inverted = true,
                                    name = "name",
                                    variableId = "id",
                                )
                            )
                        )
                    ),
                    repetitionCount = 0,
                    maxCadence = 0f,
                    avgStrideLength = 0f,
                    fatConsumption = 0,
                    carbohydrateConsumption = 0,
                    avgGroundContactTime = 0f,
                    avgVerticalOscillation = 0f,
                    avgLeftGroundContactBalance = 0f,
                    avgRightGroundContactBalance = 0f,
                    lacticThHr = null,
                    lacticThPace = null,
                    avgAscentSpeed = 0f,
                    maxAscentSpeed = 0f,
                    avgDescentSpeed = 0f,
                    maxDescentSpeed = 0f,
                    avgDistancePerStroke = 0f
                )
            }

            RemoteWorkoutExtension.Type.INTENSITY_EXTENSION -> {
                IntensityExtension(
                    1,
                    IntensityZones(
                        IntensityZonesData(
                            1f,
                            2f,
                            3f,
                            4f,
                            5f,
                            7f,
                            8f,
                            9f,
                            10f
                        ),
                        IntensityZonesData(
                            1f,
                            2f,
                            3f,
                            4f,
                            5f,
                            7f,
                            8f,
                            9f,
                            10f
                        ),
                        IntensityZonesData(
                            1f,
                            2f,
                            3f,
                            4f,
                            5f,
                            7f,
                            8f,
                            9f,
                            10f
                        ),
                    )
                )
            }

            RemoteWorkoutExtension.Type.FITNESS_EXTENSION -> {
                FitnessExtension(
                    3,
                    1,
                    2.0f,
                    null,
                )
            }

            RemoteWorkoutExtension.Type.SKI_EXTENSION -> {
                SlopeSkiSummary(
                    1,
                    1,
                    2L,
                    3.0,
                    4.0,
                    5.0
                )
            }

            else -> throw IllegalArgumentException("$type not supported")
        }
    }

    fun makeRemoteExtensionEntity(type: RemoteWorkoutExtension.Type): RemoteWorkoutExtension {
        return when (type) {
            RemoteWorkoutExtension.Type.SUMMARY_EXTENSION -> {
                RemoteWorkoutExtension.RemoteSummaryExtension(
                    pte = 1f,
                    feeling = 2,
                    avgTemperature = 3f,
                    peakEpoc = 4f,
                    avgPower = 5f,
                    avgCadence = 6f,
                    avgSpeed = 7f,
                    ascentTime = 8f,
                    descentTime = 9f,
                    performanceLevel = 10f,
                    recoveryTime = null,
                    ascent = null,
                    descent = null,
                    gear = null,
                    exerciseId = null,
                    zapps = listOf(
                        RemoteWorkoutExtension.RemoteZapp(
                            id = "zapp",
                            authorId = null,
                            externalId = null,
                            name = "a name",
                            summaryOutputs = listOf(
                                RemoteWorkoutExtension.RemoteSummaryOutput(
                                    "format",
                                    "id",
                                    "summaryOutput",
                                    "postfix",
                                    1.0
                                )
                            ),
                            channels = listOf(
                                RemoteWorkoutExtension.RemoteZappChannel(
                                    channelId = 0,
                                    format = "format",
                                    inverted = true,
                                    name = "name",
                                    variableId = "id",
                                )
                            )
                        )
                    ),
                    type = RemoteWorkoutExtension.Type.SUMMARY_EXTENSION.value,
                    maxCadence = 0f,
                    repetitionCount = 0,
                    avgStrideLength = 0f,
                    fatConsumption = 0,
                    carbohydrateConsumption = 0,
                    avgGroundContactTime = 0f,
                    avgVerticalOscillation = 0f,
                    avgLeftGroundContactBalance = 0f,
                    avgRightGroundContactBalance = 0f,
                    lacticThHr = null,
                    lacticThPace = null,
                    avgAscentSpeed = 0f,
                    maxAscentSpeed = 0f,
                    avgDescentSpeed = 0f,
                    maxDescentSpeed = 0f,
                    avgDistancePerStroke = 0f
                )
            }

            RemoteWorkoutExtension.Type.INTENSITY_EXTENSION -> {
                RemoteWorkoutExtension.RemoteIntensityExtension(
                    RemoteIntensityExtensionIntensityZones(
                        RemoteIntensityExtensionZones(
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            )
                        ),
                        RemoteIntensityExtensionZones(
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            )
                        ),
                        RemoteIntensityExtensionZones(
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            ),
                            RemoteIntensityExtensionZone(
                                1f,
                                2f
                            )
                        )
                    ),
                    RemoteIntensityExtensionPhysiologicalThresholds(
                        RemoteIntensityExtensionPhysiologicalThresholdsValues(
                            1,
                            2f,
                            3f
                        ),
                        RemoteIntensityExtensionPhysiologicalThresholdsValues(
                            1,
                            2f,
                            3f
                        )
                    )
                )
            }

            RemoteWorkoutExtension.Type.FITNESS_EXTENSION -> {
                RemoteWorkoutExtension.RemoteFitnessExtension(
                    1,
                    2.0f,
                    null,
                )
            }

            RemoteWorkoutExtension.Type.SKI_EXTENSION -> {
                RemoteWorkoutExtension.RemoteSlopeSkiSummaryExtension(
                    RemoteSlopeSkiSummaryExtensionStatistics(
                        1,
                        2L,
                        3.0,
                        4.0,
                        5.0
                    ),
                    listOf(
                        RemoteSlopeSkiSummaryExtensionRun(
                            1L,
                            2L,
                            3.0,
                            4.0
                        )
                    )
                )
            }

            else -> throw IllegalArgumentException("$type not supported")
        }
    }

    private fun makeRemoteSyncedWorkoutPoint() = RemotePoint(1.0, 3.0)

    private fun makeRemoteSyncedWorkoutHrData() = RemoteSyncedWorkoutHrData(0, 1, 2, 3, 4, 5)

    private fun makeRemoteSyncedWorkoutCadenceData() = RemoteSyncedWorkoutCadenceData(0, 1)

    fun makeRemoteSyncedWorkout(): RemoteSyncedWorkout {
        val tss = RemoteTSS(
            trainingStressScore = 1f,
            calculationMethod = RemoteTSSCalculationMethod.POWER,
            intensityFactor = 2f,
            normalizedPower = 3f,
            averageGradeAdjustedPace = 4f
        )
        return RemoteSyncedWorkout(
            workoutKey = "workoutKey",
            totalDistance = 1.0,
            maxSpeed = 2.0,
            activityId = 3,
            avgSpeed = 4.0,
            description = "description",
            startPosition = makeRemoteSyncedWorkoutPoint(),
            stopPosition = makeRemoteSyncedWorkoutPoint(),
            centerPosition = makeRemoteSyncedWorkoutPoint(),
            startTime = 1L,
            stopTime = 2L,
            totalTime = 3.0,
            energyConsumption = 4.0,
            username = "username",
            hrdata = makeRemoteSyncedWorkoutHrData(),
            cadence = makeRemoteSyncedWorkoutCadenceData(),
            viewCount = 5,
            sharingFlags = 6,
            stepCount = 7,
            manuallyAdded = false,
            polyline = null,
            pictureCount = 8,
            totalAscent = 9.0,
            totalDescent = 10.0,
            recoveryTime = 1L,
            comments = null,
            photos = null,
            reactions = null,
            videos = null,
            extensions = null,
            rankings = null,
            maxAltitude = 10.0,
            minAltitude = 11.0,
            tss = tss,
            tssList = listOf(tss),
            suuntoTags = listOf(),
            userTags = listOf(),
            zoneSense = null,
            avgAscentSpeed = 10.0,
            maxAscentSpeed = 10.0,
            avgDescentSpeed = 10.0,
            maxDescentSpeed = 10.0,
            avgDistancePerStroke = 10.0
        )
    }
}
