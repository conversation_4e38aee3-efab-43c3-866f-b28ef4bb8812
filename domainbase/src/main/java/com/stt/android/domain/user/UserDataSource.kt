package com.stt.android.domain.user

interface UserDataSource {
    suspend fun findLocalUser(username: String): User?
    suspend fun findLocalUsers(usernames: Set<String>): Map<String, User>
    suspend fun fetchUserFromRemote(username: String): User
    suspend fun getAllOtherUsers(): List<User>
    suspend fun getAllOtherUsernames(): List<String>
    suspend fun replaceOtherUsers(users: List<User>)
    suspend fun getLatestGearFromRemote(username: String): List<UserGearLatest>
}
