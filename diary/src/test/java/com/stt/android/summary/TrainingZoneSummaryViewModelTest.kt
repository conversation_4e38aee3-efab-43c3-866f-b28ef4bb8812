package com.stt.android.summary

import app.cash.turbine.test
import com.google.common.truth.Truth.assertThat
import com.stt.android.chart.impl.usecases.FormatChartHighlightDateTimeUseCase
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.diary.graph.data.SelectedPrimaryGraphLiveData
import com.stt.android.diary.graph.data.SelectedSecondaryGraphLiveData
import com.stt.android.diary.summary.TimeFrameSummaryWorkoutsCalculator
import com.stt.android.diary.summary.TrainingZoneSummaryAnalytics
import com.stt.android.diary.summary.TrainingZoneSummaryColumn
import com.stt.android.diary.summary.TrainingZoneSummaryDataAggregatorUseCase
import com.stt.android.diary.summary.TrainingZoneSummaryFormatter
import com.stt.android.diary.summary.TrainingZoneSummaryGetRowsUseCase
import com.stt.android.diary.summary.TrainingZoneSummaryGetSummaryColumnsUseCase
import com.stt.android.diary.summary.TrainingZoneSummaryGetTotalRowUseCase
import com.stt.android.diary.summary.TrainingZoneSummaryGetWorkoutsUseCase
import com.stt.android.diary.summary.TrainingZoneSummaryGrouping
import com.stt.android.diary.summary.TrainingZoneSummaryGroupingCalculatorUseCase
import com.stt.android.diary.summary.TrainingZoneSummarySelectionsRepository
import com.stt.android.diary.summary.TrainingZoneSummarySortingOrder
import com.stt.android.diary.summary.TrainingZoneSummarySortingUseCase
import com.stt.android.diary.summary.TrainingZoneSummaryViewModel
import com.stt.android.diary.trainingv2.summary.CreateTrainingGraphChartDataUseCase
import com.stt.android.diary.trainingv2.summary.TrainingGraphEntryYFormatter
import com.stt.android.diary.trainingv2.summary.TrainingGraphType
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.tags.UserTagsRepository
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.UserSettings
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.testutils.NewCoroutinesTestRule
import com.stt.android.utils.CalendarProvider
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.whenever
import java.time.Clock
import java.time.DayOfWeek
import java.time.LocalDate

@RunWith(MockitoJUnitRunner::class)
class TrainingZoneSummaryViewModelTest {
    @get:Rule
    val coroutinesTestRule = NewCoroutinesTestRule()

    private val coroutinesDispatchers = object : CoroutinesDispatchers {
        override val main = coroutinesTestRule.testDispatcher
        override val computation = coroutinesTestRule.testDispatcher
        override val io = coroutinesTestRule.testDispatcher
    }

    @Mock
    lateinit var userSettingsController: UserSettingsController

    @Mock
    lateinit var userSettings: UserSettings

    @Mock
    lateinit var currentUserController: CurrentUserController

    @Mock
    lateinit var workoutDataSource: WorkoutDataSource

    @Mock
    lateinit var trainingZoneSummarySelectionsRepository: TrainingZoneSummarySelectionsRepository

    @Mock
    lateinit var trainingZoneSummaryAnalytics: TrainingZoneSummaryAnalytics

    @Mock
    lateinit var clock: Clock

    @Mock
    lateinit var calendarProvider: CalendarProvider

    @Mock
    lateinit var isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase

    @Mock
    lateinit var selectedPrimaryGraphLiveData: SelectedPrimaryGraphLiveData

    @Mock
    lateinit var selectedSecondaryGraphLiveData: SelectedSecondaryGraphLiveData

    @Mock
    lateinit var userTagsRepository: UserTagsRepository

    @Mock
    lateinit var createTrainingGraphChartDataUseCase: CreateTrainingGraphChartDataUseCase

    @Mock
    lateinit var trainingGraphEntryYFormatter: TrainingGraphEntryYFormatter

    @Mock
    lateinit var formatChartHighlightDateTimeUseCase: FormatChartHighlightDateTimeUseCase

    @Mock
    lateinit var timeFrameSummaryWorkoutsCalculator: TimeFrameSummaryWorkoutsCalculator

    private val trainingZoneSummaryFormatter: TrainingZoneSummaryFormatter =
        TrainingZoneSummaryFormatterFake()

    private val aggregator = TrainingZoneSummaryDataAggregatorUseCase()

    private lateinit var trainingZoneSummaryGroupingCalculatorUseCase: TrainingZoneSummaryGroupingCalculatorUseCase

    private lateinit var trainingZoneSummaryViewModel: TrainingZoneSummaryViewModel

    private fun initViewModel() {
        `when`(workoutDataSource.hasWorkouts(any())).thenReturn(flowOf(true))
        `when`(isSubscribedToPremiumUseCase.invoke()).thenReturn(flowOf(true))

        trainingZoneSummaryGroupingCalculatorUseCase = TrainingZoneSummaryGroupingCalculatorUseCase(
            aggregator = aggregator,
            coroutinesDispatchers = coroutinesDispatchers,
            localDate = LocalDate.now(),
            trainingZoneSummaryFormatter = trainingZoneSummaryFormatter,
        )
        trainingZoneSummaryViewModel = TrainingZoneSummaryViewModel(
            trainingZoneSummaryGroupingCalculatorUseCase = trainingZoneSummaryGroupingCalculatorUseCase,
            selectionsRepository = trainingZoneSummarySelectionsRepository,
            formatter = trainingZoneSummaryFormatter,
            workoutDataSource = workoutDataSource,
            currentUserController = currentUserController,
            coroutinesDispatchers = coroutinesDispatchers,
            userSettingsController = userSettingsController,
            trainingZoneSummaryAnalytics = trainingZoneSummaryAnalytics,
            sortingUseCase = TrainingZoneSummarySortingUseCase(),
            getWorkoutsUseCase = TrainingZoneSummaryGetWorkoutsUseCase(
                workoutDataSource = workoutDataSource,
                currentUserController = currentUserController,
                measurementUnit = MeasurementUnit.METRIC
            ),
            getRowsUseCase = TrainingZoneSummaryGetRowsUseCase(
                formatter = TrainingZoneSummaryFormatterFake()
            ),
            getTotalRowUseCase = TrainingZoneSummaryGetTotalRowUseCase(
                trainingZoneSummaryGroupingCalculatorUseCase = TrainingZoneSummaryGroupingCalculatorUseCase(
                    aggregator = TrainingZoneSummaryDataAggregatorUseCase(),
                    coroutinesDispatchers = coroutinesDispatchers,
                    localDate = LocalDate.now(),
                    trainingZoneSummaryFormatter = trainingZoneSummaryFormatter,
                )
            ),
            getColumnsUseCase = TrainingZoneSummaryGetSummaryColumnsUseCase(),
            clock = clock,
            calendarProvider = calendarProvider,
            selectedPrimaryGraphLiveData = selectedPrimaryGraphLiveData,
            selectedSecondaryGraphLiveData = selectedSecondaryGraphLiveData,
            isSubscribedToPremiumUseCase = isSubscribedToPremiumUseCase,
            userTagsRepository = userTagsRepository,
            createTrainingGraphChartDataUseCase = createTrainingGraphChartDataUseCase,
            trainingGraphEntryYFormatter = trainingGraphEntryYFormatter,
            formatChartHighlightDateTimeUseCase = formatChartHighlightDateTimeUseCase,
            timeFrameSummaryWorkoutsCalculator = timeFrameSummaryWorkoutsCalculator,
        )
    }

    @Before
    fun setup() {
        whenever(currentUserController.username).thenReturn("username")
        whenever(userSettingsController.settings).thenReturn(userSettings)
        whenever(userSettingsController.settings.firstDayOfTheWeek).thenReturn(DayOfWeek.MONDAY)
        whenever(userSettingsController.settings.measurementUnit).thenReturn(MeasurementUnit.METRIC)
        whenever(trainingZoneSummarySelectionsRepository.defaultGrouping).thenReturn(
            TrainingZoneSummarySelectionsRepository.DEFAULT_FILTER_GROUPING
        )
        whenever(trainingZoneSummarySelectionsRepository.defaultDistance).thenReturn(
            TrainingZoneSummarySelectionsRepository.DEFAULT_FILTER_DISTANCE
        )
        whenever(trainingZoneSummarySelectionsRepository.defaultLayoutType).thenReturn(
            TrainingZoneSummarySelectionsRepository.DEFAULT_LAYOUT_TYPE
        )
        whenever(trainingZoneSummarySelectionsRepository.defaultSortingColumn).thenReturn(
            TrainingZoneSummarySelectionsRepository.DEFAULT_SORTING_COLUMN
        )
        whenever(trainingZoneSummarySelectionsRepository.defaultSortingOrder).thenReturn(
            TrainingZoneSummarySelectionsRepository.DEFAULT_SORTING_ORDER
        )
        whenever(trainingZoneSummarySelectionsRepository.hiddenSummaryColumnsFlow()).thenReturn(
            flowOf(emptyList())
        )
        whenever(trainingZoneSummarySelectionsRepository.sortedSummaryColumnsFlow()).thenReturn(
            flowOf(TrainingZoneSummaryColumn.entries.toList())
        )
        whenever(trainingZoneSummarySelectionsRepository.primaryTrainingGraphType).thenReturn(
            TrainingGraphType.DURATION
        )
        whenever(trainingZoneSummarySelectionsRepository.secondaryTrainingGraphType).thenReturn(
            TrainingGraphType.DISTANCE
        )
        whenever(trainingZoneSummarySelectionsRepository.trainingGraphTimeRange).thenReturn(
            GraphTimeRange.CURRENT_WEEK
        )
    }

    @Test
    fun whenThereIsOneTableItem_totalIsNotAddedToTheListOfRows() = runTest {
        `when`(
            workoutDataSource.getAllWorkoutsForSummary(
                any(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
            )
        ).thenReturn(
            flowOf(listOf(running1))
        )

        initViewModel()

        trainingZoneSummaryViewModel.summaryViewState.test {
            awaitItem() // default value
            val item = awaitItem()
            assertThat(item.rows.size).isEqualTo(1) // Only one item
            assertThat(item.hasTotalsRowItem).isFalse()
        }
    }

    @Test
    fun whenThereIsMoreThanOneTableItem_totalIsAddedToTheListOfRows() = runTest {
        whenever(
            workoutDataSource.getAllWorkoutsForSummary(
                any(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
            )
        ).thenReturn(
            flowOf(
                listOf(
                    running1.copy(
                        startTime = LocalDate.of(2022, 2, 2).atStartOfDay().toEpochMilli()
                    ),
                    walking1.copy(
                        startTime = LocalDate.of(2021, 2, 2).atStartOfDay().toEpochMilli()
                    ),
                    gym1.copy(startTime = LocalDate.of(2020, 2, 2).atStartOfDay().toEpochMilli()),
                )
            )
        )


        initViewModel()

        trainingZoneSummaryViewModel.summaryViewState.test {
            awaitItem() // default value
            val item = awaitItem()
            assertThat(item.rows.size).isEqualTo(4) // 3 different row per workout + total
            assertThat(item.hasTotalsRowItem).isTrue()
        }
    }

    @Test
    fun whenUserSortByHeader_DescendingIsDefaultValueForNewClickedColumn() = runTest {
        val workouts = listOf(
            running1.copy(
                totalDistance = 5000.0,
                startTime = LocalDate.of(2022, 2, 2).atStartOfDay().toEpochMilli()
            ),
            walking1.copy(
                totalDistance = 2000.0,
                startTime = LocalDate.of(2023, 2, 2).atStartOfDay().toEpochMilli()
            ),
            running2.copy(
                totalDistance = 7000.0,
                startTime = LocalDate.of(2022, 10, 20).atStartOfDay().toEpochMilli()
            ),
        )
        whenever(
            workoutDataSource.getAllWorkoutsForSummary(
                any(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
            )
        ).thenReturn(
            flowOf(workouts)
        )

        initViewModel()

        trainingZoneSummaryViewModel.updateGrouping(TrainingZoneSummaryGrouping.BY_ACTIVITY) // By activity is easier to test
        trainingZoneSummaryViewModel.summaryViewState.test {
            awaitItem() // default value
            val item = awaitItem()
            assertThat(item.rows[1].workouts).containsExactly(workouts[1])
            assertThat(item.rows[2].workouts).containsExactly(workouts[2])
            assertThat(item.rows[3].workouts).containsExactly(workouts[0])
        }

        trainingZoneSummaryViewModel.mainSorting.test {
            val item = awaitItem()
            assertThat(item.selectedColumn).isEqualTo(TrainingZoneSummaryColumn.DATE)
            assertThat(item.selectedOrder).isEqualTo(TrainingZoneSummarySortingOrder.DESCENDING)
        }

        trainingZoneSummaryViewModel.setMainSorting(TrainingZoneSummaryColumn.DATE)
        trainingZoneSummaryViewModel.summaryViewState.test {
            val item = awaitItem()
            assertThat(item.rows[1].workouts).containsExactly(workouts[0])
            assertThat(item.rows[2].workouts).containsExactly(workouts[2])
            assertThat(item.rows[3].workouts).containsExactly(workouts[1])
        }

        trainingZoneSummaryViewModel.mainSorting.test {
            val item = awaitItem()
            assertThat(item.selectedColumn).isEqualTo(TrainingZoneSummaryColumn.DATE)
            assertThat(item.selectedOrder).isEqualTo(TrainingZoneSummarySortingOrder.ASCENDING)
        }

        trainingZoneSummaryViewModel.setMainSorting(TrainingZoneSummaryColumn.DATE)
        trainingZoneSummaryViewModel.summaryViewState.test {
            val item = awaitItem()
            assertThat(item.rows[1].workouts).containsExactly(workouts[1])
            assertThat(item.rows[2].workouts).containsExactly(workouts[2])
            assertThat(item.rows[3].workouts).containsExactly(workouts[0])
        }

        trainingZoneSummaryViewModel.mainSorting.test {
            val item = awaitItem()
            assertThat(item.selectedColumn).isEqualTo(TrainingZoneSummaryColumn.DATE)
            assertThat(item.selectedOrder).isEqualTo(TrainingZoneSummarySortingOrder.DESCENDING)
        }

        trainingZoneSummaryViewModel.setMainSorting(TrainingZoneSummaryColumn.DISTANCE)
        trainingZoneSummaryViewModel.summaryViewState.test {
            val item = awaitItem()
            assertThat(item.rows[1].workouts).containsExactly(workouts[2])
            assertThat(item.rows[2].workouts).containsExactly(workouts[0])
            assertThat(item.rows[3].workouts).containsExactly(workouts[1])
        }

        trainingZoneSummaryViewModel.mainSorting.test {
            val item = awaitItem()
            assertThat(item.selectedColumn).isEqualTo(TrainingZoneSummaryColumn.DISTANCE)
            assertThat(item.selectedOrder).isEqualTo(TrainingZoneSummarySortingOrder.DESCENDING)
        }
    }

    @Test
    fun whenOnlySportsWithoutDistanceAreSelected_distanceShouldBeIgnored() = runTest {
        val workouts = listOf(
            running1.copy(totalDistance = 5000.0),
            walking1.copy(totalDistance = 2000.0),
            gym1.copy(startTime = 2000),
            gym2.copy(startTime = 1000)
        )
        whenever(
            workoutDataSource.getAllWorkoutsForSummary(
                any(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
            )
        ).thenReturn(
            flowOf(workouts)
        )

        initViewModel()

        trainingZoneSummaryViewModel.summaryViewState.test {
            trainingZoneSummaryViewModel.updateGrouping(TrainingZoneSummaryGrouping.BY_ACTIVITY) // By activity is easier to test
            awaitItem() // default value
            val item = awaitItem()
            assertThat(item.rows[1].workouts).containsExactly(workouts[2])
            assertThat(item.rows[2].workouts).containsExactly(workouts[3])
        }
    }

    @Test
    fun whenSelectedSportsListIsEmpty_whichMeansAllSportsAreSelected_disableDistanceFilterFlagMustBeTrue() =
        runTest {
            whenever(
                workoutDataSource.getAllWorkoutsForSummary(
                    any(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                )
            ).thenReturn(
                flowOf(emptyList())
            )
            initViewModel()
            trainingZoneSummaryViewModel.updateSports(emptyList())

            trainingZoneSummaryViewModel.filterUiState.test {
                assertThat(expectMostRecentItem().isDistanceSupported).isTrue()
            }
        }

    @Test
    fun whenSportWithNoDistanceIsSelected_disableDistanceFilterFlagMustBeFalse() =
        runTest {
            whenever(
                workoutDataSource.getAllWorkoutsForSummary(
                    any(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                )
            ).thenReturn(
                flowOf(emptyList())
            )
            initViewModel()
            trainingZoneSummaryViewModel.updateSports(listOf(CoreActivityType.GYM))

            trainingZoneSummaryViewModel.filterUiState.test {
                assertThat(expectMostRecentItem().isDistanceSupported).isFalse()
            }
        }

    @Test
    fun whenMixedSportWithNoDistanceAndDistanceAreSelected_disableDistanceFilterFlagMustBeFalse() =
        runTest {
            whenever(
                workoutDataSource.getAllWorkoutsForSummary(
                    any(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                    anyOrNull(),
                )
            ).thenReturn(
                flowOf(emptyList())
            )
            initViewModel()
            trainingZoneSummaryViewModel.updateSports(
                listOf(
                    CoreActivityType.GYM,
                    CoreActivityType.RUNNING
                )
            )

            trainingZoneSummaryViewModel.filterUiState.test {
                assertThat(expectMostRecentItem().isDistanceSupported).isTrue()
            }
        }
}
