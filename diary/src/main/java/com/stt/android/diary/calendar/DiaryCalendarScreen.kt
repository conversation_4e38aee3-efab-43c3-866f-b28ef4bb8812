package com.stt.android.diary.calendar

import android.os.Bundle
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.fragment.compose.AndroidFragment
import com.stt.android.compose.component.SuuntoExtendedFloatingActionButton
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.calendar.ext.calendarTabIndex
import com.stt.android.diary.calendar.ext.toCalendarTimeRange
import com.stt.android.diary.common.TimeRangeSegmentedControl
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.home.diary.diarycalendar.CalendarContainerViewModel
import com.stt.android.home.diary.diarycalendar.DiaryCalendarConstants
import com.stt.android.home.diary.diarycalendar.DiaryCalendarPagerNavigation
import com.stt.android.home.diary.diarycalendar.DiaryCalendarPagerNavigationOwner
import com.stt.android.home.diary.diarycalendar.last30days.DiaryCalendarLast30DaysFragment
import com.stt.android.home.diary.diarycalendar.month.DiaryCalendarMonthPagerFragment
import com.stt.android.home.diary.diarycalendar.week.DiaryCalendarWeekPagerFragment
import com.stt.android.home.diary.diarycalendar.year.DiaryCalendarYearPagerFragment
import java.time.LocalDate
import com.stt.android.R as BR

@Composable
internal fun DiaryCalendarScreen(
    viewModel: CalendarContainerViewModel,
    displayMode: Int,
    modifier: Modifier = Modifier,
) {
    val selectedTab by viewModel.selectedTab.collectAsState()
    val timeRange = selectedTab?.first.toCalendarTimeRange()

    var prevTimeRange by remember { mutableStateOf<GraphTimeRange?>(null) }
    var pagerNavigation by remember { mutableStateOf(DiaryCalendarPagerNavigation.DUMMY) }

    val navigationState by pagerNavigation.navigationState.collectAsState()

    Box(modifier = modifier) {
        Column(modifier = Modifier.narrowContentWithBgColors(backgroundColor = MaterialTheme.colorScheme.surface)) {
            TimeRangeSegmentedControl(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        horizontal = MaterialTheme.spacing.medium,
                        vertical = MaterialTheme.spacing.small,
                    ),
                timeRange = timeRange,
                timeRanges = listOf(
                    GraphTimeRange.CURRENT_WEEK,
                    GraphTimeRange.CURRENT_MONTH,
                    GraphTimeRange.ONE_YEAR,
                    GraphTimeRange.THIRTY_DAYS,
                ),
                moreTimeRanges = emptyList(),
                onTimeRangeToggled = {
                    prevTimeRange = timeRange
                    viewModel.selectedTabPosition = it.calendarTabIndex
                },
            )
            AnimatedContent(
                targetState = timeRange,
                transitionSpec = { fadeIn().togetherWith(fadeOut()) },
            ) {
                val extras = selectedTab?.second
                when (it) {
                    GraphTimeRange.CURRENT_WEEK -> AndroidFragment<DiaryCalendarWeekPagerFragment>(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f),
                        arguments = viewModel.buildArguments(
                            it,
                            prevTimeRange,
                            displayMode,
                            extras
                        ),
                        onUpdate = {
                            (it as? DiaryCalendarPagerNavigationOwner)?.let {
                                pagerNavigation = it.pagerNavigation
                            }
                        },
                    )

                    GraphTimeRange.CURRENT_MONTH -> AndroidFragment<DiaryCalendarMonthPagerFragment>(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f),
                        arguments = viewModel.buildArguments(
                            it,
                            prevTimeRange,
                            displayMode,
                            extras
                        ),
                        onUpdate = {
                            (it as? DiaryCalendarPagerNavigationOwner)?.let {
                                pagerNavigation = it.pagerNavigation
                            }
                        },
                    )

                    GraphTimeRange.ONE_YEAR -> AndroidFragment<DiaryCalendarYearPagerFragment>(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f),
                        arguments = viewModel.buildArguments(
                            it,
                            prevTimeRange,
                            displayMode,
                            extras
                        ),
                        onUpdate = {
                            (it as? DiaryCalendarPagerNavigationOwner)?.let {
                                pagerNavigation = it.pagerNavigation
                            }
                        },
                    )

                    GraphTimeRange.THIRTY_DAYS -> AndroidFragment<DiaryCalendarLast30DaysFragment>(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f),
                        arguments = viewModel.buildArguments(
                            it,
                            prevTimeRange,
                            displayMode,
                            extras
                        ),
                        onUpdate = {
                            pagerNavigation = DiaryCalendarPagerNavigation.DUMMY
                        },
                    )

                    else -> throw error("Unsupported time range: $it")
                }
            }
        }
        AnimatedVisibility(
            visible = !navigationState.isDefaultItem,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = MaterialTheme.spacing.xxxlarge),
            enter = slideInVertically(initialOffsetY = { it * 2 }),
            exit = slideOutVertically(targetOffsetY = { it * 2 }),
        ) {
            SuuntoExtendedFloatingActionButton(
                icon = BR.drawable.ic_revert,
                text = BR.string.back_to_current_button,
                onClick = pagerNavigation::backToDefault,
            )
        }
    }
}

private fun CalendarContainerViewModel.buildArguments(
    target: GraphTimeRange,
    previous: GraphTimeRange?,
    displayMode: Int,
    extras: Bundle?,
) = Bundle().also { args ->
    when (target) {
        GraphTimeRange.CURRENT_WEEK -> when (previous) {
            GraphTimeRange.CURRENT_MONTH -> {
                args.putString(
                    DiaryCalendarWeekPagerFragment.ARG_START_OF_WEEK,
                    getStartOfWeekFromMonthToWeek().toString(),
                )
            }

            GraphTimeRange.ONE_YEAR -> {
                args.putString(
                    DiaryCalendarWeekPagerFragment.ARG_START_OF_WEEK,
                    getStartOfWeekFromYearToWeek().toString(),
                )
            }

            GraphTimeRange.THIRTY_DAYS -> {
                args.putString(
                    DiaryCalendarWeekPagerFragment.ARG_START_OF_WEEK,
                    getStartOfWeekFromLast30DaysToWeek().toString(),
                )
            }

            else -> Unit
        }

        GraphTimeRange.CURRENT_MONTH -> when (previous) {
            GraphTimeRange.CURRENT_WEEK -> {
                args.putString(
                    DiaryCalendarMonthPagerFragment.ARG_MONTH,
                    getStartOfMonthFromWeekToMonth().toString(),
                )
            }

            GraphTimeRange.ONE_YEAR -> {
                args.putString(
                    DiaryCalendarMonthPagerFragment.ARG_MONTH,
                    getStartOfMonthFromYearToMonth().toString(),
                )
            }

            GraphTimeRange.THIRTY_DAYS -> {
                args.putString(
                    DiaryCalendarMonthPagerFragment.ARG_MONTH,
                    LocalDate.now().month.toString(),
                )
            }

            else -> Unit
        }

        GraphTimeRange.ONE_YEAR -> when (previous) {
            GraphTimeRange.CURRENT_WEEK -> {
                args.putString(
                    DiaryCalendarYearPagerFragment.ARG_YEAR,
                    getYearFromWeekToYear().toString(),
                )
            }

            GraphTimeRange.CURRENT_MONTH -> {
                args.putString(
                    DiaryCalendarYearPagerFragment.ARG_YEAR,
                    getYearFromMonthToYear().toString(),
                )
            }

            GraphTimeRange.THIRTY_DAYS -> {
                args.putString(
                    DiaryCalendarYearPagerFragment.ARG_YEAR,
                    LocalDate.now().year.toString(),
                )
            }

            else -> Unit
        }

        else -> Unit
    }

    args.putInt(DiaryCalendarConstants.DIARY_CALENDAR_DISPLAY_MODE, displayMode)

    // always do this in the end to override the previous args
    extras?.let { args.putAll(it) }
}
