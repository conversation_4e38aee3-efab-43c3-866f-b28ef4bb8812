package com.stt.android.diary.statistics

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.stt.android.compose.ui.R
import com.stt.android.compose.widgets.ActionBottomSheet
import com.stt.android.diary.trainingv2.composables.GraphDataTypeListContent
import com.stt.android.diary.trainingv2.composables.TrainingGraph
import com.stt.android.diary.trainingv2.composables.TrainingGraphDataTypeSelection
import com.stt.android.diary.trainingv2.summary.TrainingGraphPage
import com.stt.android.diary.trainingv2.summary.TrainingGraphType
import com.stt.android.diary.trainingv2.summary.TrainingGraphUiState
import java.util.Locale

@Composable
internal fun StatisticsGraphScreen(
    graphUiState: TrainingGraphUiState,
    onPrimaryGraphTypeSelected: (TrainingGraphType) -> Unit,
    onSecondaryGraphTypeSelected: (TrainingGraphType) -> Unit,
    chartHighlightedEvent: (Long?) -> Unit,
    onChartPositionInRootYChanged: (Float) -> Unit,
    onGraphPageChanged: (Int, TrainingGraphPage) -> Unit,
    showReturnToCurrent: (Boolean) -> Unit,
    graphPagerState: PagerState,
    modifier: Modifier = Modifier,
) {
    var showPrimaryGraphTypesPicker by remember { mutableStateOf(false) }
    var showSecondaryGraphTypesPicker by remember { mutableStateOf(false) }
    val graphPage by remember(graphPagerState.currentPage, graphUiState.graphPages) {
        derivedStateOf { graphUiState.graphPages[graphPagerState.currentPage] }
    }
    val currentShowReturnToCurrent by rememberUpdatedState(showReturnToCurrent)
    LaunchedEffect(graphPagerState) {
        snapshotFlow { graphPagerState.currentPage }
            .collect { pageIndex ->
                onGraphPageChanged(pageIndex, graphUiState.graphPages[pageIndex])
                currentShowReturnToCurrent(pageIndex < graphPagerState.pageCount - 1)
            }
    }
    var chartEntrySelected by remember { mutableStateOf(false) }

    Column(
        modifier = modifier
    ) {
        graphPage.trainingDateRange?.let { dateRange ->
            TrainingGraphDataTypeSelection(
                dateRange = dateRange,
                primaryGraphType = graphUiState.primaryGraphType,
                primaryGraphUnitResId = graphUiState.primaryGraphUnitResId,
                secondaryGraphType = graphUiState.secondaryGraphType,
                secondaryGraphUnitId = graphUiState.secondaryGraphUnitResId,
                onPrimaryClick = { showPrimaryGraphTypesPicker = true },
                onSecondaryClick = { showSecondaryGraphTypesPicker = true },
            )
        }

        HorizontalPager(
            state = graphPagerState,
            userScrollEnabled = !chartEntrySelected,
        ) { index ->
            graphUiState.graphPages[index].chartData?.let { chartData ->
                TrainingGraph(
                    chartData = chartData,
                    entryYFormatter = graphUiState.entryYFormatter,
                    onChartPositionInRootYChanged = onChartPositionInRootYChanged,
                    chartHighlightedEvent = chartHighlightedEvent,
                    onLongPressed = {
                        chartEntrySelected = it
                    }
                )
            }
        }
    }

    if (showPrimaryGraphTypesPicker) {
        ActionBottomSheet(
            onDismiss = { showPrimaryGraphTypesPicker = false },
            actionTitle = stringResource(R.string.segmented_control_cancel).uppercase(Locale.getDefault()),
            onActionClick = { showPrimaryGraphTypesPicker = false },
        ) {
            GraphDataTypeListContent(
                selectedGraphType = graphUiState.primaryGraphType,
                graphTypes = graphUiState.primaryGraphTypes,
                onGraphTypeSelected = {
                    onPrimaryGraphTypeSelected(it)
                    showPrimaryGraphTypesPicker = false
                }
            )
        }
    }

    if (showSecondaryGraphTypesPicker) {
        ActionBottomSheet(
            onDismiss = { showSecondaryGraphTypesPicker = false },
            actionTitle = stringResource(R.string.segmented_control_cancel).uppercase(Locale.getDefault()),
            onActionClick = { showSecondaryGraphTypesPicker = false },
        ) {
            GraphDataTypeListContent(
                selectedGraphType = graphUiState.secondaryGraphType,
                graphTypes = graphUiState.secondaryGraphTypes,
                onGraphTypeSelected = {
                    onSecondaryGraphTypeSelected(it)
                    showSecondaryGraphTypesPicker = false
                }
            )
        }
    }
}
