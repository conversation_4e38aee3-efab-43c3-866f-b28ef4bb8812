package com.stt.android.diary.statistics

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.stt.android.R
import com.stt.android.compose.component.SuuntoExtendedFloatingActionButton
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.summary.SummaryDetailsViewState
import com.stt.android.diary.summary.SummaryTableViewStateNew
import com.stt.android.diary.summary.TrainingZoneSummaryColumn
import com.stt.android.diary.summary.TrainingZoneSummaryDetails
import com.stt.android.diary.summary.TrainingZoneSummaryGrouping
import com.stt.android.diary.summary.TrainingZoneSummaryLayoutType
import com.stt.android.diary.summary.TrainingZoneSummarySortingUiState
import com.stt.android.diary.summary.composables.DialogContent
import com.stt.android.diary.summary.composables.Empty
import com.stt.android.diary.summary.composables.Loading
import com.stt.android.diary.summary.composables.NoWorkouts
import com.stt.android.diary.summary.composables.StickySummaryTable
import com.stt.android.diary.trainingv2.composables.TrainingChartHighlightedView
import com.stt.android.diary.trainingv2.summary.TrainingGraphHighlightedData
import com.stt.android.diary.trainingv2.summary.TrainingGraphPage
import com.stt.android.diary.trainingv2.summary.TrainingGraphType
import com.stt.android.diary.trainingv2.summary.TrainingGraphUiState
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.workouts.SummaryWorkoutHeader
import com.stt.android.premium.featurepromotion.PremiumPromotionDialog
import kotlinx.collections.immutable.ImmutableList
import kotlinx.coroutines.launch
import kotlin.math.roundToInt

@Composable
internal fun StatisticsScreen(
    layoutType: TrainingZoneSummaryLayoutType,
    onLayoutTypeClick: () -> Unit,
    onFilterClick: () -> Unit,
    grouping: TrainingZoneSummaryGrouping,
    onGroupingChange: (TrainingZoneSummaryGrouping) -> Unit,
    workoutsCount: Int,
    showFilterResetIcon: Boolean,
    onFilterResetClick: () -> Unit,
    onSelectActivitiesClick: () -> Unit,
    summaryTableViewStateNew: SummaryTableViewStateNew,
    onDateClick: (selectedSports: ImmutableList<SummaryWorkoutHeader>, date: String) -> Unit,
    onHeaderItemClick: (TrainingZoneSummaryColumn) -> Unit,
    sortingUiState: TrainingZoneSummarySortingUiState,
    showColumnsEditButton: Boolean,
    onColumnsEditClick: () -> Unit,
    isDetailsVisible: Boolean,
    isDetailsLoading: Boolean,
    summaryDetailsViewState: SummaryDetailsViewState,
    onDetailsDialogDismiss: () -> Unit,
    onWorkoutClick: (SummaryWorkoutHeader) -> Unit,
    onDetailsHeaderItemClick: (TrainingZoneSummaryColumn) -> Unit,
    detailsSortingUiState: TrainingZoneSummarySortingUiState,
    hasPremium: Boolean,
    openFeaturePromotion: () -> Unit,
    hasWorkouts: Boolean?,
    summaryIsLoading: Boolean,
    graphTimeRange: GraphTimeRange,
    onGraphTimeRangeToggled: (GraphTimeRange) -> Unit,
    chartHighlightedData: TrainingGraphHighlightedData?,
    graphUiState: TrainingGraphUiState,
    onGraphPageChanged: (Int, TrainingGraphPage) -> Unit,
    onPrimaryGraphTypeSelected: (TrainingGraphType) -> Unit,
    onSecondaryGraphTypeSelected: (TrainingGraphType) -> Unit,
    chartHighlightedEvent: (Long?) -> Unit,
    isGraphLoading: Boolean,
    modifier: Modifier = Modifier,
) {
    var isTableScrolledUp by remember {
        mutableStateOf(false)
    }

    val isFilterSectionVisible by remember {
        derivedStateOf { !isTableScrolledUp || layoutType == TrainingZoneSummaryLayoutType.GRAPH }
    }

    var chartHighlightedViewHeight by remember { mutableIntStateOf(0) }
    var chartPositionInRootY by remember { mutableFloatStateOf(0f) }
    var containerPositionInRootY by remember { mutableFloatStateOf(0f) }

    val graphPagerState = remember(graphUiState.graphPages) {
        PagerState(
            currentPage = graphUiState.graphPageIndex,
            pageCount = { graphUiState.graphPages.size }
        )
    }
    val coroutineScope = rememberCoroutineScope()
    var isReturnToCurrentButtonVisible by remember { mutableStateOf(false) }

    Box(
        modifier = modifier
            .fillMaxSize()
            .narrowContentWithBgColors(
                backgroundColor = MaterialTheme.colors.surface,
                outerBackgroundColor = MaterialTheme.colors.background,
            )
            .onGloballyPositioned {
                containerPositionInRootY = it.boundsInRoot().top
            }
    ) {
        Column(
            modifier = if (layoutType == TrainingZoneSummaryLayoutType.GRAPH && !isGraphLoading)
                Modifier.verticalScroll(rememberScrollState()) else Modifier
        ) {
            AnimatedVisibility(visible = isFilterSectionVisible) {
                M3AppTheme {
                    StatisticsFilterSection(
                        layoutType = layoutType,
                        onLayoutTypeClick = onLayoutTypeClick,
                        onFilterClick = onFilterClick,
                        grouping = grouping,
                        onGroupingChange = onGroupingChange,
                        workoutsCount = workoutsCount,
                        showFilterResetIcon = showFilterResetIcon,
                        onFilterResetClick = onFilterResetClick,
                        onSelectActivitiesClick = onSelectActivitiesClick,
                        graphTimeRange = graphTimeRange,
                        onGraphTimeRangeToggled = onGraphTimeRangeToggled,
                    )
                }
            }

            when (layoutType) {
                TrainingZoneSummaryLayoutType.TABLE -> {
                    when (hasWorkouts) {
                        null -> Loading()
                        false -> NoWorkouts()
                        true -> if (summaryIsLoading) Loading() else {
                            if (summaryTableViewStateNew.showNoDataForSelectedFilter) {
                                Empty(
                                    onResetFilterClicked = onFilterResetClick
                                )
                            } else {
                                if (summaryTableViewStateNew.columns.isEmpty()) {
                                    return@Column
                                }
                                StickySummaryTable(
                                    columns = summaryTableViewStateNew.columns,
                                    rows = summaryTableViewStateNew.rows,
                                    onDateClicked = { t1, t2 -> onDateClick(t1, t2) },
                                    isLoading = false,
                                    maxValueLength = summaryTableViewStateNew.maxValueLength,
                                    hasTotalsRowItem = summaryTableViewStateNew.hasTotalsRowItem,
                                    onHeaderItemClicked = onHeaderItemClick,
                                    sortingUiState = sortingUiState,
                                    selectedGrouping = grouping,
                                    isTableScrolledUp = { isTableScrolledUp = it },
                                    showColumnsEditButton = showColumnsEditButton,
                                    onColumnsEditClick = onColumnsEditClick,
                                )
                                if (isDetailsVisible) {
                                    val detailsColumns = summaryDetailsViewState.columns
                                    // https://console.firebase.google.com/project/suunto-app/crashlytics/app/android:com.stt.android.suunto/issues/e23cdfbc7cbf49b5f23718b996704b53?time=last-seven-days&types=crash&versions=4.107.0%20(4107000);4.106.3%20(4106003)
                                    // Avoid IndexOutOfBoundsException
                                    if (detailsColumns.isNotEmpty()) {
                                        Dialog(
                                            onDismissRequest = { onDetailsDialogDismiss() },
                                            properties = DialogProperties(
                                                usePlatformDefaultWidth = false
                                            )
                                        ) {
                                            TrainingZoneSummaryDetails(
                                                numberOfWorkouts = summaryDetailsViewState.numberOfWorkouts,
                                                selectedDate = summaryDetailsViewState.selectedDate,
                                                onCloseClick = { onDetailsDialogDismiss() },
                                                columns = detailsColumns,
                                                rows = summaryDetailsViewState.rows,
                                                maxValueLength = summaryDetailsViewState.maxValueLength,
                                                onWorkoutClicked = onWorkoutClick,
                                                onHeaderItemClicked = onDetailsHeaderItemClick,
                                                sortingUiState = detailsSortingUiState,
                                                isLoading = isDetailsLoading,
                                                hasTotalsRowItem = summaryDetailsViewState.hasTotalsRowItem,
                                                selectedGrouping = TrainingZoneSummaryGrouping.BY_ACTIVITY
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                TrainingZoneSummaryLayoutType.GRAPH -> {
                    if (isGraphLoading) {
                        Loading()
                    } else {
                        M3AppTheme {
                            StatisticsGraphScreen(
                                graphUiState = graphUiState,
                                onPrimaryGraphTypeSelected = onPrimaryGraphTypeSelected,
                                onSecondaryGraphTypeSelected = onSecondaryGraphTypeSelected,
                                chartHighlightedEvent = chartHighlightedEvent,
                                onChartPositionInRootYChanged = {
                                    chartPositionInRootY = it
                                },
                                onGraphPageChanged = onGraphPageChanged,
                                showReturnToCurrent = { isReturnToCurrentButtonVisible = it },
                                graphPagerState = graphPagerState,
                                modifier = Modifier
                                    .padding(top = MaterialTheme.spacing.small)
                            )
                        }
                    }
                }
            }
        }

        if (!hasPremium) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .pointerInput(Unit) {}
                    .background(Color(0X66303030))) {
                PremiumPromotionDialog(
                    openFeaturePromotion = openFeaturePromotion,
                    modifier = Modifier.align(Alignment.Center),
                ) { _, _ ->
                    DialogContent()
                }
            }
        }

        TrainingChartHighlightedView(
            chartHighlightedData = chartHighlightedData,
            modifier = Modifier
                .offset {
                    IntOffset(
                        x = 0,
                        y = (chartPositionInRootY - containerPositionInRootY - chartHighlightedViewHeight)
                            .roundToInt().coerceAtLeast(0)
                    )
                }
                .onSizeChanged { chartHighlightedViewHeight = it.height },
        )

        AnimatedVisibility(
            visible = isReturnToCurrentButtonVisible,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = MaterialTheme.spacing.xxxlarge),
            enter = slideInVertically(initialOffsetY = { it * 2 }),
            exit = slideOutVertically(targetOffsetY = { it * 2 }),
        ) {
            M3AppTheme {
                SuuntoExtendedFloatingActionButton(
                    icon = R.drawable.ic_revert,
                    text = R.string.back_to_current_button,
                    onClick = {
                        coroutineScope.launch {
                            graphPagerState.scrollToPage(graphPagerState.pageCount - 1)
                        }
                    },
                )
            }
        }
    }
}
