package com.stt.android.diary.summary

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.dimensionResource
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.AppTheme
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.diary.summary.composables.Loading
import com.stt.android.diary.summary.composables.NoWorkouts
import com.stt.android.diary.summary.composables.TrainingZoneSummary
import com.stt.android.domain.workouts.SummaryWorkoutHeader
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.diary.R
import com.stt.android.premium.PremiumPurchaseFlowLauncher
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class TrainingZoneSummaryFragment : Fragment() {
    val viewModel: TrainingZoneSummaryViewModel by viewModels()

    @Inject
    lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    @Inject
    lateinit var premiumPurchaseFlowLauncher: PremiumPurchaseFlowLauncher

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        premiumPurchaseFlowLauncher.register(this)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContent {
            AppTheme {
                ContentCenteringColumn(
                    modifier = Modifier
                        .background(MaterialTheme.colors.background)
                        .padding(top = dimensionResource(R.dimen.top_bottom_divider))
                ) {
                    var isDetailsVisible by rememberSaveable {
                        mutableStateOf(false)
                    }

                    val filterUiState by viewModel.filterUiState.collectAsState()
                    val selectedDate by viewModel.formattedSelectedDates.collectAsState()
                    val summaryTableViewStateNew by viewModel.summaryViewState.collectAsState()
                    val mainSorting by viewModel.mainSorting.collectAsState()
                    val detailsSorting by viewModel.detailsSorting.collectAsState()
                    val summaryDetailsViewState by viewModel.summaryDetailsViewState.collectAsState()
                    val isDetailsLoading by viewModel.isDetailsLoading.collectAsState()
                    val hasWorkouts by viewModel.hasWorkouts.collectAsState()
                    val layoutType by viewModel.layoutType.collectAsState()
                    val hasPremium by viewModel.hasPremium.collectAsState()
                    val isShowEmptyRowsEnabled by viewModel.isShowEmptyRowsEnabled.collectAsState()
                    val summaryIsLoading by viewModel.summaryIsLoading.collectAsState()

                    when (hasWorkouts) {
                        null -> {
                            Loading()
                        }

                        false -> {
                            NoWorkouts()
                        }

                        else -> {
                            if (summaryIsLoading) Loading() else TrainingZoneSummary(
                                sports = filterUiState.sportGrouping,
                                grouping = filterUiState.grouping,
                                selectedDate = selectedDate,
                                distanceUiState = filterUiState.distance,
                                summaryTableViewStateNew = summaryTableViewStateNew,
                                summaryDetailsViewState = summaryDetailsViewState,
                                sortingUiState = mainSorting,
                                detailsSortingUiState = detailsSorting,
                                isDetailsVisible = isDetailsVisible,
                                distanceUnit = filterUiState.distanceUnit,
                                selectedSuuntoTags = filterUiState.suuntoTags,
                                selectedUserTags = filterUiState.userTags,
                                onFilterClick = {
                                    if (childFragmentManager.findFragmentByTag(
                                            TrainingZoneSummaryFilterBottomSheet.TAG
                                        ) == null
                                    ) {
                                        TrainingZoneSummaryFilterBottomSheet().show(
                                            childFragmentManager,
                                            TrainingZoneSummaryFilterBottomSheet.TAG
                                        )
                                    }
                                },
                                onDateClicked = { workouts, date ->
                                    if (filterUiState.grouping == TrainingZoneSummaryGrouping.BY_ACTIVITY) {
                                        navigateToDetails(workouts.first())
                                    } else {
                                        viewModel.setSelectedWorkouts(workouts, date)
                                        isDetailsVisible = true
                                    }
                                },
                                onDetailsDialogDismissed = {
                                    isDetailsVisible = false
                                    viewModel.resetSelectedWorkouts()
                                },
                                onWorkoutClicked = ::navigateToDetails,
                                isDetailsLoading = isDetailsLoading,
                                onResetFilterClicked = {
                                    viewModel.resetFilters()
                                },
                                onHeaderItemClicked = {
                                    viewModel.setMainSorting(it)
                                },
                                onDetailsHeaderItemClicked = {
                                    viewModel.setDetailsSorting(it)
                                },
                                selectedLayoutType = layoutType,
                                onLayoutClick = viewModel::toggleSelectedLayout,
                                hasPremium = hasPremium,
                                openFeaturePromotion = {
                                    premiumPurchaseFlowLauncher.launchFeaturePromotionAndAskAboutAppReset(
                                        context = requireContext(),
                                        analyticsSource = AnalyticsPropertyValue.PremiumPurchaseFlowScreenSource.SUMMARY
                                    )
                                },
                                showColumnsEditButton = true,
                                onColumnsEditClick = ::navigateToSummaryColumnsEdit,
                                isNewTrainingZoneEnabled = false,
                                filteredWorkoutSize = 0,
                                isShowEmptyRowsEnabled = isShowEmptyRowsEnabled,
                                showEmptyRowsChecked = filterUiState.showEmptyRowsChecked,
                                onShowEmptyRowsCheckedChange = viewModel::updateShowEmptyWeeksChecked,
                                showResetIcon = false,
                            )
                        }
                    }
                }
            }
        }
    }

    private fun navigateToDetails(summaryWorkoutHeader: SummaryWorkoutHeader) {
        rewriteNavigator.navigate(
            context = requireContext(),
            username = summaryWorkoutHeader.username,
            workoutId = summaryWorkoutHeader.id,
            workoutKey = summaryWorkoutHeader.key
        )
    }

    private fun navigateToWorkoutDetails(workoutHeader: WorkoutHeader) {
        rewriteNavigator.navigate(
            context = requireContext(),
            username = workoutHeader.username,
            workoutId = workoutHeader.id,
            workoutKey = workoutHeader.key
        )
    }

    private fun navigateToSummaryColumnsEdit() {
        startActivity(TrainingZoneSummaryColumnsEditActivity.newIntent(requireContext()))
    }
}
