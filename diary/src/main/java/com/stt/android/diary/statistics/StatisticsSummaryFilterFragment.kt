package com.stt.android.diary.statistics

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.viewModels
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.diary.summary.DateSelectionType
import com.stt.android.diary.summary.FilterDatePicker
import com.stt.android.diary.summary.TrainingZoneSummaryViewModel
import com.stt.android.diary.summary.rememberSummaryFilterDatePickerState
import com.stt.android.ui.utils.SmartBottomSheetDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.collections.immutable.toImmutableList

@AndroidEntryPoint
class StatisticsSummaryFilterFragment : SmartBottomSheetDialogFragment() {

    private val parentViewModel: TrainingZoneSummaryViewModel by viewModels(ownerProducer = { requireParentFragment() })

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return super.onCreateDialog(savedInstanceState).also { dialog ->
            (dialog as BottomSheetDialog).behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContentWithTheme {
            val filterUiState by parentViewModel.filterUiState.collectAsState()
            val formattedStartDate by parentViewModel.formattedStartDate.collectAsState()
            val formattedEndDate by parentViewModel.formattedEndDate.collectAsState()
            val isShowEmptyRowsEnabled by parentViewModel.isShowEmptyRowsEnabled.collectAsState()
            val filterDatePickerState = rememberSummaryFilterDatePickerState(
                initialSelectedStartDateMillis = filterUiState.selectedStartDateMillis,
                initialSelectedEndDateMillis = filterUiState.selectedEndDateMillis
            )
            val userTags by parentViewModel.allUserTags.collectAsState()

            var dateSelectionType by rememberSaveable {
                // Null means no selection
                mutableStateOf<DateSelectionType?>(null)
            }
            StatisticsSummaryFilter(
                onCloseClick = ::dismiss,
                formattedStartDate = formattedStartDate,
                formattedEndDate = formattedEndDate,
                onDateSelectionTypeChange = {
                    dateSelectionType = it
                },
                summarySuuntoTags = filterUiState.summarySuuntoTags,
                summaryUserTags = filterUiState.summaryUserTags,
                suuntoTags = parentViewModel.allSuuntoTags,
                userTags = userTags,
                onSelectTagsChange = { suuntoTags, userTags ->
                    parentViewModel.updateTags(
                        suuntoTags.toImmutableList(),
                        userTags.toImmutableList()
                    )
                },
                distanceUiState = filterUiState.distance,
                onDistanceChange = { distance ->
                    parentViewModel.updateDistance(distance.start, distance.endInclusive)
                },
                isDistanceSupported = filterUiState.isDistanceSupported,
                distanceUnit = filterUiState.distanceUnit,
                showEmptyRowsChecked = filterUiState.showEmptyRowsChecked,
                isShowEmptyRowsEnabled = isShowEmptyRowsEnabled,
                onShowEmptyRowsCheckedChange = parentViewModel::updateShowEmptyWeeksChecked,
                onResetClick = parentViewModel::resetFilters,
            )

            if (dateSelectionType != null) {
                FilterDatePicker(
                    dateSelectionType = dateSelectionType,
                    filterDatePickerState = filterDatePickerState,
                    onDismissRequest = { dateSelectionType = null },
                    onStartDateSet = parentViewModel::setStartDate,
                    onEndDateSet = parentViewModel::setEndDate,
                )
            }
        }
    }

    companion object {
        const val TAG = "StatisticsSummaryFilterFragment"

        fun newInstance() =
            StatisticsSummaryFilterFragment()
    }
}
