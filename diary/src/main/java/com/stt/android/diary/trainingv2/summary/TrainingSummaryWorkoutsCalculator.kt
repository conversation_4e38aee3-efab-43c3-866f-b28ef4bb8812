package com.stt.android.diary.trainingv2.summary

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.diary.summary.TrainingZoneSummaryDataAggregatorUseCase
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.workouts.SummaryWorkoutHeader
import com.stt.android.utils.CalendarProvider
import kotlinx.coroutines.withContext
import java.time.LocalDate
import java.time.YearMonth
import java.time.temporal.TemporalAdjusters
import javax.inject.Inject

class TrainingSummaryWorkoutsCalculator @Inject constructor(
    private val calendarProvider: CalendarProvider,
    private val dispatchers: CoroutinesDispatchers,
    private val aggregator: TrainingZoneSummaryDataAggregatorUseCase,
) {

    suspend fun calculateYearly(
        dateRange: ClosedRange<LocalDate>,
        workouts: List<SummaryWorkoutHeader>,
    ) = withContext(dispatchers.computation) {
        val groupedWorkouts = workouts.groupBy {
            it.startTime.toLocalDate().year
        }
        val startYear = dateRange.start.year
        val endYear = dateRange.endInclusive.year
        generateSequence(startYear) { it + 1 }
            .takeWhile { it <= endYear }
            .map { year ->
                val workoutsPerYear = groupedWorkouts[year].orEmpty()
                TrainingGraphWorkoutSummary(
                    date = LocalDate.of(year, 1, 1),
                    totalDuration = aggregator.calculateDuration(workoutsPerYear),
                    totalDistance = aggregator.calculateDistance(workoutsPerYear),
                    avgSpeed = aggregator.calculateAvgSpeed(workoutsPerYear),
                    avgPace = aggregator.calculateAvgPace(workoutsPerYear),
                    heartRateAverage = aggregator.calculateAvgHeartRate(workoutsPerYear),
                    totalAscent = aggregator.calculateAscent(workoutsPerYear),
                    tss = aggregator.calculateTss(workoutsPerYear),
                    energyConsumption = aggregator.calculateEnergy(workoutsPerYear),
                    estVo2Peak = aggregator.calculateVo2Peak(workoutsPerYear),
                    avgPower = aggregator.calculateAvgPower(workoutsPerYear),
                    normalizedPower = aggregator.calculateNormalizedPower(workoutsPerYear),
                    avgSwimPace = aggregator.calculateAvgSwimPace(workoutsPerYear),
                )
            }
    }

    suspend fun calculateMonthly(
        dateRange: ClosedRange<LocalDate>,
        workouts: List<SummaryWorkoutHeader>,
    ) = withContext(dispatchers.computation) {
        val groupedWorkouts = workouts.groupBy {
            it.startTime.toLocalDate().withDayOfMonth(1)
        }
        val startMonth = YearMonth.of(dateRange.start.year, dateRange.start.monthValue)
        val endMonth = YearMonth.of(dateRange.endInclusive.year, dateRange.endInclusive.monthValue)
        generateSequence(startMonth) { it.plusMonths(1) }
            .takeWhile { !it.isAfter(endMonth) }
            .map { month ->
                val date = month.atDay(1)
                val workoutsPerMonth = groupedWorkouts[date].orEmpty()
                TrainingGraphWorkoutSummary(
                    date = date,
                    totalDuration = aggregator.calculateDuration(workoutsPerMonth),
                    totalDistance = aggregator.calculateDistance(workoutsPerMonth),
                    avgSpeed = aggregator.calculateAvgSpeed(workoutsPerMonth),
                    avgPace = aggregator.calculateAvgPace(workoutsPerMonth),
                    heartRateAverage = aggregator.calculateAvgHeartRate(workoutsPerMonth),
                    totalAscent = aggregator.calculateAscent(workoutsPerMonth),
                    tss = aggregator.calculateTss(workoutsPerMonth),
                    energyConsumption = aggregator.calculateEnergy(workoutsPerMonth),
                    estVo2Peak = aggregator.calculateVo2Peak(workoutsPerMonth),
                    avgPower = aggregator.calculateAvgPower(workoutsPerMonth),
                    normalizedPower = aggregator.calculateNormalizedPower(workoutsPerMonth),
                    avgSwimPace = aggregator.calculateAvgSwimPace(workoutsPerMonth),
                )
            }
    }

    suspend fun calculateWeekly(
        dateRange: ClosedRange<LocalDate>,
        workouts: List<SummaryWorkoutHeader>,
    ) = withContext(dispatchers.computation) {
        val firstDayOfWeek = calendarProvider.getFirstDayOfWeek()
        val groupedWorkouts = workouts.groupBy {
            it.startTime.toLocalDate().with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
        }
        val startDate = dateRange.start.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
        val endDate = dateRange.endInclusive.with(TemporalAdjusters.previousOrSame(firstDayOfWeek))
        generateSequence(startDate) { it.plusWeeks(1) }
            .takeWhile { !it.isAfter(endDate) }
            .map { weekStartDate ->
                val workoutsPerWeek = groupedWorkouts[weekStartDate].orEmpty()
                TrainingGraphWorkoutSummary(
                    date = weekStartDate,
                    totalDuration = aggregator.calculateDuration(workoutsPerWeek),
                    totalDistance = aggregator.calculateDistance(workoutsPerWeek),
                    avgSpeed = aggregator.calculateAvgSpeed(workoutsPerWeek),
                    avgPace = aggregator.calculateAvgPace(workoutsPerWeek),
                    heartRateAverage = aggregator.calculateAvgHeartRate(workoutsPerWeek),
                    totalAscent = aggregator.calculateAscent(workoutsPerWeek),
                    tss = aggregator.calculateTss(workoutsPerWeek),
                    energyConsumption = aggregator.calculateEnergy(workoutsPerWeek),
                    estVo2Peak = aggregator.calculateVo2Peak(workoutsPerWeek),
                    avgPower = aggregator.calculateAvgPower(workoutsPerWeek),
                    normalizedPower = aggregator.calculateNormalizedPower(workoutsPerWeek),
                    avgSwimPace = aggregator.calculateAvgSwimPace(workoutsPerWeek),
                )
            }
    }

    suspend fun calculateDaily(
        dateRange: ClosedRange<LocalDate>,
        workouts: List<SummaryWorkoutHeader>,
    ) = withContext(dispatchers.computation) {
        val groupedWorkouts = workouts.groupBy {
            it.startTime.toLocalDate()
        }
        val startDate = dateRange.start
        val endDate = dateRange.endInclusive
        generateSequence(startDate) { it.plusDays(1) }
            .takeWhile { !it.isAfter(endDate) }
            .map { date ->
                val workoutsPerDay = groupedWorkouts[date].orEmpty()
                TrainingGraphWorkoutSummary(
                    date = date,
                    totalDuration = aggregator.calculateDuration(workoutsPerDay),
                    totalDistance = aggregator.calculateDistance(workoutsPerDay),
                    avgSpeed = aggregator.calculateAvgSpeed(workoutsPerDay),
                    avgPace = aggregator.calculateAvgPace(workoutsPerDay),
                    heartRateAverage = aggregator.calculateAvgHeartRate(workoutsPerDay),
                    totalAscent = aggregator.calculateAscent(workoutsPerDay),
                    tss = aggregator.calculateTss(workoutsPerDay),
                    energyConsumption = aggregator.calculateEnergy(workoutsPerDay),
                    estVo2Peak = aggregator.calculateVo2Peak(workoutsPerDay),
                    avgPower = aggregator.calculateAvgPower(workoutsPerDay),
                    normalizedPower = aggregator.calculateNormalizedPower(workoutsPerDay),
                    avgSwimPace = aggregator.calculateAvgSwimPace(workoutsPerDay),
                )
            }
    }
}
