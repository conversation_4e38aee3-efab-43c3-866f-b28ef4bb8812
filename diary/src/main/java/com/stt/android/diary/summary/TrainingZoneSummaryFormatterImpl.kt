package com.stt.android.diary.summary

import android.content.Context
import android.text.format.DateUtils
import com.amersports.formatter.Success
import com.amersports.formatter.Unit
import com.stt.android.R
import com.stt.android.data.toEpochMilli
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.workouts.details.values.WorkoutValue
import dagger.hilt.android.qualifiers.ApplicationContext
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import javax.inject.Inject

class TrainingZoneSummaryFormatterImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val infoModelFormatter: InfoModelFormatter,
) : TrainingZoneSummaryFormatter {
    // The idea behind this is to convert BasicWorkoutValue to a string that will be shown in the UI
    // and then we get the longest one (in term of characters) to dynamically calculate the width of a cell
    override fun toMeasurableString(basicWorkoutValue: BasicWorkoutValue?): String = buildString {
        if (basicWorkoutValue == null) {
            append("")
        } else {
            append(basicWorkoutValue.value)
            if (basicWorkoutValue.unitRes != null) {
                append(" ")
                append(infoModelFormatter.getString(basicWorkoutValue.unitRes))
            }
        }
    }

    override fun formatDuration(totalDuration: Double): BasicWorkoutValue {
        return if (totalDuration <= 3600000.0) {
            infoModelFormatter.formatValue(
                SummaryItem.DURATION,
                totalDuration
            )
        } else {
            val result = infoModelFormatter.formatValue(
                "DurationAccumulated",
                totalDuration,
                true
            )
            if (result is Success) {
                return BasicWorkoutValue(
                    value = result.value,
                    unitRes = InfoModelFormatter.getUnitResId(result.unit)
                )
            } else {
                null
            }
        }.toBasicWorkoutValue()
    }

    override fun formatDiveTime(diveTime: Double): BasicWorkoutValue {
        return infoModelFormatter.formatValue(SummaryItem.DIVETIME, diveTime).toBasicWorkoutValue()
    }

    override fun formatDistance(totalDistance: Double?): BasicWorkoutValue {
        return totalDistance?.let {
            infoModelFormatter.formatValue(
                SummaryItem.DISTANCE,
                it
            )
        }.toBasicWorkoutValue()
    }

    override fun formatAvgSpeed(totalAvgSpeed: Double?): BasicWorkoutValue {
        return totalAvgSpeed?.let {
            infoModelFormatter.formatValue(
                SummaryItem.AVGSPEED,
                it
            )
        }.toBasicWorkoutValue()
    }

    override fun formatAvgPace(totalAvgPace: Double?): BasicWorkoutValue {
        return totalAvgPace?.let {
            infoModelFormatter.formatValue(
                SummaryItem.AVGPACE,
                it
            )
        }.toBasicWorkoutValue()
    }

    override fun formatAvgHeartRate(totalAvgHeartRate: Double?): BasicWorkoutValue {
        return totalAvgHeartRate?.let {
            infoModelFormatter.formatValue(
                SummaryItem.AVGHEARTRATE,
                it
            )
        }.toBasicWorkoutValue()
    }

    override fun formatAscent(totalAscent: Double?): BasicWorkoutValue =
        totalAscent?.let {
            infoModelFormatter.formatValue(
                SummaryItem.ASCENTALTITUDE,
                it
            )
        }.toBasicWorkoutValue()

    override fun formatTss(totalTss: Float?): BasicWorkoutValue =
        totalTss?.let(infoModelFormatter::formatTss)
            ?.let { formattedTss ->
                BasicWorkoutValue(
                    value = formattedTss,
                    unitRes = R.string.workout_values_headline_tss
                )
            }
            ?: null.toBasicWorkoutValue()

    override fun formatEnergy(totalEnergy: Int?): BasicWorkoutValue {
        val result = totalEnergy?.let {
            // We can't use SummaryItem.ENERGY as it uses EnergyFivedigitsGeneric which has max value of 99999.5
            // EnergySixdigits max' value is 999999.5
            infoModelFormatter.formatValue(
                "EnergySixdigits",
                it,
                true

            )
        }
        return if (result is Success) {
            BasicWorkoutValue(
                value = result.value,
                unitRes = InfoModelFormatter.getUnitResId(Unit.KCAL)
            )
        } else {
            null.toBasicWorkoutValue()
        }
    }

    override fun formatVo2Peak(totalVo2Peak: Float?): BasicWorkoutValue {
        return totalVo2Peak?.let {
            infoModelFormatter.formatValue(
                SummaryItem.ESTVO2PEAK,
                it
            )
        }.toBasicWorkoutValue()
    }

    override fun formatFilterDate(startDateMillis: Long?, endDateMillis: Long?): String? {
        val thisYear = LocalDate.now().year
        return when {
            startDateMillis == null && endDateMillis == null -> null
            startDateMillis == null && endDateMillis != null -> {
                val endDate = Instant.ofEpochMilli(endDateMillis).atZone(ZoneOffset.UTC)
                val formattedDate = if (thisYear == endDate.year) {
                    endDate.format(context, false)
                } else {
                    endDate.format(context, true)
                }
                ".-$formattedDate"
            }

            startDateMillis != null && endDateMillis == null -> {
                val startDate = Instant.ofEpochMilli(startDateMillis).atZone(ZoneOffset.UTC)
                val formattedDate = if (thisYear == startDate.year) {
                    startDate.format(context, false)
                } else {
                    startDate.format(context, true)
                }
                "$formattedDate-."
            }

            else -> {
                val startDate = Instant.ofEpochMilli(startDateMillis!!).atZone(ZoneOffset.UTC)
                val endDate = Instant.ofEpochMilli(endDateMillis!!).atZone(ZoneOffset.UTC)
                if (startDate.year == thisYear && endDate.year == thisYear) {
                    if (startDate == endDate) {
                        startDate.format(context, false)
                    } else {
                        "${startDate.format(context, false)}-${endDate.format(context, false)}"
                    }
                } else if (startDate.year == endDate.year) {
                    if (startDate == endDate) {
                        startDate.format(context, true)
                    } else {
                        "${startDate.format(context, false)}-${endDate.format(context, true)}"
                    }
                } else {
                    "${startDate.format(context, true)}-${endDate.format(context, true)}"
                }
            }
        }
    }

    private fun ZonedDateTime.format(context: Context, formatYear: Boolean): String =
        DateUtils.formatDateTime(
            context,
            toEpochMilli(),
            DateUtils.FORMAT_SHOW_DATE or DateUtils.FORMAT_NUMERIC_DATE or (if (formatYear) DateUtils.FORMAT_SHOW_YEAR else DateUtils.FORMAT_NO_YEAR)
        )

    override fun formatDatePickerDateDate(dateMillis: Long?): String? {
        if (dateMillis == null) return null

        return DateTimeFormatter.ofLocalizedDate(FormatStyle.LONG)
            .format(ZonedDateTime.ofInstant(Instant.ofEpochMilli(dateMillis), ZoneOffset.UTC))
    }

    override fun formatAvgPower(avgPower: Double?): BasicWorkoutValue {
        return avgPower?.let {
            infoModelFormatter.formatValue(
                SummaryItem.AVGPOWER,
                it
            )
        }.toBasicWorkoutValue()
    }

    override fun formatNormalizedPower(normalizedPower: Double?): BasicWorkoutValue {
        return normalizedPower?.let {
            infoModelFormatter.formatValue(
                SummaryItem.NORMALIZEDPOWER,
                it
            )
        }.toBasicWorkoutValue()
    }

    override fun formatAvgSwimPace(avgSwimPace: Double?): BasicWorkoutValue {
        return avgSwimPace?.let {
            infoModelFormatter.formatValue(
                SummaryItem.AVGSWIMPACE,
                it
            )
        }.toBasicWorkoutValue()
    }

    override fun formatRowDate(dateMillis: Long): String {
        return DateUtils.formatDateTime(
            context,
            dateMillis,
            DateUtils.FORMAT_SHOW_DATE or DateUtils.FORMAT_NUMERIC_DATE or DateUtils.FORMAT_NO_YEAR
        )
    }
}

private fun WorkoutValue?.toBasicWorkoutValue(): BasicWorkoutValue {
    if (this == null || this.value.isNullOrBlank()) {
        return BasicWorkoutValue(value = "-", unitRes = null)
    }

    return BasicWorkoutValue(
        value = this.value,
        unitRes = this.unit
    )
}
