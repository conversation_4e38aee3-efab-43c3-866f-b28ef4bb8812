package com.stt.android.diary.trainingv2.composables

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.compose.cartesian.CartesianChartHost
import com.patrykandpatrick.vico.compose.cartesian.axis.fixed
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberAxisGuidelineComponent
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberAxisLabelComponent
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberAxisLineComponent
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberBottom
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberEnd
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberStart
import com.patrykandpatrick.vico.compose.cartesian.layer.point
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberColumnCartesianLayer
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberLine
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberLineCartesianLayer
import com.patrykandpatrick.vico.compose.cartesian.layer.stacked
import com.patrykandpatrick.vico.compose.cartesian.rememberCartesianChart
import com.patrykandpatrick.vico.compose.cartesian.rememberVicoZoomState
import com.patrykandpatrick.vico.compose.common.component.rememberLineComponent
import com.patrykandpatrick.vico.compose.common.component.rememberShapeComponent
import com.patrykandpatrick.vico.compose.common.fill
import com.patrykandpatrick.vico.compose.common.insets
import com.patrykandpatrick.vico.compose.common.shape.dashedShape
import com.patrykandpatrick.vico.compose.common.shape.rounded
import com.patrykandpatrick.vico.core.cartesian.CartesianMeasuringContext
import com.patrykandpatrick.vico.core.cartesian.Zoom
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.axis.BaseAxis
import com.patrykandpatrick.vico.core.cartesian.axis.HorizontalAxis
import com.patrykandpatrick.vico.core.cartesian.axis.VerticalAxis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianChartModelProducer
import com.patrykandpatrick.vico.core.cartesian.data.CartesianLayerRangeProvider
import com.patrykandpatrick.vico.core.cartesian.data.CartesianValueFormatter
import com.patrykandpatrick.vico.core.cartesian.data.columnSeries
import com.patrykandpatrick.vico.core.cartesian.data.lineSeries
import com.patrykandpatrick.vico.core.cartesian.decoration.Decoration
import com.patrykandpatrick.vico.core.cartesian.layer.CartesianLayer
import com.patrykandpatrick.vico.core.cartesian.layer.ColumnCartesianLayer
import com.patrykandpatrick.vico.core.cartesian.layer.ColumnCartesianLayer.MergeMode
import com.patrykandpatrick.vico.core.cartesian.layer.LineCartesianLayer
import com.patrykandpatrick.vico.core.cartesian.marker.CartesianMarker
import com.patrykandpatrick.vico.core.cartesian.marker.CartesianMarkerVisibilityListener
import com.patrykandpatrick.vico.core.common.shape.CorneredShape
import com.stt.android.chart.impl.chart.AverageGuideLine
import com.stt.android.chart.impl.chart.HighlightMarker
import com.stt.android.chart.impl.chart.axis.calculateXAxisStep
import com.stt.android.chart.impl.chart.axis.createXAxisItemPlacer
import com.stt.android.chart.impl.chart.axis.createXAxisValueFormatter
import com.stt.android.chart.impl.chart.rememberRoundedCornerStackedLineComponent
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.compose.modifiers.onLongPress
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.trainingv2.summary.HighlightedData
import com.stt.android.diary.trainingv2.summary.TrainingChartData
import com.stt.android.diary.trainingv2.summary.TrainingGraphEntryYFormatter
import com.stt.android.diary.trainingv2.summary.TrainingGraphHighlightedData
import com.stt.android.diary.trainingv2.summary.TrainingGraphType
import kotlin.math.roundToLong

@Composable
internal fun TrainingChart(
    chartData: TrainingChartData,
    entryYFormatter: TrainingGraphEntryYFormatter,
    onEntrySelected: (Long) -> Unit,
    onNoEntrySelected: () -> Unit,
    onLongPressed: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current

    val modelProducer = remember { CartesianChartModelProducer() }
    LaunchedEffect(chartData) {
        modelProducer.prepareData(chartData)
    }

    val markerLine = rememberLineComponent()
    val highlightMarker = remember {
        HighlightMarker(
            markerLine = markerLine,
            drawOverLayers = true,
        )
    }

    var chartLongPressed by remember { mutableStateOf(false) }
    var selectedEntryX by remember { mutableStateOf<Long?>(null) }

    CartesianChartHost(
        chart = rememberCartesianChart(
            layers = listOf(chartData.primarySeries, chartData.secondarySeries)
                .flatMap { rememberLayer(it) }
                .toTypedArray(),
            startAxis = VerticalAxis.rememberStart(
                line = null,
                tick = null,
                guideline = rememberAxisGuidelineComponent(
                    fill = fill(MaterialTheme.colors.cloudyGrey),
                ),
                itemPlacer = remember { VerticalAxis.ItemPlacer.count(count = { 4 }) },
                valueFormatter = rememberGraphTypeValueFormatter(
                    entryYFormatter,
                    chartData.primaryGraphType,
                ),
            ),
            endAxis = VerticalAxis.rememberEnd(
                line = null,
                tick = null,
                itemPlacer = remember { VerticalAxis.ItemPlacer.count(count = { 4 }) },
                valueFormatter = rememberGraphTypeValueFormatter(
                    entryYFormatter,
                    chartData.secondaryGraphType,
                ),
            ),
            bottomAxis = HorizontalAxis.rememberBottom(
                label = rememberAxisLabelComponent(
                    textSize = with(LocalDensity.current) { 12.dp.toSp() },
                    padding = insets(4.dp, 0.dp),
                ),
                valueFormatter = remember(chartData.chartGranularity) {
                    createXAxisValueFormatter(context, chartData.chartGranularity)
                },
                line = rememberAxisLineComponent(
                    fill = fill(MaterialTheme.colors.mediumGrey),
                ),
                guideline = null,
                itemPlacer = remember(chartData.chartGranularity) {
                    createXAxisItemPlacer(chartData.chartGranularity)
                },
                size = BaseAxis.Size.fixed(20.dp),
            ),
            decorations = buildList {
                rememberAverageDecoration(
                    chartData.primaryGraphType,
                    chartData.primarySeries.average,
                    entryYFormatter,
                    true,
                )?.let { add(it) }
                rememberAverageDecoration(
                    chartData.secondaryGraphType,
                    chartData.secondarySeries.average,
                    entryYFormatter,
                    false,
                )?.let { add(it) }
            },
            marker = highlightMarker,
            markerVisibilityListener = object : CartesianMarkerVisibilityListener {
                override fun onShown(
                    marker: CartesianMarker,
                    targets: List<CartesianMarker.Target>,
                ) = onUpdated(marker, targets)

                override fun onUpdated(
                    marker: CartesianMarker,
                    targets: List<CartesianMarker.Target>
                ) {
                    targets.firstOrNull()
                        ?.x
                        ?.roundToLong()
                        ?.let { entryX ->
                            selectedEntryX = entryX
                            if (chartLongPressed) {
                                onEntrySelected(entryX)
                            }
                        }
                        ?: run(onNoEntrySelected)
                }

                override fun onHidden(marker: CartesianMarker) {
                    onNoEntrySelected()
                }
            },
            getXStep = calculateXAxisStep(chartData.chartGranularity),
        ),
        modelProducer = modelProducer,
        zoomState = rememberVicoZoomState(
            zoomEnabled = false,
            initialZoom = Zoom.Content,
        ),
        modifier = modifier
            .fillMaxWidth()
            .onLongPress(
                key = Unit,
                onLongPressed = { longPressed ->
                    chartLongPressed = longPressed
                    highlightMarker.drawOverLayers = longPressed
                    if (longPressed) {
                        selectedEntryX?.let(onEntrySelected)
                    }
                    onLongPressed(longPressed)
                },
            ),
    )
}

@Composable
private fun rememberColumnLayer(
    axisRange: TrainingChartData.AxisRange,
) = rememberColumnCartesianLayer(
    columnProvider = ColumnCartesianLayer.ColumnProvider.series(
        columns = listOf(
            rememberRoundedCornerStackedLineComponent(
                fill = fill(MaterialTheme.colors.primary),
                prevFill = fill(MaterialTheme.colors.primary),
                thicknessDp = 16f,
                shape = CorneredShape.rounded(topLeft = 4.dp, topRight = 4.dp),
                roundedCornerOffsetDp = 4f,
                horizontalAxisHeightDp = 20f,
            )
        )
    ),
    rangeProvider = rememberRangeProvider(axisRange),
    verticalAxisPosition = Axis.Position.Vertical.Start,
    mergeMode = { MergeMode.stacked() },
    columnCollectionSpacing = 16.dp,
)

@Composable
private fun rememberLineLayer(
    axisRange: TrainingChartData.AxisRange
) = rememberLineCartesianLayer(
    lineProvider = LineCartesianLayer.LineProvider.series(
        lines = listOf(
            LineCartesianLayer.rememberLine(
                fill = LineCartesianLayer.LineFill.single(fill(MaterialTheme.colors.onSurface)),
                pointProvider = LineCartesianLayer.PointProvider.single(
                    LineCartesianLayer.point(
                        rememberShapeComponent(
                            fill = fill(MaterialTheme.colors.surface),
                            shape = CorneredShape.Pill,
                            strokeFill = fill(MaterialTheme.colors.onSurface),
                            strokeThickness = 2.dp,
                        )
                    )
                ),
            )
        ),
    ),
    rangeProvider = rememberRangeProvider(axisRange),
    verticalAxisPosition = Axis.Position.Vertical.End,
    pointSpacing = 16.dp,
)

@Composable
private fun rememberLayer(
    series: TrainingChartData.Series
): List<CartesianLayer<*>> = when (series.chartType) {
    ChartType.BAR -> listOf(rememberColumnLayer(series.axisRange))

    ChartType.LINE -> series.entries.map {
        rememberLineLayer(series.axisRange)
    }

    ChartType.CANDLESTICK,
    ChartType.SLEEP_STAGE -> throw Exception("Not supported")
}

@Composable
private fun rememberRangeProvider(
    axisRange: TrainingChartData.AxisRange,
): CartesianLayerRangeProvider = remember(axisRange) {
    with(axisRange) { CartesianLayerRangeProvider.fixed(minX, maxX, minY, maxY) }
}

private suspend fun CartesianChartModelProducer.prepareData(
    chartData: TrainingChartData,
) = runTransaction {
    columnSeries {
        val xy = chartData.primarySeries.entries.map {
            it.map { it.x } to it.map { it.y }
        }.takeIf { it.isNotEmpty() } ?: listOf(listOf(0) to listOf(0))
        xy.forEach { (x, y) -> series(x, y) }
    }

    chartData.secondarySeries.entries.map {
        it.map { it.x } to it.map { it.y }
    }.forEach { (x, y) ->
        lineSeries { series(x, y) }
    }
}

@SuppressLint("RestrictedApi")
@Composable
private fun rememberGraphTypeValueFormatter(
    entryYFormatter: TrainingGraphEntryYFormatter,
    graphType: TrainingGraphType,
) = remember(graphType) {
    when (graphType) {
        TrainingGraphType.DURATION,
        TrainingGraphType.DISTANCE,
        TrainingGraphType.TSS,
        TrainingGraphType.CALORIES,
        TrainingGraphType.AVG_HEART_RATE,
        TrainingGraphType.ASCENT,
        TrainingGraphType.VO2MAX,
        TrainingGraphType.AVG_SPEED,
        TrainingGraphType.AVG_POWER,
        TrainingGraphType.NORMALIZED_POWER -> CartesianValueFormatter.Default

        TrainingGraphType.AVG_PACE -> PaceFormater(entryYFormatter)
        TrainingGraphType.AVG_SWIM_PACE -> SwimPaceFormater(entryYFormatter)
    }
}

private class PaceFormater(
    private val entryYFormatter: TrainingGraphEntryYFormatter,
) : CartesianValueFormatter {

    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?
    ): CharSequence = entryYFormatter.format(value, TrainingGraphType.AVG_PACE).value.orEmpty()
}

private class SwimPaceFormater(
    private val entryYFormatter: TrainingGraphEntryYFormatter,
) : CartesianValueFormatter {

    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?
    ): CharSequence = entryYFormatter.format(value, TrainingGraphType.AVG_SWIM_PACE).value.orEmpty()
}

@Composable
private fun rememberAverageDecoration(
    graphType: TrainingGraphType,
    average: Double?,
    entryYFormatter: TrainingGraphEntryYFormatter,
    alignStart: Boolean,
): Decoration? {
    if (average == null) return null
    val line = rememberLineComponent(
        fill = fill(MaterialTheme.colors.onSurface),
        shape = dashedShape(),
    )
    val labelBackgroundColor = MaterialTheme.colors.onSurface.toArgb()
    return remember(graphType, average) {
        AverageGuideLine(
            y = { average.toDouble() },
            line = line,
            label = { entryYFormatter.format(average, graphType).value.orEmpty() },
            labelColor = Color.White.toArgb(),
            labelBackgroundColor = labelBackgroundColor,
            verticalAxisPosition = if (alignStart) Axis.Position.Vertical.Start else Axis.Position.Vertical.End,
        )
    }
}

@Composable
internal fun TrainingChartHighlightedView(
    chartHighlightedData: TrainingGraphHighlightedData?,
    modifier: Modifier = Modifier,
) {
    if (chartHighlightedData == null) return

    Card(
        modifier = modifier
            .padding(horizontal = MaterialTheme.spacing.medium)
            .fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(8.dp),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            chartHighlightedData.primary?.let {
                HighlightedData(
                    highlightedData = it,
                    modifier = Modifier.weight(1f)
                )
            }

            chartHighlightedData.secondary?.let {
                HighlightedData(
                    highlightedData = it,
                    modifier = Modifier.weight(1f)
                )
            }

            Text(
                text = chartHighlightedData.formattedDate,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colors.onSurface,
                textAlign = TextAlign.End,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun HighlightedData(
    highlightedData: HighlightedData,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall)
    ) {
        Text(
            text = stringResource(highlightedData.labelResId),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colors.primaryVariant,
        )

        Text(
            text = buildString {
                append(highlightedData.formattedValue)
                highlightedData.unitResId?.let { unit ->
                    append(" ${stringResource(unit)}")
                }
            },
            style = MaterialTheme.typography.bodyBold,
            color = MaterialTheme.colors.onSurface,
        )

        Text(
            text = stringResource(highlightedData.typeResId),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colors.primaryVariant,
        )
    }
}
