package com.stt.android.diary.trainingv2

import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.utils.CalendarProvider
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import javax.inject.Inject

class TrainingChartGranularityHelper @Inject constructor(
    private val calendarProvider: CalendarProvider,
) {

    fun flattenToGranularity(date: LocalDate, chartGranularity: ChartGranularity): LocalDate =
        date.atStartOfDay(ZoneId.systemDefault()).run {
            when (chartGranularity) {
                ChartGranularity.WEEKLY,
                ChartGranularity.SEVEN_DAYS,
                ChartGranularity.THIRTY_DAYS,
                ChartGranularity.MONTHLY,
                ChartGranularity.SIX_WEEKS -> truncatedTo(ChronoUnit.DAYS)

                ChartGranularity.SIX_MONTHS ->
                    with(calendarProvider.getDayOfWeekField(), 1)
                        .truncatedTo(ChronoUnit.DAYS)

                ChartGranularity.YEARLY ->
                    with(TemporalAdjusters.firstDayOfMonth()).truncatedTo(ChronoUnit.DAYS)

                ChartGranularity.EIGHT_YEARS ->
                    with(TemporalAdjusters.firstDayOfYear()).truncatedTo(ChronoUnit.DAYS)

                else -> throw IllegalArgumentException("Unsupported chart granularity: $chartGranularity")
            }.toLocalDate()
        }

    fun epochAxisX(date: LocalDate, chartGranularity: ChartGranularity): Long =
        when (chartGranularity) {
            ChartGranularity.WEEKLY,
            ChartGranularity.SEVEN_DAYS,
            ChartGranularity.THIRTY_DAYS,
            ChartGranularity.MONTHLY,
            ChartGranularity.SIX_WEEKS,
            ChartGranularity.SIX_MONTHS -> date.toEpochDay()

            ChartGranularity.YEARLY -> date.epochMonth.toLong()

            ChartGranularity.EIGHT_YEARS -> date.year.toLong()

            else -> throw IllegalArgumentException("Unsupported chart granularity: $chartGranularity")
        }
}
