package com.stt.android.diary.statistics

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.diary.summary.TrainingZoneSummaryColumnsEditActivity
import com.stt.android.diary.summary.TrainingZoneSummaryGrouping
import com.stt.android.diary.summary.TrainingZoneSummaryLayoutType
import com.stt.android.diary.summary.TrainingZoneSummaryViewModel
import com.stt.android.diary.trainingv2.TrainingActivitySheetPickerFragment
import com.stt.android.domain.workouts.SummaryWorkoutHeader
import com.stt.android.premium.PremiumPurchaseFlowLauncher
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class StatisticsFragment : Fragment() {

    val viewModel: TrainingZoneSummaryViewModel by viewModels()

    @Inject
    lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    @Inject
    lateinit var premiumPurchaseFlowLauncher: PremiumPurchaseFlowLauncher

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        premiumPurchaseFlowLauncher.register(this)

        childFragmentManager.setFragmentResultListener(
            TrainingActivitySheetPickerFragment.REQUEST_KEY_SELECTED_ACTIVITIES,
            this
        ) { _, bundle ->
            val ids =
                bundle.getIntegerArrayList(TrainingActivitySheetPickerFragment.BUNDLE_SELECTED_ACTIVITIES)
                    ?: emptyList<Int>()
            CoreActivityType.entries.filter { it.id in ids }.let {
                viewModel.updateSports(it)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        setContentWithTheme {
            val layoutType by viewModel.layoutType.collectAsState()
            val filterUiState by viewModel.filterUiState.collectAsState()
            val summaryTableViewStateNew by viewModel.summaryViewState.collectAsState()
            val mainSorting by viewModel.mainSorting.collectAsState()
            val detailsSorting by viewModel.detailsSorting.collectAsState()
            val summaryDetailsViewState by viewModel.summaryDetailsViewState.collectAsState()
            val isDetailsLoading by viewModel.isDetailsLoading.collectAsState()
            val hasWorkouts by viewModel.hasWorkouts.collectAsState()
            val hasPremium by viewModel.hasPremium.collectAsState()
            val summaryWorkoutSize by viewModel.filteredWorkoutSize.collectAsState()
            val showFilterResetIcon = remember(filterUiState) {
                viewModel.defaultFilterUiState != filterUiState
            }
            val summaryIsLoading by viewModel.summaryIsLoading.collectAsState()

            var isDetailsVisible by rememberSaveable {
                mutableStateOf(false)
            }

            val graphTimeRange by viewModel.graphTimeRange.collectAsState()
            val graphUiState by viewModel.graphUiState.collectAsState()
            val graphHighlightedData by viewModel.graphHighlightedData.collectAsState()
            val currentGraphPage by viewModel.currentGraphPage.collectAsState()
            val isGraphLoading by viewModel.isGraphLoading.collectAsState()

            StatisticsScreen(
                layoutType = layoutType,
                onLayoutTypeClick = viewModel::toggleSelectedLayout,
                onFilterClick = ::openFilter,
                grouping = filterUiState.grouping,
                onGroupingChange = viewModel::updateGrouping,
                workoutsCount = when (layoutType) {
                    TrainingZoneSummaryLayoutType.TABLE -> summaryWorkoutSize
                    TrainingZoneSummaryLayoutType.GRAPH -> currentGraphPage?.countWorkouts ?: 0
                },
                showFilterResetIcon = showFilterResetIcon,
                onFilterResetClick = viewModel::resetFilters,
                onSelectActivitiesClick = {
                    openSportsPicker(filterUiState.sports)
                },
                summaryTableViewStateNew = summaryTableViewStateNew,
                onDateClick = { workouts, date ->
                    if (filterUiState.grouping == TrainingZoneSummaryGrouping.BY_ACTIVITY) {
                        navigateToDetails(workouts.first())
                    } else {
                        viewModel.setSelectedWorkouts(workouts, date)
                        isDetailsVisible = true
                    }
                },
                onHeaderItemClick = viewModel::setMainSorting,
                sortingUiState = mainSorting,
                showColumnsEditButton = true,
                onColumnsEditClick = ::navigateToSummaryColumnsEdit,
                isDetailsVisible = isDetailsVisible,
                isDetailsLoading = isDetailsLoading,
                summaryDetailsViewState = summaryDetailsViewState,
                onDetailsDialogDismiss = {
                    isDetailsVisible = false
                    viewModel.resetSelectedWorkouts()
                },
                onWorkoutClick = ::navigateToDetails,
                onDetailsHeaderItemClick = viewModel::setDetailsSorting,
                detailsSortingUiState = detailsSorting,
                hasPremium = hasPremium,
                openFeaturePromotion = {
                    premiumPurchaseFlowLauncher.launchFeaturePromotionAndAskAboutAppReset(
                        context = requireContext(),
                        analyticsSource = AnalyticsPropertyValue.PremiumPurchaseFlowScreenSource.SUMMARY
                    )
                },
                hasWorkouts = hasWorkouts,
                summaryIsLoading = summaryIsLoading,
                graphTimeRange = graphTimeRange,
                onGraphTimeRangeToggled = viewModel::onGraphTimeRangeToggled,
                chartHighlightedData = graphHighlightedData,
                graphUiState = graphUiState,
                onGraphPageChanged = viewModel::onGraphPageChanged,
                onPrimaryGraphTypeSelected = viewModel::updateTrainingPrimaryGraphType,
                onSecondaryGraphTypeSelected = viewModel::updateTrainingSecondaryGraphType,
                chartHighlightedEvent = viewModel::onGraphHighlightedEntryXEvent,
                isGraphLoading = isGraphLoading,
            )
        }
    }

    private fun navigateToDetails(summaryWorkoutHeader: SummaryWorkoutHeader) {
        rewriteNavigator.navigate(
            context = requireContext(),
            username = summaryWorkoutHeader.username,
            workoutId = summaryWorkoutHeader.id,
            workoutKey = summaryWorkoutHeader.key
        )
    }

    private fun navigateToSummaryColumnsEdit() {
        startActivity(TrainingZoneSummaryColumnsEditActivity.newIntent(requireContext()))
    }

    private fun openSportsPicker(
        selectedActivityTypes: List<CoreActivityType>
    ) {
        TrainingActivitySheetPickerFragment.newInstance(selectedActivityTypes)
            .show(childFragmentManager, "TrainingActivityPickerSheet")
    }

    private fun openFilter() {
        if (childFragmentManager.findFragmentByTag(StatisticsSummaryFilterFragment.TAG) == null) {
            StatisticsSummaryFilterFragment.newInstance()
                .show(childFragmentManager, StatisticsSummaryFilterFragment.TAG)
        }
    }
}
