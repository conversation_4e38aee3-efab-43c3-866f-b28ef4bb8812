package com.stt.android.diary.trainingv2

import androidx.annotation.StringRes
import androidx.compose.runtime.Immutable
import com.stt.android.home.diary.R

@Immutable
sealed class TrainingDateRange {

    object CurrentWeek : TrainingDateRange() {
        @StringRes
        val value: Int = R.string.training_v2_this_week
    }

    object CurrentMonth : TrainingDateRange() {
        @StringRes
        val value: Int = R.string.training_v2_this_month
    }

    object CurrentYear : TrainingDateRange() {
        @StringRes
        val value: Int = R.string.training_v2_this_year
    }

    data class CustomRange(
        val value: String
    ) : TrainingDateRange()
}
