package com.stt.android.diary.statistics

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.RangeSlider
import androidx.compose.material.SliderDefaults
import androidx.compose.material.Switch
import androidx.compose.material.SwitchDefaults
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Info
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.attention
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodyMegaBold
import com.stt.android.compose.theme.bottomSheetShape
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.darkestGrey
import com.stt.android.compose.theme.disabledColor
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberViewInteropNestedScrollConnection
import com.stt.android.compose.widgets.Chip
import com.stt.android.compose.widgets.DraggableBottomSheetHandle
import com.stt.android.diary.summary.DateSelectionType
import com.stt.android.diary.summary.DistanceUiState
import com.stt.android.diary.summary.MeasureViewWidth
import com.stt.android.diary.summary.SummaryDistanceFilterRange
import com.stt.android.diary.summary.SummaryTag
import com.stt.android.diary.summary.TrainingZoneSummaryGrouping
import com.stt.android.diary.summary.rememberFormattedDistanceWithUnit
import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.home.diary.R
import java.util.Locale
import com.stt.android.R as BR
import com.stt.android.core.R as CR

@Composable
internal fun StatisticsSummaryFilter(
    onCloseClick: () -> Unit,
    formattedStartDate: String?,
    formattedEndDate: String?,
    onDateSelectionTypeChange: (DateSelectionType) -> Unit,
    summarySuuntoTags: List<SummaryTag.SummarySuuntoTag>,
    summaryUserTags: List<SummaryTag.SummaryUserTag>,
    suuntoTags: List<SuuntoTag>,
    userTags: List<UserTag>,
    onSelectTagsChange: (List<SummaryTag.SummarySuuntoTag>, List<SummaryTag.SummaryUserTag>) -> Unit,
    distanceUiState: DistanceUiState,
    onDistanceChange: (ClosedFloatingPointRange<Float>) -> Unit,
    isDistanceSupported: Boolean,
    distanceUnit: Int,
    showEmptyRowsChecked: Boolean,
    isShowEmptyRowsEnabled: Boolean,
    onShowEmptyRowsCheckedChange: (Boolean) -> Unit,
    onResetClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val formattedDistanceWithUnit = rememberFormattedDistanceWithUnit(
        distanceUiState = distanceUiState,
        distanceUnit = distanceUnit
    )

    Column(
        modifier = modifier
            .nestedScroll(rememberViewInteropNestedScrollConnection())
            .fillMaxSize()
            .clip(MaterialTheme.shapes.bottomSheetShape)
            .background(color = MaterialTheme.colors.surface)
    ) {
        DraggableBottomSheetHandle(bottomPadding = 0.dp)

        FilterTopBar(
            onDoneClick = onCloseClick,
            modifier = Modifier
                .padding(start = MaterialTheme.spacing.smaller)
        )
        Divider()

        Column(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
        ) {
            FilterItem(
                label = stringResource(R.string.training_zone_summary_filter_date_start),
                onClick = {
                    onDateSelectionTypeChange(DateSelectionType.START)
                },
                valueContent = {
                    formattedStartDate?.let {
                        Text(
                            text = it,
                            style = MaterialTheme.typography.bodyBold,
                            color = MaterialTheme.colors.primary,
                            modifier = Modifier
                                .padding(end = MaterialTheme.spacing.medium)
                        )
                    }
                }
            )

            FilterItem(
                label = stringResource(R.string.training_zone_summary_filter_date_end),
                onClick = {
                    onDateSelectionTypeChange(DateSelectionType.END)
                },
                valueContent = {
                    formattedEndDate?.let {
                        Text(
                            text = it,
                            style = MaterialTheme.typography.bodyBold,
                            color = MaterialTheme.colors.primary,
                            modifier = Modifier
                                .padding(end = MaterialTheme.spacing.medium)
                        )
                    }
                }
            )

            FilterItem(
                label = stringResource(R.string.training_zone_summary_filter_tags_title),
                valueContent = {
                    val enabled = summarySuuntoTags.isNotEmpty() || summaryUserTags.isNotEmpty()
                    TextButton(
                        enabled = enabled,
                        onClick = { onSelectTagsChange(emptyList(), emptyList()) },
                        modifier = Modifier
                            .padding(end = MaterialTheme.spacing.small)
                    ) {
                        Text(
                            text = stringResource(R.string.statistics_filter_tags_clear_all).uppercase(
                                Locale.getDefault()
                            ),
                            style = MaterialTheme.typography.bodyBold,
                            color = if (enabled) MaterialTheme.colors.primary else MaterialTheme.colors.cloudyGrey
                        )
                    }
                },
                content = {
                    TagsContent(
                        summarySuuntoTags = summarySuuntoTags,
                        summaryUserTags = summaryUserTags,
                        suuntoTags = suuntoTags,
                        userTags = userTags,
                        onSuuntoTagClick = { tag, isSelected ->
                            val selectedSuuntoTags = if (isSelected) {
                                summarySuuntoTags + SummaryTag.SummarySuuntoTag(
                                    suuntoTag = tag,
                                    timestamp = System.currentTimeMillis()
                                )
                            } else {
                                summarySuuntoTags.filter { it.suuntoTag != tag }
                            }
                            onSelectTagsChange(selectedSuuntoTags, summaryUserTags)
                        },
                        onUserTagClick = { tag, isSelected ->
                            val selectUserTags = if (isSelected) {
                                summaryUserTags + SummaryTag.SummaryUserTag(
                                    userTag = tag,
                                    timestamp = System.currentTimeMillis()
                                )
                            } else {
                                summaryUserTags.filter { it.userTag != tag }
                            }
                            onSelectTagsChange(summarySuuntoTags, selectUserTags)
                        }
                    )
                }
            )

            FilterItem(
                label = stringResource(R.string.training_zone_summary_filter_distance),
                valueContent = {
                    Text(
                        text = buildAnnotatedString {
                            append(formattedDistanceWithUnit.first)
                            formattedDistanceWithUnit.second?.let {
                                append(" ${stringResource(it)}")
                            }
                        },
                        style = MaterialTheme.typography.bodyBold,
                        color = MaterialTheme.colors.primary,
                        modifier = Modifier
                            .padding(end = MaterialTheme.spacing.medium)
                    )
                },
                content = {
                    DistanceContent(
                        distanceUiState = distanceUiState,
                        onDistanceChange = onDistanceChange,
                        isDistanceSupported = isDistanceSupported,
                        distanceUnit = distanceUnit
                    )
                }
            )

            FilterItem(
                label = stringResource(R.string.statistics_table_settings),
                content = {
                    TableSettingsContent(
                        showEmptyRowsChecked = showEmptyRowsChecked,
                        onShowEmptyRowsCheckedChange = onShowEmptyRowsCheckedChange,
                        isEnabled = isShowEmptyRowsEnabled,
                    )
                }
            )

            Divider()

            TextButton(
                onClick = onResetClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = MaterialTheme.spacing.medium)
            ) {
                Text(
                    text = stringResource(R.string.training_zone_summary_filter_reset)
                        .uppercase(Locale.getDefault())
                )
            }
        }
    }
}

@Composable
private fun FilterTopBar(
    onDoneClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                horizontal = MaterialTheme.spacing.xsmall,
                vertical = MaterialTheme.spacing.medium
            ),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = stringResource(R.string.statistics_filters),
            color = MaterialTheme.colors.onSurface,
            style = MaterialTheme.typography.bodyMegaBold
        )

        TextButton(
            onClick = onDoneClick
        ) {
            Text(
                text = stringResource(BR.string.save),
                color = MaterialTheme.colors.primary,
                style = MaterialTheme.typography.bodyBold
            )
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun GroupByContent(
    grouping: TrainingZoneSummaryGrouping,
    onGroupingChange: (TrainingZoneSummaryGrouping) -> Unit,
    modifier: Modifier = Modifier,
) {
    FlowRow(
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        modifier = modifier
            .padding(MaterialTheme.spacing.medium)
    ) {
        TrainingZoneSummaryGrouping.entries.forEach { value ->
            Chip(
                isSelected = value == grouping,
                onChecked = { onGroupingChange(value) },
                modifier = Modifier
                    .sizeIn(
                        minWidth = dimensionResource(BR.dimen.tag_editable_chip_min_width),
                        minHeight = dimensionResource(BR.dimen.tag_editable_chip_min_height)
                    )
            ) {
                Text(
                    text = stringResource(
                        when (value) {
                            TrainingZoneSummaryGrouping.WEEKLY -> R.string.training_zone_summary_filter_grouping_weekly
                            TrainingZoneSummaryGrouping.MONTHLY -> R.string.training_zone_summary_filter_grouping_monthly
                            TrainingZoneSummaryGrouping.YEARLY -> R.string.training_zone_summary_filter_grouping_yearly
                            TrainingZoneSummaryGrouping.BY_ACTIVITY -> R.string.training_zone_summary_filter_grouping_by_activity
                        }
                    ),
                    style = MaterialTheme.typography.body,
                    color = if (value == grouping) {
                        Color.White
                    } else {
                        MaterialTheme.colors.onSurface
                    },
                    modifier = Modifier
                        .padding(horizontal = MaterialTheme.spacing.small)
                )
            }
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun TagsContent(
    summarySuuntoTags: List<SummaryTag.SummarySuuntoTag>,
    summaryUserTags: List<SummaryTag.SummaryUserTag>,
    suuntoTags: List<SuuntoTag>,
    userTags: List<UserTag>,
    onSuuntoTagClick: (SuuntoTag, Boolean) -> Unit,
    onUserTagClick: (UserTag, Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    val selectedSuuntoTags = remember(summarySuuntoTags) {
        summarySuuntoTags.map { it.suuntoTag }
    }
    val selectedUserTags = remember(summaryUserTags) {
        summaryUserTags.map { it.userTag }
    }

    FlowRow(
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        modifier = modifier
            .padding(MaterialTheme.spacing.medium)
    ) {
        suuntoTags.forEach { tag ->
            val isSelected = selectedSuuntoTags.contains(tag)
            Chip(
                isSelected = isSelected,
                onChecked = {
                    onSuuntoTagClick(tag, !isSelected)
                },
                modifier = Modifier
                    .sizeIn(
                        minWidth = dimensionResource(BR.dimen.tag_editable_chip_min_width),
                        minHeight = dimensionResource(BR.dimen.tag_editable_chip_min_height)
                    )
            ) {
                Text(
                    text = stringResource(tag.nameRes),
                    style = MaterialTheme.typography.body,
                    color = if (isSelected) {
                        Color.White
                    } else {
                        MaterialTheme.colors.onSurface
                    },
                    modifier = Modifier
                        .padding(horizontal = MaterialTheme.spacing.small)
                )
            }
        }

        userTags.forEach { tag ->
            val isSelected = selectedUserTags.contains(tag)
            Chip(
                isSelected = isSelected,
                onChecked = {
                    onUserTagClick(tag, !isSelected)
                },
                modifier = Modifier
                    .sizeIn(
                        minWidth = dimensionResource(BR.dimen.tag_editable_chip_min_width),
                        minHeight = dimensionResource(BR.dimen.tag_editable_chip_min_height)
                    )
            ) {
                Text(
                    text = tag.name,
                    style = MaterialTheme.typography.body,
                    color = if (isSelected) {
                        Color.White
                    } else {
                        MaterialTheme.colors.onSurface
                    },
                    modifier = Modifier
                        .padding(horizontal = MaterialTheme.spacing.small)
                )
            }
        }
    }
}

@Composable
private fun TableSettingsContent(
    showEmptyRowsChecked: Boolean,
    onShowEmptyRowsCheckedChange: (Boolean) -> Unit,
    isEnabled: Boolean,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(
                start = MaterialTheme.spacing.medium,
                top = MaterialTheme.spacing.small,
                end = MaterialTheme.spacing.small,
                bottom = MaterialTheme.spacing.small
            ),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = stringResource(R.string.training_v2_training_data_analysis_table_show_inactive_period),
            style = MaterialTheme.typography.bodyLarge,
            color = if (isEnabled) MaterialTheme.colors.onSurface else MaterialTheme.colors.disabledColor
        )
        Switch(
            checked = showEmptyRowsChecked,
            onCheckedChange = onShowEmptyRowsCheckedChange,
            colors = SwitchDefaults.colors(
                checkedTrackColor = MaterialTheme.colors.primary,
                checkedThumbColor = MaterialTheme.colors.primary,
                disabledCheckedThumbColor = MaterialTheme.colors.disabledColor,
                disabledCheckedTrackColor = MaterialTheme.colors.disabledColor,
            ),
            enabled = isEnabled
        )
    }
}

@SuppressLint("UnusedBoxWithConstraintsScope")
@OptIn(ExperimentalMaterialApi::class)
@Composable
internal fun DistanceContent(
    distanceUiState: DistanceUiState,
    onDistanceChange: (ClosedFloatingPointRange<Float>) -> Unit,
    isDistanceSupported: Boolean,
    distanceUnit: Int,
    modifier: Modifier = Modifier,
) {
    val distance by remember(distanceUiState) {
        mutableStateOf(
            run {
                val (min, max) = DistanceUiState.toFilterRange(distanceUiState)
                min..max
            }
        )
    }

    Column(
        modifier = modifier
            .padding(MaterialTheme.spacing.medium)
    ) {
        RangeSlider(
            value = distance,
            onValueChange = onDistanceChange,
            valueRange = SummaryDistanceFilterRange,
            colors = SliderDefaults.colors(
                activeTrackColor = MaterialTheme.colors.nearBlack,
                inactiveTrackColor = MaterialTheme.colors.cloudyGrey,
                thumbColor = MaterialTheme.colors.nearBlack,
                disabledActiveTrackColor = MaterialTheme.colors.lightGrey,
                disabledInactiveTrackColor = MaterialTheme.colors.lightGrey
            ),
            enabled = isDistanceSupported,
            modifier = Modifier
                .semantics { contentDescription = "DistanceRangeSlider" }
        )
        BoxWithConstraints(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = stringResource(
                    id = BR.string.value_unit,
                    "0",
                    stringResource(distanceUnit)
                ),
                modifier = Modifier.align(Alignment.CenterStart),
                color = if (isDistanceSupported) MaterialTheme.colors.darkGrey else MaterialTheme.colors.cloudyGrey
            )
            MeasureViewWidth(
                viewToMeasure = {
                    Text(
                        text = stringResource(
                            id = BR.string.value_unit,
                            "30",
                            stringResource(distanceUnit)
                        )
                    )
                }
            ) {
                Text(
                    text = stringResource(
                        id = BR.string.value_unit,
                        "30",
                        stringResource(distanceUnit)
                    ),
                    modifier = Modifier.offset(
                        x = (maxWidth.times(30)).div(
                            SummaryDistanceFilterRange.endInclusive
                        ).minus(it.div(2))
                    ), // To centralize the thumb with the text
                    color = if (isDistanceSupported) MaterialTheme.colors.darkGrey else MaterialTheme.colors.cloudyGrey
                )
            }
            Text(
                text = stringResource(
                    id = BR.string.value_unit,
                    "100+",
                    stringResource(distanceUnit)
                ),
                modifier = Modifier.align(Alignment.CenterEnd),
                color = if (isDistanceSupported) MaterialTheme.colors.darkGrey else MaterialTheme.colors.cloudyGrey
            )
        }
        if (!isDistanceSupported) {
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Outlined.Info,
                    contentDescription = null,
                    tint = MaterialTheme.colors.attention
                )
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
                Text(
                    text = stringResource(R.string.training_zone_summary_filter_distance_select_sport_with_distance),
                    style = MaterialTheme.typography.body,
                    color = MaterialTheme.colors.darkestGrey,
                    modifier = Modifier.semantics {
                        contentDescription = "DistanceNotSupportedWarn"
                    }
                )
            }
        }
    }
}

@Composable
internal fun FilterItem(
    label: String,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
    valueContent: (@Composable RowScope.() -> Unit)? = null,
    content: (@Composable ColumnScope.() -> Unit)? = null,
    showDivider: Boolean = true,
) {
    Column(
        modifier = modifier
            .clickableThrottleFirst(
                enabled = onClick != null,
                onClick = { onClick?.invoke() }
            )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                color = MaterialTheme.colors.onSurface,
                style = MaterialTheme.typography.bodyLargeBold,
                modifier = Modifier
                    .padding(start = MaterialTheme.spacing.medium)
            )

            if (valueContent != null) {
                valueContent()
            }
        }

        if (showDivider) {
            Divider()
        }

        if (content != null) {
            content()
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun DataAnalysisFilterPreview() {
    AppTheme {
        StatisticsSummaryFilter(
            onCloseClick = {},
            formattedStartDate = null,
            formattedEndDate = null,
            onDateSelectionTypeChange = {},
            summarySuuntoTags = emptyList(),
            summaryUserTags = emptyList(),
            suuntoTags = SuuntoTag.entries,
            userTags = emptyList(),
            onSelectTagsChange = { _, _ -> },
            distanceUiState = DistanceUiState(
                minDistance = DistanceUiState.RangeValue.Exact(10),
                maxDistance = DistanceUiState.RangeValue.Exact(10)
            ),
            onDistanceChange = { _ -> },
            distanceUnit = CR.string.km,
            isDistanceSupported = false,
            showEmptyRowsChecked = true,
            onShowEmptyRowsCheckedChange = {},
            isShowEmptyRowsEnabled = true,
            onResetClick = {},
        )
    }
}
