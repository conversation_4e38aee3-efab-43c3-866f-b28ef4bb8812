package com.stt.android.diary.progress

import androidx.fragment.app.Fragment
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.home.diary.R
import com.stt.android.diary.DiaryFragmentInfo
import javax.inject.Inject

class ProgressFragmentInfo @Inject constructor() : DiaryFragmentInfo {
    override fun clazz(): Class<out Fragment> = ProgressFragment::class.java
    override fun getTitleResId(): Int = R.string.progress_tab_title
    override fun getTabTypeForAnalytics(): String = AnalyticsPropertyValue.SuuntoDiaryType.PROGRESS
}
