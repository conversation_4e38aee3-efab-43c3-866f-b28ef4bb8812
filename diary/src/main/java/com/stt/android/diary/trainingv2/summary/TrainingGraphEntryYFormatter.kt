package com.stt.android.diary.trainingv2.summary

import com.amersports.formatter.Unit
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.diary.summary.BasicWorkoutValue
import com.stt.android.diary.summary.TrainingZoneSummaryFormatter
import com.stt.android.mapping.InfoModelFormatter
import javax.inject.Inject
import kotlin.math.roundToInt

class TrainingGraphEntryYFormatter @Inject constructor(
    private val trainingSummaryFormatter: TrainingZoneSummaryFormatter,
    private val infoModelFormatter: InfoModelFormatter,
) {
    private val unitConverter = JScienceUnitConverter()

    fun format(entryY: Double, graphType: TrainingGraphType): BasicWorkoutValue {
        return when (graphType) {
            TrainingGraphType.DURATION ->
                trainingSummaryFormatter.formatDuration(entryY * 3600)

            TrainingGraphType.DISTANCE ->
                trainingSummaryFormatter.formatDistance(
                    infoModelFormatter.unit.fromDistanceUnit(entryY)
                )

            TrainingGraphType.TSS ->
                trainingSummaryFormatter.formatTss(entryY.toFloat())

            TrainingGraphType.CALORIES -> trainingSummaryFormatter.formatEnergy(
                unitConverter.convert(entryY, Unit.KCAL, Unit.J).roundToInt()
            )

            TrainingGraphType.AVG_HEART_RATE ->
                trainingSummaryFormatter.formatAvgHeartRate(
                    infoModelFormatter.unit.fromRpmToHz(entryY)
                )

            TrainingGraphType.ASCENT ->
                trainingSummaryFormatter.formatAscent(
                    infoModelFormatter.unit.fromAltitudeUnit(entryY)
                )

            TrainingGraphType.VO2MAX ->
                trainingSummaryFormatter.formatVo2Peak(entryY.toFloat())

            TrainingGraphType.AVG_SPEED ->
                trainingSummaryFormatter.formatAvgSpeed(
                    infoModelFormatter.unit.fromSpeedUnit(entryY)
                )

            TrainingGraphType.AVG_POWER ->
                trainingSummaryFormatter.formatAvgPower(entryY)

            TrainingGraphType.NORMALIZED_POWER ->
                trainingSummaryFormatter.formatNormalizedPower(entryY)

            TrainingGraphType.AVG_PACE ->
                trainingSummaryFormatter.formatAvgPace(
                    infoModelFormatter.unit.fromPaceUnit(entryY)
                )

            TrainingGraphType.AVG_SWIM_PACE -> trainingSummaryFormatter.formatAvgSwimPace(
                infoModelFormatter.unit.fromSwimPaceUnit(entryY)
            )
        }
    }
}
