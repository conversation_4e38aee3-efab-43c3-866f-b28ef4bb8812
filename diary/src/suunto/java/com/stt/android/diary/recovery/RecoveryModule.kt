package com.stt.android.diary.recovery

import android.content.Context
import android.content.Intent
import com.stt.android.common.ui.ViewPagerFragmentCreator
import com.stt.android.diary.recovery.v2.RecoveryFragmentInfo
import com.stt.android.diary.recovery.v2.RecoveryV2Activity
import com.stt.android.diary.DiaryFragmentInfo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Inject
import javax.inject.Named

@Module
@InstallIn(SingletonComponent::class)
abstract class RecoveryModule {
    @Binds
    @Named("RECOVERY_FRAGMENT_CREATOR")
    abstract fun bindActivityFragmentCreator(creator: RecoveryFragmentCreator): ViewPagerFragmentCreator

    @Binds
    @Named("RECOVERY_FRAGMENT_INFO")
    abstract fun bindRecoveryFragmentInfo(creator: RecoveryFragmentInfo): DiaryFragmentInfo

    @Binds
    abstract fun bindRecoveryNavigator(navigator: RecoveryNavigatorImpl): RecoveryNavigator
}

class RecoveryNavigatorImpl @Inject constructor() : RecoveryNavigator {
    override fun launchRecoveryV2(context: Context) {
        context.startActivity(Intent(context, RecoveryV2Activity::class.java))
    }
}
