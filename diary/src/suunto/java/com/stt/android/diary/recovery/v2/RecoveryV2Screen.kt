package com.stt.android.diary.recovery.v2

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.insights.common.TrainingHubDatePicker
import com.stt.android.diary.insights.common.TrainingHubDateRange
import com.stt.android.diary.recovery.composables.ExtraTimeGranularityBottomSheet
import com.stt.android.domain.diary.models.RecoveryStateContributor
import com.stt.android.diary.recovery.composables.RecoveryTimeSelection
import com.stt.android.diary.recovery.composables.SleepComparisonGraphSelectionBottomSheet

@Composable
fun RecoveryV2Screen(
    onShowRecoveryStateInfoSheet: () -> Unit,
    modifier: Modifier = Modifier,
    onContributorClick: (RecoveryStateContributor) -> Unit = {},
    viewModel: RecoveryV2ViewModel = hiltViewModel(),
) {
    val recoveryV2State by viewModel.viewState.collectAsState()
    
    viewModel.setShowRecoveryStateInfoSheetCallback(onShowRecoveryStateInfoSheet)
    viewModel.setContributorClickCallback(onContributorClick)
    
    Box(modifier = modifier.fillMaxSize()) {
        when (recoveryV2State) {
            is RecoveryV2State.Loading -> {
                RecoveryV2Loading(modifier = Modifier.fillMaxSize())
            }
            
            is RecoveryV2State.Loaded -> {
                val loadedState = recoveryV2State as RecoveryV2State.Loaded
                
                Column {
                    RecoveryTimeSelection(
                        mainTimeGranularities = loadedState.mainTimeGranularities,
                        extraTimeGranularities = loadedState.extraTimeGranularities,
                        currentTimeGranularity = loadedState.currentTimeGranularity,
                        onEvent = viewModel::onEvent,
                        modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium,
                            vertical = MaterialTheme.spacing.small)
                    )
                    
                    val datePickerData = loadedState.datePickerData
                    TrainingHubDatePicker(
                        trainingHubDateRange = TrainingHubDateRange.CustomRange(datePickerData.displayTitle),
                        onNextClick = { viewModel.onEvent(RecoveryV2Event.NavigateNext) },
                        onPreviousClick = { viewModel.onEvent(RecoveryV2Event.NavigatePrevious) },
                        isNextEnabled = datePickerData.canNavigateForward,
                        isPreviousEnabled = datePickerData.canNavigateBack,
                        isLoading = false,
                    )

                    RecoveryV2DailyContent(
                        recoveryDailyStateData = loadedState.recoveryDailyStateData,
                        onEvent = viewModel::onEvent,
                        modifier = Modifier,
                    )

                    RecoveryV2ChartContent(
                        recoveryChartStateData = loadedState.recoveryChartStateData,
                        onEvent = viewModel::onEvent,
                        modifier = Modifier,
                    )
                }
                
                ExtraTimeGranularityBottomSheet(
                    extraTimeGranularities = loadedState.extraTimeGranularities,
                    currentTimeGranularity = loadedState.currentTimeGranularity,
                    showExtraTimeGranularitySelection = loadedState.showExtraTimeGranularitySelection,
                    onEvent = viewModel::onEvent
                )

                SleepComparisonGraphSelectionBottomSheet(
                    state = loadedState,
                    onEvent = viewModel::onEvent
                )
            }
        }
    }
}




@Composable
private fun RecoveryV2Loading(
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier,
    ) {
        CircularProgressIndicator(
            modifier = Modifier.size(32.dp),
            color = color,
        )
    }
}

