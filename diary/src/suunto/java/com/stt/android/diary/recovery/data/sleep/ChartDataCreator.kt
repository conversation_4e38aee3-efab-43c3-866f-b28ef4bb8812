package com.stt.android.diary.recovery.data.sleep

import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.api.model.ChartGranularity.DAILY
import com.stt.android.chart.api.model.ChartGranularity.EIGHT_YEARS
import com.stt.android.chart.api.model.ChartGranularity.MONTHLY
import com.stt.android.chart.api.model.ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.SEVEN_DAYS
import com.stt.android.chart.api.model.ChartGranularity.SIXTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.SIX_MONTHS
import com.stt.android.chart.api.model.ChartGranularity.SIX_WEEKS
import com.stt.android.chart.api.model.ChartGranularity.THIRTY_DAYS
import com.stt.android.chart.api.model.ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS
import com.stt.android.chart.api.model.ChartGranularity.WEEKLY
import com.stt.android.chart.api.model.ChartGranularity.YEARLY
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.model.epochMonth
import com.stt.android.controllers.UserSettingsController
import com.stt.android.diary.recovery.data.sleep.ChartDataFormatterUtils.toLocalDate
import com.stt.android.diary.recovery.model.SleepChartData
import com.stt.android.diary.recovery.v2.SleepChartSelectionType
import com.stt.android.domain.sleep.Sleep
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters
import kotlin.math.max
import kotlin.math.min
import javax.inject.Inject

class ChartDataCreator @Inject constructor(
    private val userSettingsController: UserSettingsController,
) {

    fun createSleepGraphData(
        chartGranularity: ChartGranularity,
        chartType: ChartType,
        from: LocalDate,
        to: LocalDate,
        sleepList: List<Sleep>,
        dataExtractor: (Sleep) -> Float?,
        dataConverter: (Float) -> Float,
        yRangeConverter: (Float, Float) -> Pair<Float, Float>,
    ): SleepChartData.Series {
        var minX = 0.0
        var maxX = 0.0
        var minY = Float.MAX_VALUE
        var maxY = 0f
        val average = Average()
        val entries = when (chartGranularity) {
            WEEKLY,
            SEVEN_DAYS,
            MONTHLY,
            THIRTY_DAYS,
            SIX_WEEKS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                sleepList
                    .mapNotNull { sleep ->
                        dataExtractor(sleep)?.takeIf { it > 0f }?.let { value ->
                            minY = min(minY, value)
                            maxY = max(maxY, value)
                            average.feed(value)
                            SleepChartData.Entry(
                                x = sleep.timestamp.toLocalDate().toEpochDay(),
                                high = dataConverter(value),
                            )
                        }
                    }
                    .splitBySteps(chartType, 1)
            }

            SIX_MONTHS -> {
                minX = from.toEpochDay().toDouble()
                maxX = to.toEpochDay().toDouble()
                val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek
                sleepList
                    .groupBy { sleep ->
                        sleep.timestamp.toLocalDate()
                            .with((TemporalAdjusters.previousOrSame(firstDayOfWeek)))
                    }
                    .mapNotNull { (startOfWeek: LocalDate, weeklySleepList: List<Sleep>) ->
                        val weeklyAverage = Average()
                        weeklySleepList.forEach { sleep ->
                            dataExtractor(sleep)?.takeIf { it > 0f }?.let { value ->
                                weeklyAverage.feed(value)
                                average.feed(value)
                            }
                        }
                        weeklyAverage.result.takeIf { it > 0f }?.let { value ->
                            minY = min(minY, value)
                            maxY = max(maxY, value)
                            SleepChartData.Entry(
                                x = startOfWeek.toEpochDay(),
                                high = dataConverter(value),
                            )
                        }
                    }
                    .splitBySteps(chartType, 7)
            }

            YEARLY -> {
                minX = from.epochMonth.toDouble()
                maxX = to.epochMonth.toDouble()
                sleepList
                    .groupBy { sleep ->
                        sleep.timestamp.toLocalDate().epochMonth
                    }
                    .mapNotNull { (epochMonth: Int, monthlySleepList: List<Sleep>) ->
                        val monthlyAverage = Average()
                        monthlySleepList.forEach { sleep ->
                            dataExtractor(sleep)?.takeIf { it > 0f }?.let { value ->
                                monthlyAverage.feed(value)
                                average.feed(value)
                            }
                        }
                        monthlyAverage.result.takeIf { it > 0f }?.let { value ->
                            minY = min(minY, value)
                            maxY = max(maxY, value)
                            SleepChartData.Entry(
                                x = epochMonth.toLong(),
                                high = dataConverter(value),
                            )
                        }
                    }
                    .splitBySteps(chartType, 1)
            }

            DAILY,
            SIXTY_DAYS,
            ONE_HUNDRED_EIGHTY_DAYS,
            THREE_HUNDRED_SIXTY_FIVE_DAYS,
            EIGHT_YEARS -> throw Exception("Unsupported chart granularity: $chartGranularity")
        }
        if (minY == Float.MAX_VALUE || minY == maxY) {
            minY = 0f
        }
        val convertedMinY = dataConverter(minY)
        val convertedMaxY = dataConverter(maxY)
        val (adjustedMinY, adjustedMaxY) = yRangeConverter(convertedMinY, convertedMaxY)
        return SleepChartData.Series(
            selectionType = SleepChartSelectionType.NONE,
            chartType = chartType,
            axisRange = SleepChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = adjustedMinY.toDouble(),
                maxY = adjustedMaxY.toDouble(),
            ),
            entries = entries,
            average = dataConverter(average.result),
        )
    }

    private fun List<SleepChartData.Entry>.splitBySteps(step: Long): List<List<SleepChartData.Entry>> {
        if (isEmpty()) return listOf(this)
        return buildList {
            val currentList = mutableListOf<SleepChartData.Entry>()
            <EMAIL> { entry ->
                if (currentList.isEmpty() || currentList.last().x + step == entry.x || currentList.last().x - step == entry.x) {
                    currentList.add(entry)
                } else {
                    add(currentList.toList())
                    currentList.clear()
                    currentList.add(entry)
                }
            }
            if (currentList.isNotEmpty()) {
                add(currentList)
            }
        }
    }

    private fun List<SleepChartData.Entry>.splitBySteps(
        chartType: ChartType,
        step: Long,
    ) = if (chartType == ChartType.LINE) splitBySteps(step) else listOf(this)

    class Average {
        var result = 0f
            private set
        private var total = 0

        fun feed(value: Float) {
            result += (value - result) / ++total
        }
    }
} 
