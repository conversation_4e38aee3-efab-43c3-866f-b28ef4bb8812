package com.stt.android.diary.recovery.v2

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.recovery.composables.SuuntoCoachMessage
import com.stt.android.diary.recovery.composables.RecoveryStateHeader
import com.stt.android.diary.recovery.composables.recoveryStateContributorsItems
import com.stt.android.home.diary.R as DiaryR

@Composable
internal fun RecoveryV2DailyContent(
    recoveryDailyStateData: RecoveryDailyStateData,
    onEvent: (RecoveryV2Event) -> Unit,
    modifier: Modifier = Modifier,
) {
    if (recoveryDailyStateData is RecoveryDailyStateData.Loaded) {
        val recoveryStateData = recoveryDailyStateData.recoveryStateData
        LazyColumn(modifier = modifier) {
            item {
                SuuntoCoachMessage(
                    titleRes = DiaryR.string.suunto_coach_message_title,
                    recoveryZone = recoveryStateData.recoveryZone,
                    modifier = Modifier.padding(
                        start = MaterialTheme.spacing.medium,
                        end = MaterialTheme.spacing.medium,
                        bottom = MaterialTheme.spacing.medium
                    ),
                )
            }
            item {
                RecoveryStateHeader(
                    recoveryScore = recoveryStateData.recoveryScore,
                    recoveryZone = recoveryStateData.recoveryZone,
                    onEvent = onEvent,
                    modifier =  Modifier.padding(
                        start = MaterialTheme.spacing.medium,
                        bottom = MaterialTheme.spacing.medium
                    ),
                )
            }
            recoveryStateData.contributors?.let {
                recoveryStateContributorsItems(
                    it,
                    onHeaderClick = { onEvent(RecoveryV2Event.ShowRecoveryStateInfoSheet) },
                    onContributorClick = { contributor -> onEvent(RecoveryV2Event.ContributorClicked(contributor)) },
                )
            }
        }
    }
} 
