package com.stt.android.diary.recovery.composables

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.stt.android.R as BaseR
import com.stt.android.home.diary.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.diary.recovery.v2.RecoveryV2Event
import com.stt.android.domain.diary.models.RecoveryStateData
import com.stt.android.domain.diary.models.RecoveryZone
import com.stt.android.utils.zoneColorRes


@Composable
internal fun RecoveryStateHeader(
    recoveryZone:RecoveryZone,
    recoveryScore:Int,
    onEvent: (RecoveryV2Event) -> Unit,
    modifier: Modifier = Modifier,
    isDaily: Boolean = true,
) {
    val zoneColor = colorResource(id = recoveryZone.zoneColorRes())
    val dateTimeText = if (isDaily) {
        stringResource(id = R.string.recovery_state_date_time, "")
    } else {
        stringResource(id = R.string.recovery_daily_average)
    }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surface),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        RecoveryStateHeader(
            titleRes = R.string.recovery_state_title,
            onInfoClick = { onEvent(RecoveryV2Event.ShowRecoveryStateInfoSheet) },
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(end = MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = if (recoveryScore > 0) "${recoveryScore}%" else "--",
                    style = MaterialTheme.typography.bodyMegaBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = dateTimeText,
                    style = MaterialTheme.typography.body,
                    color = Color.Gray
                )
            }
            
            when (recoveryZone) {
                RecoveryZone.OPTIMAL -> {
                    RecoveryStateChip(
                        text = stringResource(id = BaseR.string.recovery_state_optimal),
                        color = zoneColor
                    )
                }
                RecoveryZone.GOOD -> {
                    RecoveryStateChip(
                        text = stringResource(id = BaseR.string.recovery_state_good),
                        color = zoneColor
                    )
                }
                RecoveryZone.FAIR -> {
                    RecoveryStateChip(
                        text = stringResource(id = BaseR.string.recovery_state_fair),
                        color = zoneColor
                    )
                }
                RecoveryZone.POOR -> {
                    RecoveryStateChip(
                        text = stringResource(id = BaseR.string.recovery_state_poor),
                        color = zoneColor
                    )
                }
                RecoveryZone.LIMITED -> {
                    RecoveryStateChip(
                        text = stringResource(id = BaseR.string.recovery_state_limited),
                        color = zoneColor
                    )
                }
                RecoveryZone.NO_DATA -> {
                    RecoveryStateChip(
                        text = stringResource(id = BaseR.string.recovery_state_not_enough_data),
                        color = zoneColor
                    )
                }
            }
        }

        if (isDaily) {
            RecoveryProgressBar(
                progress = recoveryScore / 100f,
                color = zoneColor,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(end = MaterialTheme.spacing.medium)
            )
        }
    }
}

@Composable
fun RecoveryStateHeader(
    @StringRes titleRes: Int,
    onInfoClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier.padding(
            bottom = MaterialTheme.spacing.medium,
            end = MaterialTheme.spacing.xsmall,)
    ) {
        Text(
            text = stringResource(id = titleRes),
            style = MaterialTheme.typography.bodyMegaBold,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.weight(1f)
        )

        IconButton(onClick = onInfoClick) {
            Icon(
                painter = painterResource(id = com.stt.android.R.drawable.ic_info_outline),
                contentDescription = stringResource(id = R.string.recovery_tab_description),
                modifier = Modifier.size(MaterialTheme.iconSizes.small),
            )
        }
    }
}


@Composable
private fun RecoveryStateChip(
    text: String,
    color: Color,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .height(MaterialTheme.spacing.xxlarge)
            .clip(RoundedCornerShape(MaterialTheme.spacing.small))
            .padding(horizontal = MaterialTheme.spacing.medium, vertical = MaterialTheme.spacing.xsmall),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodyXLargeBold,
            color = color,
            textAlign = TextAlign.Center
        )
    }
}


@Composable
private fun RecoveryProgressBar(
    progress: Float,
    color: Color,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .height(MaterialTheme.spacing.xsmaller)
            .clip(RoundedCornerShape(MaterialTheme.spacing.xsmaller))
            .background(MaterialTheme.colorScheme.nearWhite)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth(fraction = progress)
                .fillMaxHeight()
                .background(color)
        )
    }
}

@Preview
@Composable
private fun RecoveryStateDisplayPreview(
    @PreviewParameter(RecoveryStateProviderForPreview::class) recoveryStateData: RecoveryStateData
) {
    M3AppTheme {
        Surface {
            RecoveryStateHeader(
                recoveryScore = recoveryStateData.recoveryScore,
                recoveryZone = recoveryStateData.recoveryZone,
                onEvent = {

                }
            )
        }
    }
}

@Preview
@Composable
private fun RecoveryStateChipPreview() {
    M3AppTheme {
        Surface {
            RecoveryStateChip(
                text = "Fair recovery",
                color = colorResource(id = BaseR.color.recovery_zone_fair)
            )
        }
    }
}

private class RecoveryStateProviderForPreview : PreviewParameterProvider<RecoveryStateData> {
    override val values: Sequence<RecoveryStateData> = sequenceOf(
        RecoveryStateData(0, RecoveryZone.NO_DATA),
        RecoveryStateData(12, RecoveryZone.LIMITED),
        RecoveryStateData(23, RecoveryZone.POOR),
        RecoveryStateData(59, RecoveryZone.FAIR),
        RecoveryStateData(67, RecoveryZone.GOOD),
        RecoveryStateData(89, RecoveryZone.OPTIMAL),
    )
} 
