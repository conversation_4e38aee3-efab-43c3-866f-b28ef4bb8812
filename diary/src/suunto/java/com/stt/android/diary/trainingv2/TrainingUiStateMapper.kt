package com.stt.android.diary.trainingv2

import android.content.Context
import android.graphics.Color
import androidx.annotation.ColorInt
import androidx.annotation.IntRange
import androidx.compose.ui.text.AnnotatedString
import com.soy.algorithms.coach.CoachPhrase
import com.soy.algorithms.coach.SuuntoCoachInsightType
import com.soy.algorithms.coach.SuuntoCoachInsightTypeTheme
import com.soy.algorithms.impact.WorkoutImpact
import com.soy.algorithms.utils.mapOfNotNull
import com.stt.android.chart.api.model.ChartGranularity
import com.stt.android.chart.impl.model.ChartBarDisplayMode
import com.stt.android.chart.impl.model.ChartData
import com.stt.android.chart.impl.model.ChartType
import com.stt.android.chart.impl.usecases.FormatChartHighlightDateTimeUseCase
import com.stt.android.core.domain.workouts.CoreActivityGroup
import com.stt.android.core.domain.workouts.CoreActivityGroupByType
import com.stt.android.core.domain.workouts.CoreActivityGrouping
import com.stt.android.core.domain.workouts.CoreActivityType
import com.stt.android.core.domain.workouts.MINIMUM_COUNT_TO_SHOW_GROUP
import com.stt.android.diary.insights.TrainingHubFormatter
import com.stt.android.diary.insights.coach.TrainingCoachUiState
import com.stt.android.diary.insights.common.TrainingHubDateRange
import com.stt.android.diary.insights.impact.ImpactUiState
import com.stt.android.diary.insights.impact.WorkoutImpactCount
import com.stt.android.diary.insights.intensity.IntensityUiState
import com.stt.android.diary.insights.intensity.IntensityZoneInfoUiState
import com.stt.android.diary.insights.volume.TrainingHighlighted
import com.stt.android.diary.insights.volume.TrainingVolumeUiState
import com.stt.android.diary.insights.volume.TrainingVolumeWorkoutSummaryTotal
import com.stt.android.diary.insights.volume.VolumeWorkoutSummaryType
import com.stt.android.domain.diary.models.GraphTimeRange
import com.stt.android.domain.diary.models.isWeekSegmented
import com.stt.android.domain.diary.training.TrainingPeriodAnalysis
import com.stt.android.domain.workouts.extensions.intensity.IntensityDistribution
import com.stt.android.home.diary.R
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.iterator
import com.stt.android.utils.takeIfNotNaN
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableMap
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.collections.immutable.toPersistentList
import java.time.LocalDate
import javax.inject.Inject
import kotlin.math.roundToInt
import com.stt.android.chart.impl.R as ChartR
import com.stt.android.core.R as CR

class TrainingUiStateMapper @Inject constructor(
    @ApplicationContext private val context: Context,
    private val trainingHubFormatter: TrainingHubFormatter,
    private val infoModelFormatter: InfoModelFormatter,
    private val chartGranularityHelper: TrainingChartGranularityHelper,
    private val formatChartHighlightDateTimeUseCase: FormatChartHighlightDateTimeUseCase,
    private val trainingDatePeriodFormatter: TrainingDatePeriodFormatter,
    private val graphMaxYCalculator: TrainingGraphMaxYCalculator,
) {
    fun toTrainingUiState(
        firstPeriod: ClosedRange<LocalDate>,
        firstAnalysis: TrainingPeriodAnalysis?,
        secondPeriod: ClosedRange<LocalDate>,
        secondAnalysis: TrainingPeriodAnalysis?,
        graphTimeRange: GraphTimeRange,
        dateRange: TrainingDateRange,
    ): TrainingUiState = TrainingUiState(
        firstPeriod = firstPeriod,
        firstAnalysis = firstAnalysis,
        secondPeriod = secondPeriod,
        secondAnalysis = secondAnalysis,
        graphTimeRange = graphTimeRange,
        dateRange = dateRange
    )

    fun toIntensityUiStateOrNull(
        intensityUiState: IntensityUiState,
        trainingUiState: TrainingUiState
    ): IntensityUiState? {
        val firstIntensityDistributionResult =
            trainingUiState.firstAnalysis?.intensityDistribution ?: return null
        val secondIntensityDistributionResult =
            trainingUiState.secondAnalysis?.intensityDistribution

        return IntensityUiState(
            heartRateZones = (5 downTo 1).map { i ->
                makeIntensityZone(
                    index = i,
                    currentZones = firstIntensityDistributionResult.hrZones,
                    comparisonZones = secondIntensityDistributionResult?.hrZones
                )
            }.toPersistentList(),
            paceZones = (5 downTo 1).map { i ->
                makeIntensityZone(
                    index = i,
                    currentZones = firstIntensityDistributionResult.speedZones,
                    comparisonZones = secondIntensityDistributionResult?.speedZones
                )
            }.toPersistentList(),
            runningPowerZones = (5 downTo 1).map { i ->
                makeIntensityZone(
                    index = i,
                    currentZones = firstIntensityDistributionResult.runningPowerZones,
                    comparisonZones = secondIntensityDistributionResult?.runningPowerZones
                )
            }.toPersistentList(),
            cyclingPowerZones = (5 downTo 1).map { i ->
                makeIntensityZone(
                    index = i,
                    currentZones = firstIntensityDistributionResult.cyclingPowerZones,
                    comparisonZones = secondIntensityDistributionResult?.cyclingPowerZones
                )
            }.toPersistentList(),
            selectedType = intensityUiState.selectedType,
            supportComparison = trainingUiState.graphTimeRange.isWeekSegmented(),
        )
    }

    fun toTrainingChartHighlightedOrNull(
        entryX: Long?,
        summaryType: VolumeWorkoutSummaryType,
        chartData: ChartData,
    ): TrainingHighlighted? {
        if (entryX == null) return null
        val entryY = chartData.series.sumOf {
            it.entries.filter { it.x == entryX }.sumOf { it.y.toDouble() }
        }.takeIf { it > 0 } ?: return null
        val formattedResult = when (summaryType) {
            VolumeWorkoutSummaryType.COUNT -> entryY.roundToInt().toString() to null
            VolumeWorkoutSummaryType.DURATION ->
                trainingHubFormatter.formatDuration(entryY.toSeconds())

            VolumeWorkoutSummaryType.DISTANCE ->
                trainingHubFormatter.formatAccumulatedDistance(
                    infoModelFormatter.unit.fromDistanceUnit(entryY)
                )

            VolumeWorkoutSummaryType.ASCENT ->
                trainingHubFormatter.formatAscent(
                    infoModelFormatter.unit.fromAltitudeUnit(entryY)
                )

            VolumeWorkoutSummaryType.TSS -> trainingHubFormatter.formatTSS(entryY)
        }
        return TrainingHighlighted(
            valueLabelResId = summaryType.labelResId,
            formattedDate = formatChartHighlightDateTimeUseCase.formatDateTime(
                chartData.chartGranularity,
                entryX
            ),
            formattedValue = formattedResult.first,
            valueUnitResId = formattedResult.second,
            valueTypeResId = ChartR.string.chart_value_total,
        )
    }

    fun toTrainingVolumeUiStateOrNull(
        trainingVolumeUiState: TrainingVolumeUiState,
        firstAnalysis: TrainingPeriodAnalysis?,
        secondAnalysis: TrainingPeriodAnalysis?,
        chartGranularity: ChartGranularity,
    ): TrainingVolumeUiState? {
        if (firstAnalysis == null) return null

        val workoutCountSummary = TrainingVolumeWorkoutSummaryTotal(
            label = VolumeWorkoutSummaryType.COUNT.labelResId,
            formatedValue = "${firstAnalysis.workoutsCount.roundToInt()}",
            unit = null,
            comparisonTitleRes = R.string.training_hub_avg,
            comparisonFormatedValue = secondAnalysis?.let {
                trainingHubFormatter.formatAverageCount(it.workoutsCount)
            },
        )
        val workoutDurationSummary = TrainingVolumeWorkoutSummaryTotal(
            label = VolumeWorkoutSummaryType.DURATION.labelResId,
            formatedValue = trainingHubFormatter.formatDuration(firstAnalysis.totalDuration).first,
            unit = CR.string.TXT_H,
            comparisonTitleRes = R.string.training_hub_avg,
            comparisonFormatedValue = secondAnalysis?.let {
                trainingHubFormatter.formatDuration(it.totalDuration).first
            },
        )
        val (formattedDistance, distanceUnit) =
            trainingHubFormatter.formatAccumulatedDistance(firstAnalysis.totalDistance)
        val workoutDistanceSummary = TrainingVolumeWorkoutSummaryTotal(
            label = VolumeWorkoutSummaryType.DISTANCE.labelResId,
            formatedValue = formattedDistance,
            unit = distanceUnit,
            comparisonTitleRes = R.string.training_hub_avg,
            comparisonFormatedValue = secondAnalysis?.let {
                trainingHubFormatter.formatAccumulatedDistance(it.totalDistance).first
            },
        ).takeIf { firstAnalysis.totalDistance > 0 }

        val (formattedAscent, ascentUnit) =
            trainingHubFormatter.formatAscent(firstAnalysis.totalAscent)
        val workoutAscentSummary = TrainingVolumeWorkoutSummaryTotal(
            label = VolumeWorkoutSummaryType.ASCENT.labelResId,
            formatedValue = formattedAscent,
            unit = ascentUnit,
            comparisonTitleRes = R.string.training_hub_avg,
            comparisonFormatedValue = secondAnalysis?.let {
                trainingHubFormatter.formatAscent(it.totalAscent).first
            },
        ).takeIf { firstAnalysis.totalAscent > 0 }

        val (formattedTss, tssUnit) = trainingHubFormatter.formatTSS(firstAnalysis.totalTSS)
        val workoutTssSummary = TrainingVolumeWorkoutSummaryTotal(
            label = VolumeWorkoutSummaryType.TSS.labelResId,
            formatedValue = formattedTss,
            unit = tssUnit,
            comparisonTitleRes = R.string.training_hub_avg,
            comparisonFormatedValue = secondAnalysis?.let {
                trainingHubFormatter.formatTSS(it.totalTSS).first
            },
        )

        val workoutSummaries = mapOfNotNull(
            VolumeWorkoutSummaryType.COUNT to workoutCountSummary,
            VolumeWorkoutSummaryType.DURATION to workoutDurationSummary,
            VolumeWorkoutSummaryType.DISTANCE to workoutDistanceSummary,
            VolumeWorkoutSummaryType.ASCENT to workoutAscentSummary,
            VolumeWorkoutSummaryType.TSS to workoutTssSummary
        )

        val selectedSummaryType = trainingVolumeUiState.selectedSummaryType.takeIf {
            workoutSummaries.containsKey(it)
        } ?: VolumeWorkoutSummaryType.COUNT

        val chartSeries = when (selectedSummaryType) {
            VolumeWorkoutSummaryType.COUNT ->
                makeActivityCountChartSeries(firstAnalysis, chartGranularity)

            VolumeWorkoutSummaryType.DURATION ->
                makeDurationChartSeries(firstAnalysis, chartGranularity)

            VolumeWorkoutSummaryType.DISTANCE ->
                makeDistanceChartSeries(firstAnalysis, chartGranularity)

            VolumeWorkoutSummaryType.ASCENT ->
                makeAscentChartSeries(firstAnalysis, chartGranularity)

            VolumeWorkoutSummaryType.TSS ->
                makeTSSChartSeries(firstAnalysis, chartGranularity)
        }

        val activityTypes = firstAnalysis.trainingTotals.map { it.activityType }
        val group = CoreActivityGroup.entries.filter {
            activityTypes.intersect(it.activityTypes).size >= MINIMUM_COUNT_TO_SHOW_GROUP
        }
        val activityGroupings = group + (activityTypes - group.flatMap { it.activityTypes })

        return TrainingVolumeUiState(
            workoutSummaries = workoutSummaries,
            selectedSummaryType = selectedSummaryType,
            chartData = ChartData(
                chartGranularity = chartGranularity,
                series = chartSeries,
                highlightEnabled = true,
                goal = null,
                highlightDecorationLines = persistentMapOf(),
                currentValues = persistentListOf(),
                chartBarDisplayMode = ChartBarDisplayMode.STACKED,
                chartContent = null,
                colorIndicator = null,
            ),
            unit = infoModelFormatter.unit,
            activityGroupings = activityGroupings.toImmutableList(),
        )
    }

    private fun makeTSSChartSeries(
        firstAnalysis: TrainingPeriodAnalysis,
        chartGranularity: ChartGranularity,
    ): ImmutableList<ChartData.Series> {
        return createTrainingChartSeries(
            startDate = firstAnalysis.startDate,
            endDateInclusive = firstAnalysis.endDateInclusive,
            chartGranularity = chartGranularity,
            buildMaxY = { tss -> graphMaxYCalculator.nearest(tss, 30.0) },
            source = { firstAnalysis.tssPerActivityTypeByDay }
        )
    }

    private fun Double.toHours() = this / 3600

    private fun Double.toSeconds() = this * 3600

    private fun makeDurationChartSeries(
        firstAnalysis: TrainingPeriodAnalysis,
        chartGranularity: ChartGranularity,
    ): ImmutableList<ChartData.Series> {
        return createTrainingChartSeries(
            startDate = firstAnalysis.startDate,
            endDateInclusive = firstAnalysis.endDateInclusive,
            chartGranularity = chartGranularity,
            buildMaxY = { duration -> graphMaxYCalculator.nearest(duration?.toHours(), 1.5) },
            source = { firstAnalysis.durationPreActivityTypeByDay },
            transfer = { it.toHours() }
        )
    }

    private fun makeDistanceChartSeries(
        firstAnalysis: TrainingPeriodAnalysis,
        chartGranularity: ChartGranularity,
    ): ImmutableList<ChartData.Series> {
        return createTrainingChartSeries(
            startDate = firstAnalysis.startDate,
            endDateInclusive = firstAnalysis.endDateInclusive,
            chartGranularity = chartGranularity,
            buildMaxY = { distance ->
                graphMaxYCalculator.nearest(
                    distance?.let { infoModelFormatter.unit.toDistanceUnit(it) },
                    3.0
                )
            },
            source = { firstAnalysis.distancePreActivityTypeByDay },
            transfer = { infoModelFormatter.unit.toDistanceUnit(it) }
        )
    }

    private fun makeAscentChartSeries(
        firstAnalysis: TrainingPeriodAnalysis,
        chartGranularity: ChartGranularity,
    ): ImmutableList<ChartData.Series> {
        return createTrainingChartSeries(
            startDate = firstAnalysis.startDate,
            endDateInclusive = firstAnalysis.endDateInclusive,
            chartGranularity = chartGranularity,
            buildMaxY = { ascent ->
                graphMaxYCalculator.nearest(
                    ascent?.let { infoModelFormatter.unit.toAltitudeUnit(it) },
                    30.0
                )
            },
            source = { firstAnalysis.ascentPreActivityTypeByDay },
            transfer = { infoModelFormatter.unit.toAltitudeUnit(it) }
        )
    }

    private fun makeActivityCountChartSeries(
        firstAnalysis: TrainingPeriodAnalysis,
        chartGranularity: ChartGranularity,
    ): ImmutableList<ChartData.Series> {
        return createTrainingChartSeries(
            startDate = firstAnalysis.startDate,
            endDateInclusive = firstAnalysis.endDateInclusive,
            chartGranularity = chartGranularity,
            buildMaxY = { workoutCount -> graphMaxYCalculator.nearest(workoutCount, 3.0) },
            source = { firstAnalysis.workoutsCountPreActivityTypeByDay }
        )
    }

    private fun createTrainingChartSeries(
        startDate: LocalDate,
        endDateInclusive: LocalDate,
        chartGranularity: ChartGranularity,
        buildMaxY: (Double?) -> Double,
        source: () -> Map<LocalDate, List<Pair<CoreActivityType, Double>>>,
        transfer: ((Double) -> Double)? = null,
    ): ImmutableList<ChartData.Series> {
        val aggregatedData = mutableMapOf<LocalDate, List<Pair<CoreActivityType, Double>>>()
        source().forEach { source ->
            aggregatedData.compute(
                chartGranularityHelper.flattenToGranularity(source.key, chartGranularity)
            ) { _, values ->
                (values ?: listOf()) + source.value.filter { it.second > 0 }
            }
        }

        val axisRange = ChartData.AxisRange(
            minX = chartGranularityHelper.epochAxisX(startDate, chartGranularity).toDouble(),
            maxX = chartGranularityHelper.epochAxisX(endDateInclusive, chartGranularity).toDouble(),
            minY = 0.0,
            maxY = buildMaxY(aggregatedData.maxOfOrNull { it.value.sumOf { it.second } })
        )
        val chartSeries = mutableListOf<ChartData.Series>()
        (startDate..endDateInclusive).iterator().asSequence()
            .map { chartGranularityHelper.flattenToGranularity(it, chartGranularity) }
            .distinct()
            .forEach { date ->
                val groupingWithValue = mutableMapOf<CoreActivityGrouping, Double>()
                aggregatedData[date]
                    ?.filter { it.second > 0 }
                    ?.forEach { (activityType, value) ->
                        val grouping = CoreActivityGroupByType[activityType] ?: activityType
                        groupingWithValue.compute(grouping) { _, previous ->
                            (previous ?: 0.0) + value
                        }
                    }
                groupingWithValue.toList()
                    .sortedByDescending { it.second }
                    .mapIndexed { index, (grouping, value) ->
                        val entryY = transfer?.invoke(value) ?: value
                        createChartSeries(
                            axisRange = axisRange,
                            color = context.getColor(grouping.color),
                            entry = ChartData.Entry(
                                x = chartGranularityHelper.epochAxisX(date, chartGranularity),
                                y = entryY
                            ),
                        )
                    }.let(chartSeries::addAll)
            }
        return chartSeries.getOrDefault {
            createChartSeries(
                axisRange = axisRange,
                color = Color.WHITE,
                entry = null,
            )
        }
    }

    private fun List<ChartData.Series>.getOrDefault(default: () -> ChartData.Series) =
        takeIf { it.isNotEmpty() }?.toImmutableList() ?: persistentListOf(default())

    private fun createChartSeries(
        axisRange: ChartData.AxisRange,
        @ColorInt color: Int,
        entry: ChartData.Entry?,
    ) = ChartData.Series(
        chartType = ChartType.BAR,
        color = color,
        axisRange = axisRange,
        entries = if (entry != null) persistentListOf(entry) else persistentListOf(),
        value = AnnotatedString(""),
        candlestickEntries = null,
        lineConfig = null,
        backgroundRegion = null,
        groupStackBarStyle = null,
    )

    fun toTrainingCoachUiState(
        coachFeedback: ImmutableMap<SuuntoCoachInsightType, CoachPhrase>?
    ): TrainingCoachUiState {
        val items = coachFeedback?.filterKeys {
            it.theme == SuuntoCoachInsightTypeTheme.TRAINING
        }
        val trainingPhrasesIds = items?.map { it.value.id }.orEmpty().toImmutableList()
        val intensityPhrasesIds = items?.filterKeys {
            SuuntoCoachInsightType.WORKOUTINTENSITY == it
        }?.map { it.value.id }.orEmpty().toImmutableList()
        val impactPhrasesIds = items?.filterKeys {
            SuuntoCoachInsightType.DISTRIBUTION == it
        }?.map { it.value.id }.orEmpty().toImmutableList()

        return TrainingCoachUiState(
            trainingPhrasesIds = trainingPhrasesIds,
            intensityPhrasesIds = intensityPhrasesIds,
            impactPhrasesIds = impactPhrasesIds,
        )
    }

    fun toImpactUiStateOrNull(
        impactUiState: ImpactUiState,
        trainingUiState: TrainingUiState,
    ): ImpactUiState? {
        val currentTrainingIntensityModel =
            trainingUiState.firstAnalysis?.trainingIntensityModel ?: return null
        val comparisonTrainingIntensityModel =
            trainingUiState.secondAnalysis?.trainingIntensityModel

        val currentWorkoutImpactsDistribution =
            trainingUiState.firstAnalysis.workoutImpactsDistribution
        val comparisonWorkoutImpactsDistribution =
            trainingUiState.secondAnalysis?.workoutImpactsDistribution

        val dateRange = if (trainingUiState.graphTimeRange == GraphTimeRange.CURRENT_WEEK) {
            when (trainingUiState.dateRange) {
                is TrainingDateRange.CurrentWeek -> TrainingHubDateRange.CurrentWeek
                is TrainingDateRange.CustomRange -> TrainingHubDateRange.CustomRange(
                    trainingUiState.dateRange.value
                )

                else -> null
            }
        } else null

        return ImpactUiState(
            isExpanded = impactUiState.isExpanded,
            selectedImpactType = impactUiState.selectedImpactType,
            _currentTrainingIntensityModel = currentTrainingIntensityModel,
            _comparisonTrainingIntensityModel = comparisonTrainingIntensityModel,
            _workoutsImpactCount = WorkoutImpact.entries
                .map {
                    val currentCount = currentWorkoutImpactsDistribution.getOrDefault(it, 0f)
                    val comparisonCount = comparisonWorkoutImpactsDistribution?.getOrDefault(it, 0f)
                    WorkoutImpactCount(
                        workoutImpact = it,
                        currentCount = currentCount,
                        comparisonCount = comparisonCount,
                        formattedCurrentCount = trainingHubFormatter.formatAverageCount(currentCount),
                        formattedComparisonCount = comparisonCount?.let {
                            trainingHubFormatter.formatAverageCount(it)
                        },
                    )
                }.toImmutableList(),
            trainingHubDateRange = dateRange,
            coachTextId = null
        )
    }

    private fun makeIntensityZone(
        @IntRange(from = 1, to = 5) index: Int,
        currentZones: IntensityDistribution.ZoneDuration,
        comparisonZones: IntensityDistribution.ZoneDuration?,
    ): IntensityZoneInfoUiState {
        val maxValue = maxOf(currentZones.maxZoneValue(), comparisonZones?.maxZoneValue() ?: 0f)
        val currentValue = currentZones[index]
        val comparisonValue = comparisonZones?.get(index)
        return IntensityZoneInfoUiState(
            number = index,
            currentValue = currentValue,
            comparisonValue = comparisonValue,
            currentValueFormatted = trainingHubFormatter.formatDurationExactToSeconds(
                currentValue.toDouble()
            ).first,
            comparisonValueFormatted = comparisonValue?.let {
                trainingHubFormatter.formatDurationExactToSeconds(it.toDouble()).first
            },
            currentFraction = (currentValue / maxValue).takeIfNotNaN() ?: 0f,
            comparisonFraction = comparisonValue?.let {
                (it / maxValue).takeIfNotNaN() ?: 0f
            },
        )
    }

    fun toTrainingDateRange(
        period: ClosedRange<LocalDate>,
        hasNextWeek: Boolean,
        timeRange: GraphTimeRange,
    ): TrainingDateRange {
        if (!hasNextWeek) {
            return when (timeRange) {
                GraphTimeRange.CURRENT_WEEK -> TrainingDateRange.CurrentWeek
                GraphTimeRange.CURRENT_MONTH -> TrainingDateRange.CurrentMonth
                GraphTimeRange.CURRENT_YEAR -> TrainingDateRange.CurrentYear
                else -> TrainingDateRange.CustomRange(
                    trainingDatePeriodFormatter.format(period, timeRange)
                )
            }
        }

        return TrainingDateRange.CustomRange(
            trainingDatePeriodFormatter.format(period, timeRange)
        )
    }
}
