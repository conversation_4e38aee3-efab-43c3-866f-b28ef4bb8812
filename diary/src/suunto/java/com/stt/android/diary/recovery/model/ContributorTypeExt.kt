package com.stt.android.diary.recovery.model

import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stt.android.diary.recovery.v2.ContributorType
import com.stt.android.home.diary.R
import com.stt.android.R as BaseR

@DrawableRes
fun ContributorType.getIconRes(): Int? {
    return when (this) {
        ContributorType.SLEEP -> BaseR.drawable.sleep_fill_32dp
        ContributorType.HRV -> BaseR.drawable.hrv_fill
        ContributorType.REST_HR -> BaseR.drawable.ic_dashboard_widget_heart_rate
        ContributorType.TRAINING_FATIGUE -> null
        ContributorType.RESOURCES -> BaseR.drawable.ic_dashboard_widget_resources
    }
}

@StringRes
fun ContributorType.getTitleRes(): Int? {
    return when (this) {
        ContributorType.SLEEP -> R.string.contributors_content_sleep
        ContributorType.HRV -> R.string.contributors_content_hrv
        ContributorType.REST_HR -> R.string.contributors_content_resting_heart_rate
        ContributorType.TRAINING_FATIGUE -> null
        ContributorType.RESOURCES -> R.string.contributors_content_resources
    }
}


@ColorRes
fun ContributorType.getColorRes(): Int? {
    return when (this) {
        ContributorType.SLEEP -> BaseR.color.dashboard_widget_sleep
        ContributorType.HRV -> BaseR.color.dashboard_widget_hrv
        ContributorType.REST_HR -> BaseR.color.dashboard_widget_minimum_heart_rate
        ContributorType.TRAINING_FATIGUE -> null
        ContributorType.RESOURCES -> BaseR.color.dashboard_widget_resources
    }
}


@StringRes
fun ContributorType.getHeaderRes(): Int {
    return when (this) {
        ContributorType.SLEEP -> BaseR.string.recovery_state_sleep_duration
        ContributorType.HRV -> BaseR.string.recovery_state_hrv
        ContributorType.REST_HR -> BaseR.string.recovery_state_rest_hr
        ContributorType.TRAINING_FATIGUE -> BaseR.string.recovery_state_training_fatigue
        ContributorType.RESOURCES -> BaseR.string.recovery_state_daily_resources
    }
}


@StringRes
fun ContributorType.getEmptyContentRes(): Int? {
    return when (this) {
        ContributorType.SLEEP -> R.string.contributor_sleep_empty
        ContributorType.HRV -> R.string.contributor_hrv_empty
        ContributorType.REST_HR -> R.string.contributor_resting_heart_rate_empty
        ContributorType.TRAINING_FATIGUE -> R.string.contributor_training_fatigue_empty
        ContributorType.RESOURCES -> null
    }
}

@StringRes
fun ContributorType.valueType():Int{
    return when (this) {
        ContributorType.SLEEP -> R.string.recovery_daily_average
        ContributorType.HRV -> R.string.recovery_daily_average
        ContributorType.REST_HR -> R.string.recovery_daily_average
        ContributorType.TRAINING_FATIGUE -> R.string.recovery_daily_average
        ContributorType.RESOURCES -> R.string.recovery_average
    }
}
