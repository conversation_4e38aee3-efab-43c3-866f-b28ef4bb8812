package com.stt.android.diary.insights.impact

import androidx.annotation.StringRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.soy.algorithms.impact.WorkoutImpactType
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodySmall
import com.stt.android.compose.theme.bodySmallBold
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.darkestGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.Chip
import com.stt.android.diary.common.trainingHubDimens
import com.stt.android.diary.insights.common.AverageComparisonChip
import com.stt.android.diary.insights.common.CoachText
import com.stt.android.diary.insights.common.DummyTrainingHubUiStates
import com.stt.android.diary.insights.common.ExpandableSection
import com.stt.android.diary.insights.common.NumberOfWeeksAvg
import com.stt.android.diary.insights.common.ProgressIndicator
import com.stt.android.diary.insights.common.ProgressValue
import com.stt.android.diary.insights.common.SubSection
import com.stt.android.diary.insights.common.TrainingHubDateRange
import com.stt.android.diary.insights.common.comparisonStringResource
import com.stt.android.diary.insights.intensity.ComparisonIndicator
import com.stt.android.domain.workouts.extensions.intensity.TrainingIntensityModel
import com.stt.android.domain.workouts.extensions.intensity.titleRes
import com.stt.android.home.diary.R
import kotlinx.collections.immutable.ImmutableList
import kotlin.math.max
import com.stt.android.core.R as BaseR

@Composable
fun TrainingHubImpact(
    impactUiState: ImpactUiState,
    onWorkoutImpactTypeClick: (WorkoutImpactType) -> Unit,
    onViewMoreOrLessClick: () -> Unit,
    onShowInfoClicked: (workoutImpactType: WorkoutImpactType) -> Unit,
    onTrainingModelInfoClicked: () -> Unit,
    modifier: Modifier = Modifier,
    showViewMoreOrLess: Boolean = true,
) {
    ExpandableSection(
        titleRes = R.string.training_hub_impact,
        modifier = modifier.testTag("TrainingHubImpact"),
        isExpanded = impactUiState.isExpanded,
        onViewMoreOrLessClick = onViewMoreOrLessClick,
        onActionClick = {
            onShowInfoClicked(impactUiState.selectedImpactType)
        },
        showViewMoreOrLess = showViewMoreOrLess,
    ) {
        ImpactTypeSelector(
            selectedType = impactUiState.selectedImpactType,
            onWorkoutImpactTypeClick = onWorkoutImpactTypeClick,
        )
        AnimatedVisibility(visible = impactUiState.supportTrainingIntensityModel) {
            SubSection(
                titleRes = R.string.training_hub_sub_section_training_model,
                modifier = Modifier.padding(top = MaterialTheme.spacing.large),
                onActionClick = onTrainingModelInfoClicked
            ) {
                impactUiState.coachTextId?.let {
                    CoachText(
                        textId = it,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(
                                bottom = MaterialTheme.spacing.large
                            )
                    )
                }
                impactUiState.trainingHubDateRange?.let {
                    ImpactTrainingModel(
                        currentTrainingIntensityModel = impactUiState.trainingIntensityModel.first,
                        comparisonTrainingIntensityModel = impactUiState.trainingIntensityModel.second,
                        trainingHubDateRange = it
                    )
                }
            }
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.large))
        Impacts(
            impacts = impactUiState.impacts,
            unclassifiedWorkoutsCount = impactUiState.unclassifiedWorkoutsCount,
            isExpanded = impactUiState.isExpanded,
            supportComparison = impactUiState.supportComparison,
        )
    }
}

@Composable
internal fun ImpactTypeSelector(
    selectedType: WorkoutImpactType,
    onWorkoutImpactTypeClick: (WorkoutImpactType) -> Unit,
    modifier: Modifier = Modifier,
    supportVerticalPadding: Boolean = true,
    nonSelectedBackgroundColor: Color = MaterialTheme.colors.lightGrey,
) {
    LazyRow(
        modifier = modifier
            .fillMaxWidth()
            .then(
                if (supportVerticalPadding) {
                    Modifier.padding(
                        top = MaterialTheme.spacing.xsmall,
                        bottom = MaterialTheme.spacing.small
                    )
                } else {
                    Modifier
                }
            ),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
    ) {
        items(
            items = listOf(WorkoutImpactType.CARDIO, WorkoutImpactType.MUSCULAR),
            key = { it.name }
        ) { workoutImpactType ->
            val isSelected = workoutImpactType == selectedType
            val contentColor = if (isSelected) {
                Color.White
            } else {
                MaterialTheme.colors.onSurface
            }
            workoutImpactType.titleRes?.let {
                Chip(
                    isSelected = isSelected,
                    onChecked = {
                        onWorkoutImpactTypeClick(workoutImpactType)
                    },
                    modifier = Modifier.height(MaterialTheme.trainingHubDimens.chipHeight),
                    nonSelectedBackgroundColor = nonSelectedBackgroundColor,
                ) {
                    Text(
                        text = stringResource(it),
                        style = MaterialTheme.typography.body,
                        color = contentColor,
                        modifier = Modifier.padding(horizontal = MaterialTheme.spacing.xsmall)
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
internal fun ImpactTrainingModel(
    currentTrainingIntensityModel: TrainingIntensityModel?,
    comparisonTrainingIntensityModel: TrainingIntensityModel?,
    trainingHubDateRange: TrainingHubDateRange,
    modifier: Modifier = Modifier,
) {
    Row(modifier = modifier) {
        ImpactTrainingModelItem(
            trainingIntensityModel = currentTrainingIntensityModel,
            timeRange = when (trainingHubDateRange) {
                is TrainingHubDateRange.CurrentWeek -> stringResource(trainingHubDateRange.value)
                is TrainingHubDateRange.CustomRange -> trainingHubDateRange.value
            },
            modifier = Modifier.weight(1f)
        )
        ImpactTrainingModelItem(
            trainingIntensityModel = comparisonTrainingIntensityModel,
            timeRange = comparisonStringResource(),
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun ImpactTrainingModelItem(
    trainingIntensityModel: TrainingIntensityModel?,
    timeRange: String,
    modifier: Modifier = Modifier
) {
    val iconRes = trainingIntensityModel?.iconRes
    val titleRes = trainingIntensityModel?.titleRes
    Column(modifier = modifier) {
        Text(
            text = timeRange,
            style = MaterialTheme.typography.body,
            color = MaterialTheme.colors.nearBlack,
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.xsmall)
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.defaultMinSize(minHeight = MaterialTheme.iconSizes.medium) // In case there is no icon, we make sure the text doesn't get less height than the other model item
        ) {
            if (iconRes != null) {
                Icon(
                    painter = painterResource(iconRes),
                    contentDescription = null,
                    modifier = Modifier.size(MaterialTheme.iconSizes.medium)
                )
            }
            Text(
                text = if (titleRes != null) {
                    stringResource(titleRes)
                } else {
                    stringResource(R.string.training_hub_no_data)
                },
                style = MaterialTheme.typography.bodyBold,
                color = MaterialTheme.colors.nearBlack,
                modifier = Modifier.padding(horizontal = MaterialTheme.spacing.xsmall)
            )
        }
    }
}

@Composable
internal fun Impacts(
    impacts: ImmutableList<WorkoutImpactCount>,
    unclassifiedWorkoutsCount: Int,
    isExpanded: Boolean,
    modifier: Modifier = Modifier,
    supportComparison: Boolean = true,
) {
    Column(modifier = modifier) {
        ImpactsListHeader()
        Divider()
        if (impacts.isEmpty()) {
            // There is a chance that this Impacts composable will be called before having the Impact data (empty list)
            // To avoid performing calculations on an empty array, we are falling back to this empty state
            NoImpactsData()
        } else {
            val hasData: Boolean by remember(impacts) {
                derivedStateOf {
                    impacts.any { it.currentCount > 0 || (it.comparisonCount != null && it.comparisonCount > 0) }
                }
            }

            val maxLoad: Float by remember(impacts) {
                derivedStateOf {
                    max(
                        impacts.maxOf { it.currentCount },
                        impacts.maxOf { it.comparisonCount ?: 0f}
                    )
                }
            }

            MeasureImpactItemValueWidth(
                impacts = impacts,
                maxLoad = maxLoad,
                supportComparison = supportComparison,
            ) { impactItemValueMaxWidth ->
                Column {
                    if (!hasData && !isExpanded) {
                        NoImpactsData()
                    }
                    for ((index, item) in impacts.withIndex()) {
                        val titleRes = item.title() ?: continue
                        key(titleRes) {
                            ImpactItem(
                                titleRes = titleRes,
                                color = item.color(),
                                currentAvgFraction = if (maxLoad == 0f) {
                                    0f
                                } else {
                                    item.currentCount / maxLoad
                                },
                                previousAvgFraction = if (maxLoad == 0f) {
                                    0f
                                } else {
                                    item.comparisonCount?.let { it / maxLoad }
                                },
                                formattedCurrentCount = item.formattedCurrentCount,
                                formattedComparisonCount = item.formattedComparisonCount,
                                impactItemValueMaxWidth = impactItemValueMaxWidth,
                                supportComparison = supportComparison,
                            )
                        }
                        if (index != impacts.lastIndex) {
                            Divider()
                        }
                    }
                    if (hasData || isExpanded) {
                        if (supportComparison) {
                            NumberOfWeeksAvg(
                                modifier = Modifier.padding(vertical = MaterialTheme.spacing.xsmaller)
                            )
                        }
                        UnclassifiedWorkoutsCount(count = unclassifiedWorkoutsCount)
                    }
                }
            }
        }
    }
}

/**
 * The ImpactItem composable is a Row with a middle item that should take up all available space,
 * and a text item that may vary in length. While this layout works well on normal and larger devices, the text can be cut off on smaller devices.
 * To solve this issue, this composable measures all the texts on the right and returns the longest width. This width is then set for all the other texts, ensuring that none of the data is cut off.
 * In summary, the ImpactItem composable helps to ensure that all data is visible, regardless of the size of the device.
 */
@Composable
private fun MeasureImpactItemValueWidth(
    impacts: ImmutableList<WorkoutImpactCount>,
    maxLoad: Float,
    supportComparison: Boolean,
    content: @Composable (impactItemValueMaxWidth: Dp) -> Unit
) {
    SubcomposeLayout { constraints ->
        val maxWidth = impacts
            .maxOf {
                subcompose(
                    slotId = it.hashCode(),
                    content = {
                        ImpactItemValue(
                            formattedCurrentCount = it.formattedCurrentCount,
                            formattedComparisonCount = it.formattedComparisonCount,
                            currentAvgFraction = if (maxLoad == 0f) {
                                0f
                            } else {
                                it.currentCount / maxLoad
                            },
                            previousAvgFraction = if (maxLoad == 0f) {
                                0f
                            } else {
                                it.comparisonCount?.let { it / maxLoad }
                            },
                            supportComparison = supportComparison
                        )
                    }
                ).first()
                    .measure(Constraints())
                    .width
                    .toDp()
            }

        val contentPlaceable = subcompose(
            slotId = "content",
            content = { content(maxWidth) }
        ).first()
            .measure(constraints)

        layout(contentPlaceable.width, contentPlaceable.height) {
            contentPlaceable.placeRelative(0, 0)
        }
    }
}

@Composable
private fun ImpactsListHeader(modifier: Modifier = Modifier) {
    Row(modifier = modifier) {
        HeaderText(
            textResId = R.string.training_hub_sub_section_impacts,
            textAlign = TextAlign.Start,
            modifier = Modifier.weight(1f)
        )
        HeaderText(
            textResId = R.string.training_hub_total_activities,
            textAlign = TextAlign.End,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun HeaderText(
    @StringRes textResId: Int,
    textAlign: TextAlign,
    modifier: Modifier = Modifier
) {
    Text(
        text = stringResource(textResId),
        style = MaterialTheme.typography.bodySmallBold,
        color = MaterialTheme.colors.darkestGrey,
        modifier = modifier
            .padding(vertical = MaterialTheme.spacing.small)
            .fillMaxWidth(),
        textAlign = textAlign
    )
}

@Composable
private fun ImpactItem(
    @StringRes titleRes: Int,
    color: Color?,
    currentAvgFraction: Float,
    previousAvgFraction: Float?,
    formattedCurrentCount: String,
    formattedComparisonCount: String?,
    impactItemValueMaxWidth: Dp,
    supportComparison: Boolean,
    modifier: Modifier = Modifier,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier.height(MaterialTheme.trainingHubDimens.impactItemHeight),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
    ) {
        Text(
            text = stringResource(titleRes),
            style = MaterialTheme.typography.body,
            color = MaterialTheme.colors.darkestGrey,
            modifier = Modifier.weight(1f)
        )

        Column(modifier = Modifier.weight(2f)) {
            ProgressIndicator(
                backgroundColor = color ?: Color.Unspecified,
                widthFraction = currentAvgFraction.coerceAtLeast(0.01f)
            )
            if (supportComparison) {
                previousAvgFraction?.let {
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.xsmall))
                    ProgressIndicator(
                        backgroundColor = MaterialTheme.colors.cloudyGrey,
                        widthFraction = it.coerceAtLeast(0.01f)
                    )
                }
            }
        }

        ImpactItemValue(
            formattedCurrentCount = formattedCurrentCount,
            formattedComparisonCount = formattedComparisonCount,
            currentAvgFraction = currentAvgFraction,
            previousAvgFraction = previousAvgFraction,
            supportComparison = supportComparison,
            modifier = Modifier.width(impactItemValueMaxWidth)
        )
    }
}

@Composable
private fun ImpactItemValue(
    currentAvgFraction: Float,
    previousAvgFraction: Float?,
    formattedCurrentCount: String,
    formattedComparisonCount: String?,
    supportComparison: Boolean,
    modifier: Modifier = Modifier
) {
    val comparisonIndicator = if (supportComparison) {
        previousAvgFraction?.let { ComparisonIndicator.from(currentAvgFraction, it) }
    } else null
    Column(
        horizontalAlignment = Alignment.End,
        modifier = modifier
    ) {
        ProgressValue(
            text = formattedCurrentCount,
            unit = null,
            comparisonIndicator = comparisonIndicator,
        )
        if (supportComparison) {
            formattedComparisonCount?.let {
                AverageComparisonChip(
                    text = it,
                    unit = null
                )
            }
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun UnclassifiedWorkoutsCount(
    count: Int,
    modifier: Modifier = Modifier
) {
    Text(
        text = pluralStringResource(
            R.plurals.training_hub_impact_unclassified_workouts_count,
            count,
            count
        ),
        style = MaterialTheme.typography.bodySmall,
        color = MaterialTheme.colors.onSurface,
        modifier = modifier
    )
}

@Composable
private fun NoImpactsData(
    modifier: Modifier = Modifier
) {
    Text(
        text = stringResource(R.string.training_hub_no_data_to_show),
        style = MaterialTheme.typography.bodySmall,
        color = MaterialTheme.colors.onSurface,
        modifier = modifier.padding(top = MaterialTheme.spacing.medium)
    )
}

@Preview(showBackground = true, widthDp = 320, heightDp = 720)
@Composable
private fun TrainingHubImpactSmallPreview() {
    AppTheme {
        TrainingHubImpact(
            impactUiState = DummyTrainingHubUiStates.impactUiState,
            onWorkoutImpactTypeClick = {},
            onViewMoreOrLessClick = {},
            onShowInfoClicked = {},
            onTrainingModelInfoClicked = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun TrainingHubImpactPreview() {
    AppTheme {
        TrainingHubImpact(
            impactUiState = DummyTrainingHubUiStates.impactUiState,
            onWorkoutImpactTypeClick = {},
            onViewMoreOrLessClick = {},
            onShowInfoClicked = {},
            onTrainingModelInfoClicked = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ImpactItemPreview() {
    AppTheme {
        ImpactItem(
            titleRes = BaseR.string.training_hub_impact_cardio_vo2_max,
            color = Color(0xFF5C2343),
            currentAvgFraction = 0.7f,
            previousAvgFraction = 0.6f,
            formattedCurrentCount = "7",
            formattedComparisonCount = "0.6",
            impactItemValueMaxWidth = 60.dp,
            supportComparison = false
        )
    }
}
