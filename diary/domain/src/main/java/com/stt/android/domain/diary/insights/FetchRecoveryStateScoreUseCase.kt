package com.stt.android.domain.diary.insights

import com.soy.algorithms.recovery.calculateDailyScore
import com.soy.algorithms.recovery.calculateRecoveryStateScore
import com.stt.android.coroutines.combine
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.diary.GetTrainingProgressDataUseCase
import com.stt.android.domain.diary.models.RecoveryStateContributors
import com.stt.android.domain.diary.models.RecoveryStateData
import com.stt.android.domain.diary.models.RecoveryStateGraphData
import com.stt.android.domain.diary.models.RecoveryZone
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.recovery.FetchRecoveryDataUseCase
import com.stt.android.domain.recovery.RecoveryData
import com.stt.android.domain.restheartrate.FetchRestHeartRateUseCase
import com.stt.android.domain.sleep.FetchSleepHrvUseCase
import com.stt.android.domain.sleep.Sleep
import com.stt.android.domain.sleep.SleepRepository
import com.stt.android.domain.trenddata.TrendDataRepository
import com.stt.android.domain.user.CurrentUserDataSource
import com.stt.android.domain.user.UserSettingsDataSource
import com.stt.android.domain.workouts.feeling.DailyFeeling
import com.stt.android.domain.workouts.feeling.FetchDailyFeelingUseCase
import com.stt.android.utils.averageOfDouble
import com.stt.android.utils.averageOrNull
import com.stt.android.utils.takeIfNotNaN
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import kotlin.math.roundToInt

class FetchRecoveryStateScoreUseCase @Inject constructor(
    private val currentUserDataSource: CurrentUserDataSource,
    private val userSettingsDataSource: UserSettingsDataSource,
    private val sleepRepository: SleepRepository,
    private val fetchRecoveryDataUseCase: FetchRecoveryDataUseCase,
    private val trendDataRepository: TrendDataRepository,
    private val fetchSleepHrvUseCase: FetchSleepHrvUseCase,
    private val getTrainingProgressDataUseCase: GetTrainingProgressDataUseCase,
    private val fetchRestHeartRateUseCase: FetchRestHeartRateUseCase,
    private val fetchDailyFeelingUseCase: FetchDailyFeelingUseCase,
) {
    operator fun invoke(
        fromDate: LocalDate,
        toDate: LocalDate,
        includeContributors: Boolean,
    ): Flow<List<RecoveryStateGraphData>> {
        return combine(
            getSleepHrvListByDateFlow(fromDate, toDate),
            getSleepListFlow(fromDate.minusDays(6), toDate),
            getTrendDataListFlow(fromDate, toDate),
            getProgressByDateFlow(fromDate, toDate),
            getRestHeartRateByDateFlow(fromDate, toDate),
            getRecoveryDataListFlow(fromDate, toDate),
            getFeelingByDateFlow(fromDate, toDate),
            getFeelingDistributionFlow(fromDate, toDate),
        ) { sleepHrvList, sleepList, trendDataList, progressByDate, restHeartRateByDate, recoveryDataList, feelingByDate, feelingDistribution ->
            val sleepListByDate =
                sleepList.groupBy { it.timestamp.toLocalDate() }
            val trendDataListByDate =
                trendDataList.groupBy { it.timestamp.toLocalDate() }
            val recoveryDataListByDate = recoveryDataList.groupBy { it.timestamp.toLocalDate() }
            val days = ChronoUnit.DAYS.between(fromDate, toDate).toInt()
            (0..days).mapNotNull { plusDays ->
                val date = fromDate.plusDays(plusDays.toLong())

                val last1DaySleep = sleepListByDate.getOrDefault(date, emptyList())
                val last7DaysSleep = (0..6).mapNotNull {
                    sleepListByDate.getOrDefault(
                        date.minusDays(it.toLong()),
                        emptyList(),
                    )
                }.flatten()

                val sleepHrv = sleepHrvList[date]?.firstOrNull()
                val hrvNormalRange = sleepHrv?.normalRange?.let {
                    it.start.roundToInt()..it.endInclusive.roundToInt()
                } ?: (0..0)
                val last1DayHrv = sleepHrv?.avgHrv?.roundToInt() ?: 0
                val last7DaysHrv = sleepHrv?.avg7DayHrv?.roundToInt() ?: 0
                val hrvBaselineAvailable = hrvNormalRange.first > 0 && hrvNormalRange.last > 0

                val last1DayRestHR = restHeartRateByDate[date]?.restHeartRate?.inBpm?.roundToInt() ?: 0
                val last7DaysRestHRList = (0..6).mapNotNull {
                    restHeartRateByDate[date.minusDays(it.toLong())]
                        .takeIf { rhr -> (rhr?.restHeartRate?.inBpm?.roundToInt() ?: 0) > 0 }
                }
                val last7DaysRestHR =
                    if (last7DaysRestHRList.size >= MIN_VALUES_FOR_7_DAY_AVG) {
                        last7DaysRestHRList.averageOfDouble { it.restHeartRate.inBpm }
                            .takeIfNotNaN()
                            ?.roundToInt()
                            ?: 0
                    } else {
                        0
                    }

                val todayProgress = progressByDate[date]
                val yesterdayProgress = progressByDate[date.minusDays(1)]

                val feeling7Days = feelingByDate.getAvgFeelingForTimeRage(date, 7)
                val feeling42Days = feelingByDate.getAvgFeelingForTimeRage(date, 42)

                val dailyResources = recoveryDataListByDate
                    .getOrDefault(date, emptyList())
                    .map { it.balance }
                    .averageOrNull()
                    ?.let { (it * 100).roundToInt() }
                    ?: 0
                val dailySteps = trendDataListByDate
                    .getOrDefault(date, emptyList())
                    .sumOf { it.steps }
                val dailyScore = getDailyScore(
                    dailyResources = dailyResources,
                    dailySteps = dailySteps,
                )

                val sd1 = last1DaySleep.getAvgSleepDuration()
                val sd7 = last7DaysSleep.getAvgSleepDuration()
                val hrv1 = if (hrvBaselineAvailable) last1DayHrv else 0
                val hrv7 = if (hrvBaselineAvailable) last7DaysHrv else 0
                val tssy = yesterdayProgress?.roundedTss ?: 0
                val tss1 = todayProgress?.roundedTss ?: 0
                val tsb1 = todayProgress?.form?.roundToInt() ?: 0
                val score = calculateRecoveryStateScore(
                    SD1 = sd1,
                    SD7 = sd7,
                    RHR1 = last1DayRestHR,
                    RHR7 = last7DaysRestHR,
                    HRV1 = hrv1,
                    HRV7 = hrv7,
                    HRVL = hrvNormalRange.first,
                    HRVU = hrvNormalRange.last,
                    TSSY = tssy,
                    TSS1 = tss1,
                    ATL = todayProgress?.fatigue?.roundToInt() ?: 0,
                    CTL = todayProgress?.fitness?.roundToInt() ?: 0,
                    F7 = feeling7Days,
                    F42 = feeling42Days,
                    dailyScore = dailyScore,
                )
                if (score.suuntoScore > 0) {
                    RecoveryStateGraphData(
                        date = date,
                        recoveryStateData = RecoveryStateData(
                            recoveryScore = score.suuntoScore,
                            recoveryZone = score.suuntoZone.toRecoveryZone(),
                            contributors = if (includeContributors) {
                                RecoveryStateContributors(
                                    last1DaySleepDuration = sd1,
                                    last7DaysSleepDuration = sd7,
                                    last1DayRestHR = last1DayRestHR,
                                    last7DaysRestHR = last7DaysRestHR,
                                    last1DayHrv = hrv1,
                                    last7DaysHrv = hrv7,
                                    yesterdayTSS = tssy,
                                    todayTSS = tss1,
                                    todayTSB = tsb1,
                                    last7DaysFeeling = feeling7Days,
                                    todayResources = dailyResources,
                                    hrvLowRange = hrvNormalRange.first,
                                    hrvHighRange = hrvNormalRange.last,
                                    feelingDistribution = feelingDistribution,
                                )
                            } else null,
                        ),
                    )
                } else {
                    null
                }
            }
        }
    }

    private fun getFeelingByDateFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ) = fetchDailyFeelingUseCase.fetchDailyFeelingForDateRange(fromDate.minusDays(41), toDate)
        .map { list ->
            list.groupBy { it.localDate }.mapValues { it.value.first() }
        }
        .catch {
            Timber.w(it, "get feeling by date error")
            emit(emptyMap())
        }

    private fun getSleepHrvListByDateFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ) = fetchSleepHrvUseCase.fetchAvgHrv(
        from = fromDate,
        to = toDate,
    ).map { sleepHrvList ->
        sleepHrvList.groupBy { it.date }
    }.catch {
        Timber.w(it, "get sleep hrv by date error")
        emit(emptyMap())
    }

    private fun getSleepListFlow(fromDate: LocalDate, toDate: LocalDate): Flow<List<Sleep>> =
        sleepRepository.fetchSleeps(
            from = fromDate,
            to = toDate,
        ).catch {
            Timber.w(it, "get sleep list error")
            emit(emptyList())
        }

    private fun getTrendDataListFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ) = trendDataRepository.fetchTrendDataForDateRange(
        fromTimestamp = fromDate.atStartOfDay().toEpochMilli(),
        toTimestamp = toDate.atStartOfDay().toEpochMilli(),
        aggregated = false,
    ).catch {
        Timber.w(it, "get trend data list error")
        emit(emptyList())
    }

    private fun getRecoveryDataListFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ): Flow<List<RecoveryData>> = fetchRecoveryDataUseCase.fetchRecoveryData(
        from = fromDate,
        to = toDate,
    ).catch {
        Timber.w(it, "get recovery data list error")
        emit(emptyList())
    }

    private fun getProgressByDateFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ) = flow {
        val username = currentUserDataSource.getCurrentUser().username
        emit(
            getTrainingProgressDataUseCase.invoke(
                GetTrainingProgressDataUseCase.Params(
                    username = username,
                    firstDay = fromDate.minusDays(1),
                    lastDay = toDate,
                    addZeroValuesBeforeFirstRecordedTssDate = false,
                )
            ).groupBy { it.day }.mapValues { it.value.first() }
        )
    }.catch {
        Timber.w(it, "get progress by date error")
        emit(emptyMap())
    }

    private fun getRestHeartRateByDateFlow(
        fromDate: LocalDate,
        toDate: LocalDate,
    ) = fetchRestHeartRateUseCase.fetchRestHeartRateForDateRange(fromDate.minusDays(6), toDate)
        .map { list ->
            list.groupBy { it.localDate }.mapValues { it.value.first() }
        }
        .catch {
            Timber.w(it, "get rest heart rate by date error")
            emit(emptyMap())
        }

    private fun getDailyScore(dailyResources: Int, dailySteps: Int): Int {
        val userSettings = userSettingsDataSource.getUserSettings()
        val gender = if ("male".equals(userSettings.gender, false)) 1 else 0
        val age = calculateAge(userSettings.birthDate)
        val weightInKilogram = userSettings.weight.toDouble()
        val heightInMeter = userSettings.height / 100.0
        return calculateDailyScore(
            age = age,
            gender = gender,
            weightInKilogram = weightInKilogram,
            heightInMeter = heightInMeter,
            dailyResources = dailyResources,
            dailySteps = dailySteps,
        )
    }

    private fun Map<LocalDate, DailyFeeling>.getAvgFeelingForTimeRage(
        date: LocalDate,
        daysCount: Int,
    ): Double {
        val feelingList = (0 until daysCount).mapNotNull {
            get(date.minusDays(it.toLong()))
                .takeIf { feeling ->
                    (feeling?.value ?: 0.0) > 0
                }
        }

        val avg = feelingList.map { it.value }.average().takeIfNotNaN() ?: 0.0
        return (avg * 10).roundToInt() / 10.0
    }

    private fun List<Sleep>.getAvgSleepDuration(): Double {
        val dailySleeps =
            groupBy { it.timestamp.toLocalDate() }.filter { it.value.isNotEmpty() && it.value.any { sleep -> sleep.hasLongSleep } }
        if (dailySleeps.none()) return 0.0
        val seconds = dailySleeps.values
            .sumOf { sleeps ->
                sleeps.sumOf { it.longSleep?.sleepDuration?.inWholeSeconds ?: 0L }
            }
            .toFloat()
        val avgSeconds = seconds / dailySleeps.size
        val hours = avgSeconds / TimeUnit.HOURS.toSeconds(1)
        return (hours * 10).roundToInt() / 10.0
    }

    private fun calculateAge(birthDate: Long): Int {
        val now = LocalDate.now(ZoneId.systemDefault())
        val birthdayDate = Instant.ofEpochMilli(birthDate).atZone(ZoneId.systemDefault())
            .toLocalDate()
        var age = now.year - birthdayDate.year
        if (now.dayOfYear < birthdayDate.dayOfYear) {
            age--
        }
        return age
    }

    private fun Int.toRecoveryZone(): RecoveryZone = when (this) {
        5 -> RecoveryZone.OPTIMAL
        4 -> RecoveryZone.GOOD
        3 -> RecoveryZone.FAIR
        2 -> RecoveryZone.POOR
        1 -> RecoveryZone.LIMITED
        else -> RecoveryZone.NO_DATA
    }


    private fun getFeelingDistributionFlow(
        fromDate: LocalDate,
        toDate: LocalDate
    ): Flow<Map<Int, Double>> = flow {
        val startDate = fromDate.minusDays(6)
        runSuspendCatching {
            val dailyFeelings = fetchDailyFeelingUseCase.fetchDailyFeelingForDateRange(
                startDate, toDate
            ).first()
            val rawFeelings = dailyFeelings.map { dailyFeeling -> dailyFeeling.value.toInt() }
            val validFeelings = rawFeelings.filter { value -> value in 1..5 }
            val countMap = (1..5).associateWith { feeling ->
                validFeelings.count { value -> value == feeling }
            }
            val totalCount = validFeelings.size
            if (totalCount > 0) {
                countMap.mapValues { entry -> 
                    val count = entry.value
                    val percentage = count * 100.0 / totalCount
                    (percentage * 10).roundToInt() / 10.0
                }
            } else {
                (1..5).associateWith { 20.0 }
            }
        }.getOrNull().let { percentageMap ->
            emit(percentageMap ?: (1..5).associateWith { 20.0 })
            
            if (percentageMap == null) {
                Timber.w("Calculate feeling distribution error, returning default 20%% values")
            }
        }
    }

    private companion object {
        private const val MIN_VALUES_FOR_7_DAY_AVG = 3
    }
}
