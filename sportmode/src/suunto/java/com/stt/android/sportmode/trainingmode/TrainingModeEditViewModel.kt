package com.stt.android.sportmode.trainingmode

import android.content.Context
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.home.dashboard.card.WorkoutCardLoader
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.newfeed.FilterTag
import com.stt.android.newfeed.WorkoutCardInfo
import com.stt.android.sportmode.base.SportModeConnectionWrapper
import com.stt.android.sportmode.entity.TrainingModeTarget
import com.stt.android.sportmode.modesetting.ModeSetting
import com.stt.android.sportmode.R
import com.stt.android.sportmode.modesetting.baseStr
import com.stt.android.sportmode.trainingmode.TrainingModeEditActivity.Companion.EXTRA_TRAINING_MODE
import com.stt.android.sportmode.trainingmode.reducer.DeleteTrainingModeReducer
import com.stt.android.sportmode.trainingmode.reducer.EditDataScreensReducer
import com.stt.android.sportmode.trainingmode.reducer.FetchTrainingModeReducer
import com.stt.android.sportmode.trainingmode.reducer.SaveTrainingModeReducer
import com.stt.android.sportmode.trainingmode.reducer.SelectTrainingModeHeaderReducer
import com.stt.android.sportmode.trainingmode.reducer.UpdateModeSettingReducer
import com.stt.android.sportmode.trainingmode.reducer.UpdateTargetByWheelReducer
import com.stt.android.utils.limitByteSizeAsUTF8
import com.stt.android.watch.MissingCurrentWatchException
import com.suunto.connectivity.runsportmodes.exceptions.DuplicatedSportModeNameException
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.concurrent.TimeoutException
import javax.inject.Inject

@HiltViewModel
class TrainingModeEditViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val fetchTrainingModeReducer: FetchTrainingModeReducer,
    private val saveTrainingModeReducer: SaveTrainingModeReducer,
    private val deleteTrainingModeReducer: DeleteTrainingModeReducer,
    private val infoModelFormatter: InfoModelFormatter,
    private val workoutCardLoader: WorkoutCardLoader,
    val sportModeConnectionWrapper: SportModeConnectionWrapper,
    userSettingsController: UserSettingsController,
    dispatchers: CoroutinesDispatchers,
) : CoroutineViewModel(dispatchers) {

    private val _filterUIState: MutableStateFlow<CompetitionTargetFilterViewState> =
        MutableStateFlow(
            CompetitionTargetFilterViewState(
                isImperial = userSettingsController.settings.measurementUnit == MeasurementUnit.IMPERIAL
            )
        )
    val filterUiState = _filterUIState.asStateFlow()

    private val _uiState = MutableStateFlow<CompetitionTargetSelectViewState>(CompetitionTargetSelectViewState.Loading)
    val uiState = _uiState.asStateFlow()

    private val _trainingModeFlow = MutableStateFlow(savedStateHandle.get<TrainingMode>(EXTRA_TRAINING_MODE) ?: TrainingMode())
    internal val trainingModeFlow = _trainingModeFlow.asStateFlow()

    private val _trainingModeEditedFlow = MutableStateFlow(false)
    internal val trainingModeEditedFlow = _trainingModeEditedFlow.asStateFlow()

    private val _trainingModeNameErrorFlow = MutableStateFlow<Int?>(null)
    internal val trainingModeNameErrorFlow = _trainingModeNameErrorFlow.asStateFlow()

    private val _loadingStateFlow = MutableStateFlow(LoadingState.IDLE)
    internal val loadingStateFlow = _loadingStateFlow.asStateFlow()

    internal var savedTrainingMode: TrainingMode? = null

    private var findPeopleNeeded: Boolean = false

    private val distanceFilterConditions: Map<DistanceFilter, (WorkoutHeader) -> Boolean> = mapOf(
        DistanceFilter.FILTER_5 to { it.totalDistance < FIVE_KILOMETERS },
        DistanceFilter.FILTER_5_10 to { it.totalDistance >= FIVE_KILOMETERS && it.totalDistance < TEN_KILOMETERS },
        DistanceFilter.FILTER_10_21 to { it.totalDistance >= TEN_KILOMETERS && it.totalDistance < TWENTY_ONE_KILOMETERS },
        DistanceFilter.FILTER_21_45 to { it.totalDistance >= TWENTY_ONE_KILOMETERS && it.totalDistance < FORTY_FIVE_KILOMETERS },
        DistanceFilter.FILTER_45 to { it.totalDistance > FORTY_FIVE_KILOMETERS },
    )

    init {
        initialize()
        refreshUiState()
    }

    private fun initialize() {
        showLoading()
        reduce(byEdit = false, fetchTrainingModeReducer)
    }

    internal fun updateModeSettings(context: Context, modeSetting: ModeSetting) {
        reduce(UpdateModeSettingReducer(context, modeSetting))
    }

    internal fun updateTrainingModeTarget(context: Context) {
        reduce(UpdateTargetByWheelReducer(context))
    }

    internal fun selectTrainingMode(context: Context) {
        showLoading()
        reduce(SelectTrainingModeHeaderReducer(context, fetchTrainingModeReducer))
    }

    internal fun editDataScreens(context: Context) {
        reduce(EditDataScreensReducer(context))
    }

    internal fun updateTitle(title: String) {
        _trainingModeNameErrorFlow.update {
            if (title.isEmpty()) baseStr.empty_value_not_allowed else null
        }
        reduce {
            copy(
                title = title.limitByteSizeAsUTF8(MAX_TITLE_BYTE_COUNT).toString(),
            )
        }
    }

    internal fun updateDataScreenIndex(index: Int) {
        reduce(byEdit = false) {
            copy(
                dataScreenList = dataScreenList.copy(
                    selectedIndex = index
                )
            )
        }
    }

    internal fun deleteTrainingMode(onDeleted: () -> Unit) {
        showLoading()
        reduceWithCallback(
            deleteTrainingModeReducer,
            onSuccess = {
                Timber.d("delete training mode success: ${_trainingModeFlow.value.id}")
                savedTrainingMode = _trainingModeFlow.value.copy(deleted = true)
                onDeleted()
            },
            onFailure = {},
        )
    }

    internal fun saveTrainingMode(onSaved: (saved: TrainingMode) -> Unit, onFailure: (Throwable) -> Unit) {
        if (!_trainingModeEditedFlow.value && _trainingModeFlow.value.type != TrainingModeType.CREATION) {
            onSaved(_trainingModeFlow.value)
            return
        }
        showLoading()
        reduceWithCallback(
            saveTrainingModeReducer,
            onSuccess = {
                Timber.d("save training mode success: ${_trainingModeFlow.value.id}")
                _trainingModeEditedFlow.update { false }
                savedTrainingMode = _trainingModeFlow.value
                onSaved(_trainingModeFlow.value)
            }, onFailure = { error ->
                if (error is DuplicatedSportModeNameException) {
                    _trainingModeNameErrorFlow.update { R.string.training_mode_duplicate_name }
                } else {
                    onFailure(error)
                }
            }
        )
    }

    internal fun generateTargetSummary(context: Context, target: TrainingModeTarget): String {
        return target.toSummary(context, infoModelFormatter)
    }

    internal fun generateModeSettingSummary(context: Context, modeSetting: ModeSetting): String {
        return modeSetting.summary(context, infoModelFormatter)
    }

    private fun showLoading() {
        if (_loadingStateFlow.value == LoadingState.IDLE) {
            launch {
                _loadingStateFlow.value = LoadingState.PENDING
                delay(400) // When the loading time is not very long, there is no need to show loading
                if (_loadingStateFlow.value == LoadingState.PENDING) {
                    _loadingStateFlow.value = LoadingState.LOADING
                }
            }
        }
    }

    private fun hideLoading() {
        _loadingStateFlow.value = LoadingState.IDLE
    }

    private fun reduce(reduce: suspend TrainingMode.() -> TrainingMode) {
        reduce(byEdit = true, reduce)
    }

    private fun reduce(byEdit: Boolean = true, reduce: suspend TrainingMode.() -> TrainingMode) {
        launch {
            val old = _trainingModeFlow.value
            runSuspendCatching {
                _trainingModeFlow.value = old.reduce()
                if (byEdit && _trainingModeFlow.value != old) {
                    _trainingModeEditedFlow.value = true
                }
            }.onFailure {
                handleFailure(it, reduce.toString())
            }
            hideLoading()
        }
    }

    private fun reduceWithCallback(reduce: suspend TrainingMode.() -> TrainingMode, onSuccess: () -> Unit, onFailure: (Throwable) -> Unit) {
        launch {
            runSuspendCatching {
                _trainingModeFlow.value = _trainingModeFlow.value.reduce()
            }.onFailure {
                handleFailure(it, reduce.toString(), onFailure)
            }.onSuccess {
                onSuccess()
            }
            hideLoading()
        }
    }

    private fun handleFailure(throwable: Throwable, reduceName: String, onFailure: (Throwable) -> Unit = {}) {
        when (throwable) {
            is MissingCurrentWatchException -> {
                Timber.i("$reduceName, Missing current watch")
            }
            is TimeoutException -> {
                Timber.i("$reduceName, Timeout")
            }
            else -> {
                Timber.w(throwable, "$reduceName error.")
                onFailure(throwable)
            }
        }
    }

    private fun refreshUiState() {
        viewModelScope.launch {
            _uiState.value = CompetitionTargetSelectViewState.Loading
            if (notAnyFilter()) {
                findPeopleNeeded = false
            }
            val result = runSuspendCatching {
                loadTargetWorkouts(
                    _filterUIState.value.selectedDistanceFilter,
                    _filterUIState.value.selectedFilterTag,
                    _filterUIState.value.searchQuery,
                    _filterUIState.value.selectedSortBy
                )
            }.getOrElse { e ->
                Timber.w(e, "Failed to load target workouts for competition: $e")
                CompetitionTargetSelectViewState.Error
            }
            _uiState.value = result
        }
    }

    private fun notAnyFilter() =
        _filterUIState.value.selectedDistanceFilter == DistanceFilter.ALL && _filterUIState.value.searchQuery.isEmpty()

    private suspend fun loadTargetWorkouts(distanceFilter: DistanceFilter, filterTag: FilterTag, searchQuery: String, sortBy: SortBy): CompetitionTargetSelectViewState.Loaded =
        withContext(io) {
            val targetWorkouts = when (filterTag) {
                FilterTag.ALL -> loadFolloweeWorkouts(WORKOUTS_TO_LOAD) + loadOwnWorkouts(WORKOUTS_TO_LOAD)
                FilterTag.FOLLOWING -> loadFolloweeWorkouts(WORKOUTS_TO_LOAD)
                else -> loadOwnWorkouts(WORKOUTS_TO_LOAD)
            }
                .asSequence()
                .filter { workoutCardInfo ->
                    workoutCardInfo.workoutHeader.activityType == _trainingModeFlow.value.activityType &&
                        distanceFilterConditions[distanceFilter]?.invoke(workoutCardInfo.workoutHeader) ?: true &&
                        (searchQuery.isEmpty() || matchesSearchQuery(workoutCardInfo, searchQuery))
                }.sortedWith(
                    when (sortBy) {
                        SortBy.POST_TIME -> compareByDescending { it.workoutHeader.stopTime }
                        SortBy.PACE -> compareByDescending { it.workoutHeader.avgSpeed }
                        SortBy.DURATION -> compareBy {  it.workoutHeader.totalTime }
                    }
                ).toList()
            if (notAnyFilter() && targetWorkouts.isEmpty()) {
                findPeopleNeeded = true
            }
            CompetitionTargetSelectViewState.Loaded(targetWorkouts, findPeopleNeeded = findPeopleNeeded)
        }

    private fun matchesSearchQuery(
        workoutCardInfo: WorkoutCardInfo,
        searchQuery: String
    ) = workoutCardInfo.user.realNameOrUsername.uppercase().contains(searchQuery.uppercase()) ||
        getPostYearAndMonth(workoutCardInfo.workoutHeader.stopTime).contains(
            searchQuery
        )

    private fun getPostYearAndMonth(timestamp: Long): String {
        val localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault())
        return "${localDateTime.year}${localDateTime.monthValue}"
    }


    fun updateFilterState(
        searchQuery: String? = null,
        distanceFilter: DistanceFilter? = null,
        sortBy: SortBy? = null
    ) {
        _filterUIState.update {
            it.copy(
                searchQuery = searchQuery ?: it.searchQuery,
                selectedDistanceFilter = distanceFilter ?: it.selectedDistanceFilter,
                selectedSortBy = sortBy ?: it.selectedSortBy
            )
        }
        refreshUiState()
    }

    fun onFilterTagChanged(filterTag: FilterTag) {
        if (filterTag != _filterUIState.value.selectedFilterTag) {
            _filterUIState.update { it.copy(selectedFilterTag = filterTag) }
        }
        refreshUiState()
    }

    private suspend fun loadFolloweeWorkouts(limit: Long) = runSuspendCatching {
        workoutCardLoader.loadFolloweeWorkouts(limit)
    }.getOrElse { e ->
        Timber.w(e, "Unable to following workouts")
        emptyList()
    }

    private suspend fun loadOwnWorkouts(limit: Long) = runSuspendCatching {
        workoutCardLoader.loadOwnWorkouts(limit)
    }.getOrElse { e ->
        Timber.w(e, "Unable to following workouts")
        emptyList()
    }


    fun targetWorkoutSelected(targetWorkout: WorkoutCardInfo?) {
        reduce(object : TrainingModeReducer {
            override suspend fun invoke(trainingMode: TrainingMode): TrainingMode {
                return trainingMode.copy(targetWorkoutCardInfo = targetWorkout)
            }
        })
    }

    companion object {
        const val WORKOUTS_TO_LOAD = 50L
        const val FIVE_KILOMETERS = 5 * 1000
        const val TEN_KILOMETERS = 10 * 1000
        const val TWENTY_ONE_KILOMETERS = 21 * 1000
        const val FORTY_FIVE_KILOMETERS = 45 * 1000
        const val MAX_TITLE_BYTE_COUNT = 63
        const val MAX_RIVAL_NAME_BYTE_COUNT = 64
    }
}

enum class DistanceFilter {
    ALL,
    FILTER_5,
    FILTER_5_10,
    FILTER_10_21,
    FILTER_21_45,
    FILTER_45
}

enum class SortBy {
    POST_TIME,
    PACE,
    DURATION
}

data class CompetitionTargetFilterViewState (
    val searchQuery: String = "",
    val selectedDistanceFilter: DistanceFilter = DistanceFilter.ALL,
    val selectedSortBy: SortBy = SortBy.POST_TIME,
    val isImperial: Boolean = false,
    val selectedFilterTag: FilterTag = FilterTag.ALL,
)

sealed class CompetitionTargetSelectViewState {
    data object Loading : CompetitionTargetSelectViewState()
    data class Loaded(
        val targetWorkoutList: List<WorkoutCardInfo> = emptyList(),
        val findPeopleNeeded: Boolean
    ) : CompetitionTargetSelectViewState()
    data object Error : CompetitionTargetSelectViewState()
}

enum class LoadingState {
    IDLE,
    PENDING,
    LOADING,
    LOADING_MORE,
    LOADED_ALL,
}
