package com.stt.android.data.usersettings

import com.stt.android.domain.user.DomainUserSettings
import com.stt.android.domain.user.UserSettingsDataSource

open class FakeUserSettingsDataSource : UserSettingsDataSource {
    override fun isLocallyChanged(): Boolean = false

    override fun getUserSettings(): DomainUserSettings {
        return DomainUserSettings(
            measurementUnit = null,
            hrMaximum = 0,
            gender = null,
            height = 0,
            weight = 0,
            birthDate = 0,
            email = null,
            screenBacklightSetting = null,
            gpsFiltering = false,
            altitudeOffset = 0.0f,
            selectedMapType = null,
            notifyNewFollower = false,
            notifyWorkoutComment = false,
            notifyWorkoutFollowingShare = false,
            autoApproveFollowers = false,
            emailDigest = false,
            optinAccepted = 0,
            optinRejected = 0,
            optinLastShown = 0,
            optinShowCount = 0,
            analyticsUUID = "",
            country = null,
            countrySubdivision = null,
            language = "",
            newFollowerNotificationEnabled = false,
            workoutCommentNotificationEnabled = false,
            workoutShareNotificationEnabled = false,
            workoutReactionNotificationEnabled = false,
            facebookFriendJoinNotificationEnabled = false,
            realName = null,
            description = null,
            sharingFlagPreference = 0,
            hasOutboundPartnerConnections = false,
            phoneNumber = null,
            predefinedReplies = listOf(),
            preferredTssCalculationMethods = mapOf(),
            firstDayOfTheWeek = null,
            tagAutomation = null,
            favoriteSports = emptyList(),
            motivations = emptyList(),
            disabledAppRatingSuggestions = listOf(),
            automaticUpdateDisabledWatches = emptyList(),
            samplingBucketValue = 0.0,
            privateAccount = false,
            menstrualCycleSettings = null,
            hrRest = 0,
            combinedIntensityZones = null,
            newActivitySyncedNotificationEnabled = false,
            showLocale = false
        )
    }

    override suspend fun saveUserSettings(remoteSettings: DomainUserSettings) {
    }

    override fun getPushApp(): String = "pushapp"
}
