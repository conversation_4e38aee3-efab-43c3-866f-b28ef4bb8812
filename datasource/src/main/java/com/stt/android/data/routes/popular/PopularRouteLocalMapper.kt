package com.stt.android.data.routes.popular

import com.stt.android.data.EntityMapper
import com.stt.android.data.source.local.routes.LocalPoint
import com.stt.android.data.source.local.routes.LocalRouteLocalization
import com.stt.android.data.source.local.routes.LocalRouteActivityMetadata
import com.stt.android.data.source.local.routes.LocalRouteMetaData
import com.stt.android.data.source.local.routes.LocalRouteSegment
import com.stt.android.data.source.local.routes.popular.LocalPopularRoute
import com.stt.android.domain.Point
import com.stt.android.domain.routes.RouteLocationLocale
import com.stt.android.domain.routes.RouteLocationMetaData
import com.stt.android.domain.routes.RouteMetaData
import com.stt.android.domain.routes.RouteSegment
import com.stt.android.domain.routes.RouteVisibility
import com.stt.android.domain.routes.RouteWatchSyncState
import com.stt.android.domain.routes.TopRoute
import timber.log.Timber
import javax.inject.Inject

class PopularRouteLocalMapper
@Inject constructor() : EntityMapper<LocalPopularRoute, TopRoute> {
    override fun toDomainEntity(): (LocalPopularRoute) -> TopRoute {
        return {
            val numPoints = it.segments.sumOf { segment -> segment.routePoints.size }
            Timber.v("toDomainEntity() called (id=${it.id}, watchRouteId=${it.watchRouteId}, key=${it.key}, name=${it.name}, numPoints=$numPoints, totalDistance=${it.totalDistance})")
            TopRoute(
                id = it.id,
                watchRouteId = it.watchRouteId,
                topRouteId = it.routeId,
                key = it.key,
                name = it.name,
                visibility = RouteVisibility.valueOf(it.visibility),
                activityId = it.activityType,
                averageSpeed = it.averageSpeed,
                totalDistance = it.totalDistance,
                ascent = it.ascent,
                descent = it.descent,
                startPoint = it.startPoint.toPoint(),
                centerPoint = it.centerPoint.toPoint(),
                stopPoint = it.stopPoint.toPoint(),
                locallyChanged = it.locallyChanged,
                deleted = it.deleted,
                createdDate = it.createdDate,
                modifiedDate = it.modifiedDate,
                segmentsModifiedDate = it.segmentsModifiedDate,
                watchSyncState = RouteWatchSyncState.valueOf(it.watchSyncState),
                watchSyncResponseCode = it.watchSyncResponseCode,
                watchEnabled = it.watchEnabled,
                favorite = true,
                isInProgress = it.isInProgress,
                turnWaypointsEnabled = it.turnWaypointsEnabled,
                segments = it.segments.map { segment -> segment.toRouteSegment() },
                metaData = it.metaData?.toRouteMetaData()
            )
        }
    }

    override fun toDataEntity(): (TopRoute) -> LocalPopularRoute {
        return {
            val numPoints = it.segments.sumOf { segment -> segment.routePoints.size }
            Timber.v("toDataEntity() called (id=${it.id}, watchRouteId=${it.watchRouteId}, key=${it.key}, name=${it.name}, numPoints=$numPoints, totalDistance=${it.totalDistance})")
            LocalPopularRoute(
                id = it.id,
                watchRouteId = it.watchRouteId,
                routeId = it.topRouteId,
                key = it.key,
                name = it.name,
                visibility = it.visibility.name,
                activityType = it.activityId,
                averageSpeed = it.averageSpeed,
                totalDistance = it.totalDistance,
                ascent = it.ascent,
                descent = it.descent,
                startPoint = it.startPoint.toLocalPoint(),
                centerPoint = it.centerPoint.toLocalPoint(),
                stopPoint = it.stopPoint.toLocalPoint(),
                locallyChanged = it.locallyChanged,
                deleted = it.deleted,
                createdDate = it.createdDate,
                modifiedDate = it.modifiedDate,
                segmentsModifiedDate = it.segmentsModifiedDate,
                watchSyncState = it.watchSyncState.name,
                watchSyncResponseCode = it.watchSyncResponseCode,
                watchEnabled = it.watchEnabled,
                isInProgress = it.isInProgress,
                turnWaypointsEnabled = it.turnWaypointsEnabled,
                segments = it.segments.map { segment -> segment.toLocalRouteSegment() },
                metaData = it.metaData?.toLocalRouteMetaData()
            )
        }
    }
}

fun LocalRouteSegment.toRouteSegment() = RouteSegment(
    startPoint = startPoint.toPoint(),
    endPoint = endPoint.toPoint(),
    position = position,
    routePoints = routePoints.map { it.toPoint() },
    ascent = ascent,
    descent = descent
)

fun LocalRouteMetaData.toRouteMetaData() = RouteMetaData(
    locationMetas = locationMetas.mapValues {
        it.value?.toRouteLocationMetaData()
    }
)

fun LocalRouteActivityMetadata.toRouteLocationMetaData() = RouteLocationMetaData(
    totalDistance = totalDistance,
    ascent = ascent,
    descent = descent,
    duration = duration,
    popularity = popularity,
    averageSpeed = averageSpeed,
    routeImages = routeImages,
    locales = locales?.mapValues { it.value.toRouteLocationLocale() }
)

fun LocalRouteLocalization.toRouteLocationLocale() = RouteLocationLocale(
    name = name,
    startAddress = startAddress,
    endAddress = endAddress
)

fun RouteSegment.toLocalRouteSegment() = LocalRouteSegment(
    startPoint = startPoint.toLocalPoint(),
    endPoint = endPoint.toLocalPoint(),
    position = position,
    routePoints = routePoints.map { it.toLocalPoint() },
    ascent = ascent,
    descent = descent
)

fun Point.toLocalPoint() = LocalPoint(
    longitude = longitude,
    latitude = latitude,
    altitude = altitude,
    name = name,
    type = type,
    description = description,
)

fun LocalPoint.toPoint() = Point(
    longitude = longitude,
    latitude = latitude,
    altitude = altitude,
    name = name,
    type = type,
    description = description,
)

fun RouteMetaData.toLocalRouteMetaData() = LocalRouteMetaData(
    locationMetas = locationMetas.mapValues { (_, metaData) -> metaData?.toLocalRouteLocationMetaData() }
)

fun RouteLocationMetaData.toLocalRouteLocationMetaData() = LocalRouteActivityMetadata(
    totalDistance = totalDistance,
    ascent = ascent,
    descent = descent,
    duration = duration,
    popularity = popularity,
    averageSpeed = averageSpeed,
    routeImages = routeImages,
    locales = locales?.mapValues { it.value.toLocalRouteLocationLocale() }
)

fun RouteLocationLocale.toLocalRouteLocationLocale() = LocalRouteLocalization(
    name = name,
    startAddress = startAddress,
    endAddress = endAddress
)
