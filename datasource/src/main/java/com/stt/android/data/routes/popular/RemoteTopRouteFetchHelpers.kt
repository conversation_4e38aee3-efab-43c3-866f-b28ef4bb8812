package com.stt.android.data.routes.popular

import com.stt.android.domain.routes.Route
import com.stt.android.domain.routes.TopRoute
import com.stt.android.domain.routes.simplifyRouteSegments

/**
 * Filters average speed and simplifies routes before upserting them to the local datasource.
 * Call [filterNewestRoutesFromRemote] beforehand to make sure newer routes in local aren't
 * overwritten by older remote routes
 *
 * @return List of routes that were upserted. Local datasource can do small changes like update
 * the ID while it stores Routes, and those updates aren't visible in the returned list. Use
 * the list only for small checks like the count or if any of the Routes need to be synced
 * to the watch
 */
suspend fun storeRemoteRoutesToLocal(
    remoteRoutes: List<TopRoute>,
    localDataSource: PopularRouteDataSource,
): List<TopRoute> {
    val simplified = simplifyIfNeeded(filterAverageSpeed(remoteRoutes))
    if (simplified.isNotEmpty()) {
        localDataSource.updateOrCreate(simplified)
    }
    return simplified
}

/**
 * Filter only new/er routes from remote source
 * @return list of remote routes that are new or newer than locally existing ones
 */
fun filterNewestRoutesFromRemote(local: List<TopRoute>, remote: List<TopRoute>): List<TopRoute> {
    return if (remote.isNotEmpty()) {
        // Map local route by both key and watchRouteId to its modified date
        val localKeyMap = local.associateBy({ it.key }, { it })

        // On clean installs when syncing routes back from watch is enabled and happens before sync
        // with server, local routes will have watchRouteId set with empty key
        val localWatchRouteIdMap = local.filter { it.watchRouteId > 0 }
            .associateBy({ it.watchRouteId }, { it.modifiedDate })

        // Filter remote routes that are new or newer than the local ones
        remote.filter {
            it.isNewerThan(
                localKeyMap[it.key]?.modifiedDate ?: localWatchRouteIdMap[it.watchRouteId] ?: 0L
            )
        }
    } else {
        emptyList()
    }
}

private fun simplifyIfNeeded(routes: List<TopRoute>): List<TopRoute> =
    routes.map { route ->
        route.copy(segments = simplifyRouteSegments(route.segments))
    }

private fun filterAverageSpeed(routes: List<TopRoute>): List<TopRoute> =
    routes.map { route ->
        route.copy(
            averageSpeed = route.averageSpeed.coerceAtLeast(Route.MIN_ALLOWED_SPEED_IN_MS)
        )
    }
