package com.stt.android.data.sleep

import com.stt.android.data.source.local.sleep.LocalSleepStage
import com.stt.android.data.source.local.sleep.LocalSleepStageInterval
import com.stt.android.domain.sleep.SleepStage
import com.stt.android.domain.sleep.SleepStageInterval
import javax.inject.Inject
import kotlin.time.Duration.Companion.seconds

/**
 * A mapper class for mapping remote data [LocalSleepStageInterval] entity to domain [SleepStageInterval] entity
 */
class SleepStagesLocalMapper @Inject constructor() {

    fun toDataEntity(syncedStatus: Int): (SleepStageInterval) -> LocalSleepStageInterval {
        return { sleepStage ->
            LocalSleepStageInterval(
                startTimestamp = sleepStage.startTimestamp,
                timeISO8601 = sleepStage.timeISO8601,
                syncedStatus = syncedStatus,
                stage = sleepStage.stage.toLocal(),
                durationSeconds = sleepStage.duration.inWholeMilliseconds / 1000.0F,
            )
        }
    }

    fun toDataEntityList(syncedStatus: Int): (List<SleepStageInterval>) -> List<LocalSleepStageInterval> =
        { list ->
            list.map { toDataEntity(syncedStatus)(it) }
        }

    fun toDomainEntity(): (LocalSleepStageInterval) -> SleepStageInterval {
        return { localSleepStage ->
            SleepStageInterval(
                duration = localSleepStage.durationSeconds.toDouble().seconds,
                stage = localSleepStage.stage.toDomain(),
                timeISO8601 = localSleepStage.timeISO8601,
            )
        }
    }

    fun toDomainEntityList(): (List<LocalSleepStageInterval>) -> List<SleepStageInterval> =
        { list ->
            list.map { toDomainEntity()(it) }
        }
}

private fun SleepStage.toLocal(): LocalSleepStage = when (this) {
    SleepStage.AWAKE -> LocalSleepStage.AWAKE
    SleepStage.REM -> LocalSleepStage.REM
    SleepStage.LIGHT -> LocalSleepStage.LIGHT
    SleepStage.DEEP -> LocalSleepStage.DEEP
}

private fun LocalSleepStage.toDomain(): SleepStage = when (this) {
    LocalSleepStage.AWAKE -> SleepStage.AWAKE
    LocalSleepStage.REM -> SleepStage.REM
    LocalSleepStage.LIGHT -> SleepStage.LIGHT
    LocalSleepStage.DEEP -> SleepStage.DEEP
}
