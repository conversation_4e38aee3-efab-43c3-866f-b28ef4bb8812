package com.stt.android.compose.widgets

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.layout.positionInParent
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.ui.R
import kotlin.math.max
import kotlin.math.roundToInt

@Composable
fun <T> TiledSegmentedControl(
    segment: T,
    segments: List<T>,
    moreSegments: List<T>,
    onSegmentToggled: (T) -> Unit,
    shortName: @Composable T.() -> String,
    longName: @Composable T.() -> String,
    description: @Composable T.() -> String,
    bottomSheetTitle: @Composable ColumnScope.() -> Unit,
    modifier: Modifier = Modifier,
) {
    var selectedSegment by remember(segment) { mutableStateOf(segment) }
    val segmentsInfo = remember { mutableStateMapOf<T, Pair<Int, Int>>() }
    var containerSize by remember { mutableStateOf(IntSize.Zero) }

    val density = LocalDensity.current
    val defaultMinHeightPx = with(density) { 36.dp.roundToPx() }
    var minHeightPx by remember { mutableIntStateOf(defaultMinHeightPx) }
    val minHeight = with(density) { minHeightPx.toDp() }

    var showPicker by remember { mutableStateOf(false) }

    val segmentCount = segments.size

    BoxWithConstraints(modifier = modifier.clip(CircleShape)) {
        Box(
            modifier = Modifier
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.dividerColor,
                    shape = CircleShape,
                )
                .size(containerSize.width.toDp(), containerSize.height.toDp()),
        )
        Row(
            modifier = Modifier
                .onGloballyPositioned { coordinates ->
                    containerSize = coordinates.size
                },
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {
                segments.forEach { item ->
                    val selected = selectedSegment == item
                    Segment(
                        modifier = Modifier.onGloballyPositioned { coordinates ->
                            val size = coordinates.size
                            val position = coordinates.positionInParent()
                            minHeightPx = max(minHeightPx, size.height)
                            segmentsInfo.put(item, Pair(position.x.roundToInt(), size.width))
                        },
                        text = item.shortName(),
                        color = with(MaterialTheme.colorScheme) { if (selected) onPrimary else onSurface },
                        minWidth = 56.dp,
                        minHeight = minHeight,
                        selected = selected,
                        onClick = {
                            if (!selected) {
                                selectedSegment = item
                                onSegmentToggled(item)
                            }
                        },
                    )
                }
            }

            if (moreSegments.isNotEmpty()) {
                val item = moreSegments.firstOrNull { it == selectedSegment }
                val minWidth = if (<EMAIL>) {
                    <EMAIL> / (segmentCount + 1)
                } else 0.dp
                Segment(
                    modifier = Modifier
                        .onSizeChanged { size ->
                            minHeightPx = max(minHeightPx, size.height)
                        },
                    text = item?.shortName() ?: stringResource(R.string.segmented_control_more),
                    color = with(MaterialTheme.colorScheme) { if (item != null) onPrimary else onSurface },
                    minWidth = minWidth.coerceAtLeast(75.dp),
                    minHeight = minHeight,
                    selected = item != null,
                    showIndicator = true,
                    onClick = { showPicker = true },
                )
            }
        }
    }

    if (showPicker) {
        GenericSegmentSelectionBottomSheet(
            selectedSegment = selectedSegment,
            list = moreSegments,
            onSelected = {
                selectedSegment = it
                onSegmentToggled(it)
                showPicker = false
            },
            onDismiss = { showPicker = false },
            longName = longName,
            description = description,
            bottomSheetTitle = bottomSheetTitle,
        )
    }
}
