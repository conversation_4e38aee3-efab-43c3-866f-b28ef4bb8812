package com.stt.android.divecustomization.customization.ui.gases.addgas

import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.TopAppBar
import androidx.compose.material.contentColorFor
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.common.viewstate.ViewState
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.divecustomization.customization.entities.gases.DiveAddGasTitle
import java.util.Locale
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@Composable
fun AddGasTopBar(
    content: ViewState<DiveAddGasTitle>,
    onCancel: () -> Unit,
    onSave: () -> Unit,
    modifier: Modifier = Modifier,
) {
    TopAppBar(
        title = {
            when (content) {
                is ViewState.Loaded -> {
                    val data = content.data!!
                    Text(
                        text = data.gasName
                    )
                }
                else -> {
                    // don't show anything
                }
            }
        },
        actions = {
            TextButton(
                modifier = Modifier.wrapContentWidth(unbounded = true),
                colors = ButtonDefaults.textButtonColors(
                    contentColor = contentColorFor(MaterialTheme.colors.primary)
                ),
                onClick = onSave
            ) {
                Text(
                    text = stringResource(BaseR.string.save).uppercase(Locale.getDefault()),
                    color = colorResource(CR.color.accent),
                    fontWeight = FontWeight.Bold
                )
            }
        },
        navigationIcon = {
            SuuntoIconButton(
                icon = SuuntoIcons.ActionClose,
                onClick = onCancel,
            )
        },
        contentColor = MaterialTheme.colors.onSurface,
        backgroundColor = MaterialTheme.colors.surface,
        modifier = modifier
    )
}

@Preview
@Composable
private fun AddGasTopBarPreview() {
    val content: ViewState<DiveAddGasTitle> = ViewState.Loaded(
        DiveAddGasTitle(
            helium = 0.2,
            oxygen = 0.2,
            newGas = true
        )
    )

    AddGasTopBar(
        content = content,
        onCancel = {},
        onSave = {}
    )
}
