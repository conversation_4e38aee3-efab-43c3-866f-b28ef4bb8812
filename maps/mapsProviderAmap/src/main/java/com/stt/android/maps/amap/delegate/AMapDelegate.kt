package com.stt.android.maps.amap.delegate

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import androidx.core.graphics.createBitmap
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.model.BitmapDescriptorFactory
import com.amap.api.maps.model.CameraPosition
import com.amap.api.maps.model.Marker
import com.amap.api.maps.model.PolylineOptions
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.model.LatLng
import com.stt.android.maps.MarkerZPriority
import com.stt.android.maps.SuuntoBitmapDescriptorFactory
import com.stt.android.maps.SuuntoCameraPosition
import com.stt.android.maps.SuuntoCameraUpdate
import com.stt.android.maps.SuuntoCameraUpdateNewPosition
import com.stt.android.maps.SuuntoCircle
import com.stt.android.maps.SuuntoCircleOptions
import com.stt.android.maps.SuuntoFreeCameraUpdate
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMap.Companion.MAP_3D_MODE_TILT
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.maps.SuuntoPolyline
import com.stt.android.maps.SuuntoPolylineOptions
import com.stt.android.maps.SuuntoProjection
import com.stt.android.maps.SuuntoRulerLineOptions
import com.stt.android.maps.SuuntoScaleBarOptions
import com.stt.android.maps.SuuntoStartingPointFeature
import com.stt.android.maps.SuuntoTileOverlay
import com.stt.android.maps.SuuntoTileOverlayOptions
import com.stt.android.maps.SuuntoTopRouteFeature
import com.stt.android.maps.SuuntoUiSettings
import com.stt.android.maps.amap.AMapsProvider
import com.stt.android.maps.amap.R
import com.stt.android.maps.amap.SuuntoAmapView
import com.stt.android.maps.amap.SuuntoUrlTileProvider
import com.stt.android.maps.amap.toAMap
import com.stt.android.maps.amap.toAMapRotation
import com.stt.android.maps.amap.toAMapType
import com.stt.android.maps.amap.toGoogle
import com.stt.android.maps.amap.toSuunto
import com.stt.android.maps.cluster.TopRouteFeatureDescriptor
import com.stt.android.maps.colortrack.ColorTrackDescriptor
import com.stt.android.maps.delegate.MapDelegate
import com.stt.android.maps.location.SuuntoLocationSource
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import kotlin.math.roundToInt

class AMapDelegate(
    internal val map: AMap,
    internal val mapView: SuuntoAmapView,
    private val provider: AMapsProvider
) : MapDelegate {

    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    private var onCameraIdleListener: GoogleMap.OnCameraIdleListener? = null
    private var locationSource: SuuntoLocationSource? = null
    private val defaultCompassMargins: Rect

    internal var mapPadding = doubleArrayOf(0.0, 0.0, 0.0, 0.0)
        private set

    private var dashPolyline: SuuntoPolyline? = null

    private var marker3d: SuuntoMarker? = null

    private var myLocationMarker: SuuntoMarker? = null

    private val cameraChangeListener = object : AMap.OnCameraChangeListener {
        override fun onCameraChange(cameraPosition: CameraPosition?) {
            cameraPosition?.let {
                myLocationMarker?.setRotation(it.bearing.toAMapRotation())
            }
        }

        override fun onCameraChangeFinish(cameraPosition: CameraPosition?) {
            onCameraIdleListener?.onCameraIdle()
        }
    }

    init {
        val resources = mapView.context.resources
        val fourDp = resources.getDimensionPixelSize(R.dimen.amap_provider_mapbox_four_dp)
        defaultCompassMargins = Rect(fourDp, fourDp, fourDp, fourDp)

        map.addOnCameraChangeListener(cameraChangeListener)
    }

    internal fun onDestroy() {
        setOnScaleListener(null)
        setOnMapClickListener(null)
        setOnMapLongClickListener(null)
        setOnCameraMoveListener(null)
        setOnCameraMoveStartedListener(null)
        setOnCameraIdleListener(null)
        setOnMapMoveListener(null)
        setLocationSource(null)
        map.removeOnCameraChangeListener(cameraChangeListener)
        scope.cancel()
        marker3d = null
        myLocationMarker = null
    }

    override fun animateCamera(
        update: SuuntoCameraUpdate,
        durationMs: Int,
        callback: SuuntoMap.CancelableCallback?
    ) {
        if (update is SuuntoFreeCameraUpdate) {
            throw Exception("animateCamera() does not support SuuntoFreeCameraUpdate.")
        }

        scope.launch {
            if (update is SuuntoCameraUpdateNewPosition) {
                update.cameraOptions.zoom?.let {
                    moveZoomCamera(it)
                    delay(500L)
                }
            }
            map.animateCamera(update.toAMap(this@AMapDelegate), durationMs.toLong(), callback?.toAMap())
        }
    }

    override fun update3dLocation(latLng: LatLng?, altitude: Double) {
        if (latLng == null) {
            marker3d?.remove()
            marker3d = null
            return
        }

        if (marker3d == null) {
            val bitmapDescriptorFactory = SuuntoBitmapDescriptorFactory(mapView.context)
            val options = SuuntoMarkerOptions().anchor(0.5f, 0.5f)
                .icon(bitmapDescriptorFactory.forCurrentLocationDot())
                .position(latLng)
                .iconScale(0.8f)
                .zPriority(MarkerZPriority.SELECTED_GEOPOINT)
            marker3d = addMarker(options)
        } else {
            marker3d?.setPosition(latLng)
        }
    }

    override fun addCircle(options: SuuntoCircleOptions): SuuntoCircle {
        return SuuntoCircle(AMapCircleDelegate(map.addCircle(options.toAMap())))
    }

    override fun addMarker(options: SuuntoMarkerOptions): SuuntoMarker? {
        val amapMarker = map.addMarker(options.toAMap()) ?: return null
        val markerDelegate = AMapMarkerDelegate(amapMarker)
        return SuuntoMarker(markerDelegate, options).also {
            amapMarker.`object` = it
        }
    }

    override fun addPolyline(options: SuuntoPolylineOptions): SuuntoPolyline {
        return SuuntoPolyline(AMapPolylineDelegate(map.addPolyline(options.toAMap())))
    }

    override fun addTileOverlay(options: SuuntoTileOverlayOptions): SuuntoTileOverlay? {
        val tileOverlayOptions = options.toAMap().apply {
            val tileSource = options.tileSource
            if (tileProvider == null && tileSource != null) {
                tileProvider(SuuntoUrlTileProvider(tileSource))
            }
        }
        val tileOverlay = map.addTileOverlay(tileOverlayOptions) ?: return null

        // Copy the options so that the overlay delegate may safely update them without affecting
        // the original
        val optionsCopy = options.copy()
        return SuuntoTileOverlay(AMapTileOverlayDelegate(optionsCopy, tileOverlay))
    }

    override fun clear() = map.clear()

    override fun getCameraPosition(): SuuntoCameraPosition = map.cameraPosition.toSuunto()

    override fun getLocationSource() = locationSource

    override fun getProjection(): SuuntoProjection {
        return SuuntoProjection(AMapProjectionDelegate(map.projection))
    }

    override fun getTerrainExaggeration(): Double = 1.0

    override fun getUiSettings(): SuuntoUiSettings {
        return SuuntoUiSettings(AMapUiSettingsDelegate(this, mapView, map.uiSettings))
    }

    override fun isMap3dModeEnabled(): Boolean {
        return map.cameraPosition.tilt != 0f
    }

    override fun isMap3dModeSupported(): Boolean = true

    override fun isMyLocationEnabled(): Boolean = true

    override fun moveCamera(update: SuuntoCameraUpdate) {
        if (update is SuuntoFreeCameraUpdate) {
            moveFreeCamera(update)
        }
        scope.launch {
            if (update is SuuntoCameraUpdateNewPosition) {
                update.cameraOptions.zoom?.let {
                    moveZoomCamera(it)
                    delay(100L)
                }
            }
            map.moveCamera(update.toAMap(this@AMapDelegate))
        }
    }

    private fun moveFreeCamera(update: SuuntoFreeCameraUpdate) {
        val cameraUpdate = CameraUpdateFactory.changeLatLng(update.markerPosition.toAMap())
        map.moveCamera(cameraUpdate)
    }

    private fun moveZoomCamera(zoom: Float) {
        val cameraUpdate = CameraUpdateFactory.zoomTo(zoom)
        map.moveCamera(cameraUpdate)
    }

    override fun setLocationSource(source: SuuntoLocationSource?) {
        if (source != locationSource) {
            locationSource = source
            map.setLocationSource(source?.toAMap())
        }
    }

    override fun setMap3dModeEnabled(enabled: Boolean) {
        if (enabled) {
            map.moveCamera(CameraUpdateFactory.changeTilt(MAP_3D_MODE_TILT))
        } else {
            map.moveCamera(CameraUpdateFactory.changeTilt(0f))
        }
    }

    override fun setMapType(type: String, onStyleLoaded: (() -> Unit)?) {
        provider.options.customMapTypes[type]?.let {
            map.mapType = it.aMapType
        } ?: run {
            map.mapType = type.toAMapType()
            map.setCustomMapStyle(null)
        }
    }

    override fun setMyLocationEnabled(enabled: Boolean) {
        // do nothing here
    }

    override fun setPadding(left: Int, top: Int, right: Int, bottom: Int) {
        getUiSettings().setCompassMargins(
            left + defaultCompassMargins.left,
            top + defaultCompassMargins.top,
            right + defaultCompassMargins.right,
            bottom + defaultCompassMargins.bottom
        )

        mapPadding[0] = left.toDouble()
        mapPadding[1] = top.toDouble()
        mapPadding[2] = right.toDouble()
        mapPadding[3] = bottom.toDouble()
    }

    override fun snapshot(callback: SuuntoMap.SnapshotReadyCallback, bitmap: Bitmap?) {
        try {
            map.getMapScreenShot(callback.toAMap())
        } catch (e: Exception) {
            Timber.w("snapshot error: $e")
        }
    }

    override fun showScaleBar(options: SuuntoScaleBarOptions) {
    }

    override fun removeScaleBar() {
    }

    override fun setCompassEnabled(enabled: Boolean) {
        val visible = if (!enabled) {
            false
        } else {
            val bearing = map.cameraPosition.bearing
            bearing != 0f && bearing != 360f
        }
        mapView.updateCompassViewVisible(visible)
    }

    override suspend fun selectFeature(layerId: String, latLng: LatLng): SuuntoTopRouteFeature? {
        // Not supported
        return null
    }

    override fun selectFeature(layerId: String, routeId: String) {
        // Not supported
    }

    override suspend fun getVisibleTopRouteFeatures(layerId: String): List<SuuntoTopRouteFeature> {
        // Not supported
        return emptyList()
    }

    override suspend fun getVisibleStartingPointFeature(
        layerId: String,
        latLng: LatLng
    ): SuuntoStartingPointFeature? {
        // Not supported
        return null
    }

    override suspend fun hasVisibleFeatures(layerId: String): Boolean {
        // Not supported
        return false
    }

    override fun getProviderName(): String = AMapsProvider.NAME

    override fun <R> batchUpdate(block: () -> R): R {
        // Batch operations not supported
        return block()
    }

    override suspend fun getClickLayerSourceIds(
        latLng: LatLng,
        layerIds: List<String>
    ): Set<String> {
        // not supported
        return emptySet()
    }

    override fun setOnCameraIdleListener(listener: GoogleMap.OnCameraIdleListener?) {
        onCameraIdleListener = listener
    }

    override fun setOnCameraMoveListener(listener: GoogleMap.OnCameraMoveListener?) {
        // not supported
    }

    override fun setOnCameraMoveStartedListener(listener: GoogleMap.OnCameraMoveStartedListener?) {
        // not supported
    }

    override fun setOnMap3dModeChangedWithTiltListener(listener: SuuntoMap.OnMap3dModeChangedListener?) {
        // Not supported
    }

    override fun setOnMapClickListener(listener: SuuntoMap.OnMapClickListener?) {
        map.setOnMapClickListener {
            listener?.onMapClick(it.toGoogle())
        }
        map.setOnPOIClickListener {
            listener?.onMapClick(it.coordinate.toGoogle(), it.name)
        }
    }

    override fun setOnMapLongClickListener(listener: SuuntoMap.OnMapLongClickListener?) {
        map.setOnMapLongClickListener {
            listener?.onMapLongClick(it.toGoogle())
        }
    }

    override fun setOnMapMoveListener(listener: SuuntoMap.OnMapMoveListener?) {
    }

    override fun setOnMarkerClickListener(listener: SuuntoMap.OnMarkerClickListener?) {
        if (listener == null) {
            map.setOnMarkerClickListener(null)
        } else {
            map.setOnMarkerClickListener { aMapMarker ->
                getSuuntoMarker(aMapMarker)
                    ?.let { listener.onAnnotationClick(it) }
                    ?: false
            }
        }
    }

    override fun setOnMarkerDragListener(listener: SuuntoMap.OnMarkerDragListener?) {
        if (listener == null) {
            map.setOnMarkerDragListener(null)
        } else {
            map.setOnMarkerDragListener(object : AMap.OnMarkerDragListener {
                override fun onMarkerDragStart(marker: Marker?) {
                    getSuuntoMarker(marker)?.let { listener.onAnnotationDragStart(it) }
                }

                override fun onMarkerDrag(marker: Marker?) {
                    getSuuntoMarker(marker)?.let { listener.onAnnotationDrag(it) }
                }

                override fun onMarkerDragEnd(marker: Marker?) {
                    getSuuntoMarker(marker)?.let { listener.onAnnotationDragEnd(it) }
                }
            })
        }
    }

    override fun setOnScaleListener(listener: SuuntoMap.OnScaleListener?) {
    }

    override fun addRulerLine(options: SuuntoRulerLineOptions): SuuntoPolyline? {
        val bitmap = createDashedPatternBitmap(options)
        val polylineOptions = PolylineOptions()
            .addAll(options.points.map { it.toAMap() })
            .zIndex(options.solidLineZIndex)
            .setUseTexture(true)
            .setCustomTexture(BitmapDescriptorFactory.fromBitmap(bitmap))
        dashPolyline = SuuntoPolyline(AMapPolylineDelegate(map.addPolyline(polylineOptions)))
        return dashPolyline
    }

    private fun createDashedPatternBitmap(options: SuuntoRulerLineOptions): Bitmap {
        val width = options.solidLineWidth.roundToInt()
        val height = width * 6
        val bitmap = createBitmap(width, height)
        val canvas = Canvas(bitmap)
        canvas.drawColor(options.solidLineColor)
        val paint = Paint(Paint.FILTER_BITMAP_FLAG).apply {
            color = options.dashLineColor
        }
        val borderWidth = ((options.solidLineWidth - options.dashLineWidth) / 2).toFloat()
        canvas.drawRect(borderWidth, 0f, width - borderWidth, height / 3f, paint)
        return bitmap
    }

    override fun removeRulerLine() {
        dashPolyline?.remove()
    }

    override fun addColorTrack(descriptor: ColorTrackDescriptor, lineWidth: Double) {
        // do nothing here
    }

    override fun addTopRoutesFeatures(descriptor: TopRouteFeatureDescriptor) {
        // do nothing here
    }

    override fun clearTopRoutesFeatures() {
        // do nothing here
    }

    override suspend fun hasTopRoutesFeatures(latLng: LatLng): Boolean? {
        return null
    }

    internal fun updateMyLocationMarker(options: SuuntoMarkerOptions) {
        if (myLocationMarker == null) {
            myLocationMarker = addMarker(options)
        }
        myLocationMarker?.let { marker ->
            marker.setPosition(options.toAMap().position.toGoogle())
            marker.setRotation(map.cameraPosition.bearing.toAMapRotation())
        }
    }

    private fun getSuuntoMarker(marker: Marker?): SuuntoMarker? =
        marker?.`object` as? SuuntoMarker
}
