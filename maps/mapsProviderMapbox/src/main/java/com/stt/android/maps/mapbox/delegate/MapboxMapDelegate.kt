package com.stt.android.maps.mapbox.delegate

import android.Manifest.permission.ACCESS_COARSE_LOCATION
import android.Manifest.permission.ACCESS_FINE_LOCATION
import android.animation.ValueAnimator
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Rect
import android.os.SystemClock
import androidx.annotation.RequiresPermission
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.GoogleMap.OnCameraMoveStartedListener.REASON_DEVELOPER_ANIMATION
import com.google.android.gms.maps.GoogleMap.OnCameraMoveStartedListener.REASON_GESTURE
import com.google.android.gms.maps.model.LatLng
import com.mapbox.android.gestures.MoveGestureDetector
import com.mapbox.android.gestures.StandardScaleGestureDetector
import com.mapbox.common.Cancelable
import com.mapbox.geojson.Feature
import com.mapbox.geojson.FeatureCollection
import com.mapbox.geojson.LineString
import com.mapbox.geojson.Point
import com.mapbox.maps.CameraChangedCallback
import com.mapbox.maps.CameraOptions
import com.mapbox.maps.CameraState
import com.mapbox.maps.EdgeInsets
import com.mapbox.maps.ImageHolder
import com.mapbox.maps.MapView
import com.mapbox.maps.MapboxMap
import com.mapbox.maps.RenderedQueryGeometry
import com.mapbox.maps.RenderedQueryOptions
import com.mapbox.maps.ScreenBox
import com.mapbox.maps.ScreenCoordinate
import com.mapbox.maps.Style
import com.mapbox.maps.Vec3
import com.mapbox.maps.extension.style.expressions.dsl.generated.has
import com.mapbox.maps.extension.style.expressions.dsl.generated.interpolate
import com.mapbox.maps.extension.style.expressions.generated.Expression
import com.mapbox.maps.extension.style.layers.addLayer
import com.mapbox.maps.extension.style.layers.addLayerAt
import com.mapbox.maps.extension.style.layers.generated.LineLayer
import com.mapbox.maps.extension.style.layers.generated.SymbolLayer
import com.mapbox.maps.extension.style.layers.generated.lineLayer
import com.mapbox.maps.extension.style.layers.getLayerAs
import com.mapbox.maps.extension.style.layers.properties.generated.IconAnchor
import com.mapbox.maps.extension.style.layers.properties.generated.LineCap
import com.mapbox.maps.extension.style.layers.properties.generated.LineJoin
import com.mapbox.maps.extension.style.layers.properties.generated.TextAnchor
import com.mapbox.maps.extension.style.sources.addSource
import com.mapbox.maps.extension.style.sources.generated.GeoJsonSource
import com.mapbox.maps.extension.style.sources.generated.geoJsonSource
import com.mapbox.maps.extension.style.sources.getSourceAs
import com.mapbox.maps.plugin.LocationPuck2D
import com.mapbox.maps.plugin.animation.CameraAnimationsLifecycleListener
import com.mapbox.maps.plugin.animation.CameraAnimatorType
import com.mapbox.maps.plugin.animation.MapAnimationOptions.Companion.mapAnimationOptions
import com.mapbox.maps.plugin.animation.flyTo
import com.mapbox.maps.plugin.attribution.attribution
import com.mapbox.maps.plugin.compass.compass
import com.mapbox.maps.plugin.gestures.OnMapClickListener
import com.mapbox.maps.plugin.gestures.OnMapLongClickListener
import com.mapbox.maps.plugin.gestures.OnMoveListener
import com.mapbox.maps.plugin.gestures.OnScaleListener
import com.mapbox.maps.plugin.gestures.addOnMapClickListener
import com.mapbox.maps.plugin.gestures.addOnMapLongClickListener
import com.mapbox.maps.plugin.gestures.addOnMoveListener
import com.mapbox.maps.plugin.gestures.addOnScaleListener
import com.mapbox.maps.plugin.gestures.removeOnMapClickListener
import com.mapbox.maps.plugin.gestures.removeOnMapLongClickListener
import com.mapbox.maps.plugin.gestures.removeOnMoveListener
import com.mapbox.maps.plugin.gestures.removeOnScaleListener
import com.mapbox.maps.plugin.locationcomponent.LocationComponentConstants
import com.mapbox.maps.plugin.locationcomponent.LocationConsumer
import com.mapbox.maps.plugin.locationcomponent.LocationProvider
import com.mapbox.maps.plugin.locationcomponent.location
import com.mapbox.maps.plugin.scalebar.ScaleBarPlugin
import com.mapbox.maps.plugin.scalebar.scalebar
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.maps.MAP_TYPE_NORMAL
import com.stt.android.maps.StartingPointFeature
import com.stt.android.maps.SuuntoCameraPosition
import com.stt.android.maps.SuuntoCameraUpdate
import com.stt.android.maps.SuuntoCircle
import com.stt.android.maps.SuuntoCircleOptions
import com.stt.android.maps.SuuntoFreeCameraUpdate
import com.stt.android.maps.SuuntoLayerType
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.maps.SuuntoPolyline
import com.stt.android.maps.SuuntoPolylineOptions
import com.stt.android.maps.SuuntoProjection
import com.stt.android.maps.SuuntoRulerLineOptions
import com.stt.android.maps.SuuntoScaleBarOptions
import com.stt.android.maps.SuuntoStartingPointFeature
import com.stt.android.maps.SuuntoTileOverlay
import com.stt.android.maps.SuuntoTileOverlayOptions
import com.stt.android.maps.SuuntoTopRouteFeature
import com.stt.android.maps.SuuntoUiSettings
import com.stt.android.maps.TopRouteFeature
import com.stt.android.maps.TopRouteFeature.Companion.PROPERTY_KEY_ROUTE_IDS
import com.stt.android.maps.TopRouteFeature.Companion.PROPERTY_ROUTE_PREFIX
import com.stt.android.maps.cluster.TopRouteFeatureDescriptor
import com.stt.android.maps.colortrack.ColorTrackDescriptor
import com.stt.android.maps.delegate.MapDelegate
import com.stt.android.maps.location.SuuntoLocationSource
import com.stt.android.maps.mapbox.MapboxMapsProvider
import com.stt.android.maps.mapbox.R
import com.stt.android.maps.mapbox.delegate.manager.Map3dManager
import com.stt.android.maps.mapbox.delegate.manager.MarkerManager
import com.stt.android.maps.mapbox.delegate.manager.PolylineManager
import com.stt.android.maps.mapbox.domain.DemSourceUseCase
import com.stt.android.maps.mapbox.toGoogle
import com.stt.android.maps.mapbox.toMapbox
import com.stt.android.maps.mapbox.toSuunto
import com.stt.android.utils.latToMercatorY
import com.stt.android.utils.lonToMercatorX
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers.Default
import kotlinx.coroutines.Dispatchers.Main
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.lang.Math.toRadians
import kotlin.coroutines.resume
import kotlin.math.cos

class MapboxMapDelegate(
    internal val map: MapboxMap,
    internal val mapView: MapView,
    private val provider: MapboxMapsProvider,
    private val options: SuuntoMapOptions,
    pxToDpFactor: Float,
    demSourceUseCase: DemSourceUseCase,
) : MapDelegate {
    private val scope = CoroutineScope(Main + SupervisorJob())

    private var onScaleListener: OnScaleListener? = null
    private var onMapClickListener: OnMapClickListener? = null
    private var onMapLongClickListener: OnMapLongClickListener? = null
    private var onCameraMoveStartedListener: GoogleMap.OnCameraMoveStartedListener? = null
    private var onCameraMoveListener: GoogleMap.OnCameraMoveListener? = null
    private var onCameraIdleListener: GoogleMap.OnCameraIdleListener? = null
    private var onMapMoveListener: SuuntoMap.OnMapMoveListener? = null
    private var locationSource: SuuntoLocationSource? = null
    private var locationProvider: LocationProvider? = null
    private var mapLoadingErrorCancelable : Cancelable? = null
    private var cameraChangedCancelable : Cancelable? = null

    private val defaultLogoMargins: Rect
    private val defaultAttributionMargins: Rect
    private val defaultCompassMargins: Rect

    internal var mapPadding = EdgeInsets(0.0, 0.0, 0.0, 0.0)
        private set

    internal var mapType: String = options.mapType ?: MAP_TYPE_NORMAL
        private set

    internal val overlays = mutableListOf<MapboxTileOverlayDelegate>()

    private var setMapTypeInProgress = false
    private var pendingSetMapType: Pair<String, (() -> Unit)?>? = null

    private val scaleBarPlugin: ScaleBarPlugin by lazy { mapView.scalebar }
    private val moshi = Moshi.Builder().build()
    private val topRouteFeatureAdapter = moshi.adapter(TopRouteFeature::class.java)
    private val topRouteIdsAdapter =
        moshi.adapter<List<String>>(
            Types.newParameterizedType(
                List::class.java,
                String::class.java
            )
        )

    private var selectedLayerId: String? = null
    private var selectedRouteId: String? = null
    private var touchingMap = false
    private var lastCameraMoveTimestamp = 0L
    internal var cameraIdle = true
        private set
    private var cameraIdleJob: Job? = null
    private var runningAnimatorCount: Int = 0
        set(value) {
            if (field != value) {
                field = value

                if (value == 0) {
                    checkCameraIdle()
                }
            }
        }

    private val polylineManager = PolylineManager(map, mapView, pxToDpFactor)
    private val markerManager = MarkerManager(map, mapView)

    internal val annotationManagers = listOf(polylineManager, markerManager)
    private var mapboxAnnotationManagersCreated = false
    private val viewAnnotationManager = mapView.viewAnnotationManager
    private var solidPolyline: SuuntoPolyline? = null
    private var dashPolyline: SuuntoPolyline? = null
    internal val map3dManager = Map3dManager(
        context = mapView.context.applicationContext,
        scope = scope,
        mapDelegate = this,
        options = options,
        demSourceUseCase = demSourceUseCase,
    )

    private var gestureListener : OnMoveListener? = null
    private val cameraChangedCallBack = CameraChangedCallback {
        lastCameraMoveTimestamp = SystemClock.elapsedRealtime()

        if (cameraIdle) {
            cameraIdle = false
            onCameraMoveStartedListener?.run {
                val reason = if (touchingMap) REASON_GESTURE else REASON_DEVELOPER_ANIMATION
                onCameraMoveStarted(reason)
            }
        }

        onCameraMoveListener?.onCameraMove()

        checkCameraIdle()
    }

    private var cameraAnimationLifecycleListener: CameraAnimationsLifecycleListener? = null

    init {
        val resources = mapView.context.resources
        val fourDp = resources.getDimensionPixelSize(R.dimen.maps_provider_mapbox_four_dp)
        val ninetyTwoDp =
            resources.getDimensionPixelSize(R.dimen.maps_provider_mapbox_ninety_two_dp)
        defaultLogoMargins = Rect(fourDp, fourDp, fourDp, fourDp)
        defaultAttributionMargins = Rect(ninetyTwoDp, fourDp, fourDp, fourDp)
        defaultCompassMargins = Rect(fourDp, fourDp, fourDp, fourDp)
        mapView.attribution.iconColor = Color.WHITE

        gestureListener = object : OnMoveListener {
            override fun onMove(detector: MoveGestureDetector) = false

            override fun onMoveBegin(detector: MoveGestureDetector) {
                touchingMap = true
                onMapMoveListener?.onMapMoveBegin()
            }

            override fun onMoveEnd(detector: MoveGestureDetector) {
                touchingMap = false
                onMapMoveListener?.onMapMoveEnd()
                checkCameraIdle()
            }
        }
        cameraChangedCancelable = map.subscribeCameraChanged(cameraChangedCallBack)
        gestureListener?.let { map.addOnMoveListener(it) }
        cameraAnimationLifecycleListener = object : CameraAnimationsLifecycleListener {
            override fun onAnimatorCancelling(
                type: CameraAnimatorType,
                animator: ValueAnimator,
                owner: String?
            ) {
                // Make sure that animator count does not go below zero.
                // Mapbox sometimes sends extra callbacks for cancelling and ending animations.
                runningAnimatorCount = (runningAnimatorCount - 1).coerceAtLeast(0)
            }

            override fun onAnimatorEnding(
                type: CameraAnimatorType,
                animator: ValueAnimator,
                owner: String?
            ) {
                runningAnimatorCount = (runningAnimatorCount - 1).coerceAtLeast(0)
            }

            override fun onAnimatorInterrupting(
                type: CameraAnimatorType,
                runningAnimator: ValueAnimator,
                runningAnimatorOwner: String?,
                newAnimator: ValueAnimator,
                newAnimatorOwner: String?
            ) {
                // onAnimatorCancelling will follow this call, no need to update
                // runningAnimatorCount here.
            }

            override fun onAnimatorStarting(
                type: CameraAnimatorType,
                animator: ValueAnimator,
                owner: String?
            ) {
                runningAnimatorCount++
            }
        }
        cameraAnimationLifecycleListener?.let {
            map.cameraAnimationsPlugin {
                addCameraAnimationsLifecycleListener(it)
            }
        }

        mapLoadingErrorCancelable = map.subscribeMapLoadingError {
            Timber.w("onMapLoadError (%s): %s", it.type.name, it.message)
            scope.launch {
                setMapTypeInProgress = false
                onSetMapTypeCompleted()
            }
        }
    }

    internal fun onDestroy() {
        for (annotationManager in annotationManagers) {
            annotationManager.onDestroy()
        }
        map3dManager.onDestroy()
        setOnScaleListener(null)
        setOnMapClickListener(null)
        setOnMapLongClickListener(null)
        setOnCameraMoveListener(null)
        setOnCameraMoveStartedListener(null)
        setOnCameraIdleListener(null)
        setOnMapMoveListener(null)
        setLocationSource(null)
        gestureListener?.let { map.removeOnMoveListener(it) }
        gestureListener = null
        cameraAnimationLifecycleListener?.let {
            map.cameraAnimationsPlugin {
                removeCameraAnimationsLifecycleListener(it)
            }
        }
        cameraAnimationLifecycleListener = null
        mapLoadingErrorCancelable?.cancel()
        mapLoadingErrorCancelable = null
        cameraChangedCancelable?.cancel()
        cameraChangedCancelable = null
        scope.cancel()
        viewAnnotationManager.removeAllViewAnnotations()
        cameraIdleJob?.cancel()
        cameraIdleJob = null
        onMapClickListener = null
        onMapLongClickListener = null
        mapView.onDestroy()
    }

    private fun createMapboxAnnotationManagers() {
        if (!mapboxAnnotationManagersCreated) {
            for (annotationManager in annotationManagers) {
                annotationManager.createMapboxAnnotationManagers(
                    belowLayerId = LocationComponentConstants.LOCATION_INDICATOR_LAYER
                )
            }
            mapboxAnnotationManagersCreated = true
        }
    }

    override fun update3dLocation(latLng: LatLng?, altitude: Double) {
        map3dManager.update3dLocation(latLng, altitude)
    }

    override fun addCircle(options: SuuntoCircleOptions): SuuntoCircle {
        // Not supported. Return dummy circle.
        return SuuntoCircle(MapboxCircleDelegate())
    }

    override fun addMarker(options: SuuntoMarkerOptions): SuuntoMarker =
        markerManager.addAnnotation(options)

    override fun addPolyline(options: SuuntoPolylineOptions) =
        polylineManager.addAnnotation(options)

    override fun addTileOverlay(options: SuuntoTileOverlayOptions): SuuntoTileOverlay {
        // Copy the options so that the overlay delegate may safely update them without affecting
        // the original
        val optionsCopy = options.copy()

        return SuuntoTileOverlay(
            MapboxTileOverlayDelegate(optionsCopy, this).also { overlay ->
                overlays.add(overlay)
                map.getStyle { overlay.createSourceAndLayers(it) }
            }
        )
    }

    override fun clear() {
        for (annotationManager in annotationManagers) {
            annotationManager.clear()
        }
    }

    override fun getCameraPosition(): SuuntoCameraPosition? {
        // Calculate center coordinate taking map padding into account.
        val centerX = (mapPadding.left + (map.getSize().width - mapPadding.right)) / 2
        val centerY = (mapPadding.top + (map.getSize().height - mapPadding.bottom)) / 2
        val centerCoordinate = map.coordinateForPixel(ScreenCoordinate(centerX, centerY))

        val cameraState = map.cameraState
        return CameraState(
            centerCoordinate,
            cameraState.padding,
            cameraState.zoom,
            cameraState.bearing,
            cameraState.pitch
        ).toSuunto()
    }

    override fun getLocationSource() = locationSource

    override fun getProjection(): SuuntoProjection {
        return SuuntoProjection(MapboxProjectionDelegate(map))
    }

    override fun getTerrainExaggeration(): Double = map3dManager.currentTerrainExaggeration

    override fun getUiSettings(): SuuntoUiSettings {
        return SuuntoUiSettings(MapboxUiSettingsDelegate(this, mapView))
    }

    override fun isMap3dModeEnabled(): Boolean = map3dManager.map3dMode

    override fun isMap3dModeSupported(): Boolean = true

    override fun isMyLocationEnabled(): Boolean = mapView.location.enabled

    override fun moveCamera(update: SuuntoCameraUpdate) {
        if (update is SuuntoFreeCameraUpdate) {
            map.setCamera(CameraOptions.Builder().padding(mapPadding).build())
            moveFreeCamera(update)
            map3dManager.onMoveCamera(update.cameraPosition)
        } else {
            val options = update.toMapbox(this)
            map.setCamera(options)

            options.center?.run {
                map3dManager.onMoveCamera(this.toGoogle())
            }
        }
    }

    private fun moveFreeCamera(update: SuuntoFreeCameraUpdate) {
        val pos = Vec3(
            lonToMercatorX(update.cameraPosition.longitude),
            latToMercatorY(update.cameraPosition.latitude),
            update.cameraAltitude / (EQUATOR_CIRCUMFERENCE * cos(toRadians(update.cameraPosition.latitude)))
        )
        map.getFreeCameraOptions().apply {
            this.position = pos
            this.setPitchBearing(update.cameraPitch, update.cameraBearing)
            map.setCamera(this)
        }
    }

    override fun animateCamera(
        update: SuuntoCameraUpdate,
        durationMs: Int,
        callback: SuuntoMap.CancelableCallback?
    ) {
        if (update is SuuntoFreeCameraUpdate) {
            throw Exception("animateCamera() does not support SuuntoFreeCameraUpdate.")
        }
        val options = update.toMapbox(this)
        map.flyTo(
            options,
            mapAnimationOptions {
                duration(durationMs.toLong())
            },
            callback?.toMapbox()
        )
        map3dManager.onAnimateCamera(options)
    }

    override fun setLocationSource(source: SuuntoLocationSource?) {
        if (source != locationSource) {
            locationSource = source
            locationProvider = source?.toMapbox()
            mapView.location.setLocationProvider(
                locationProvider ?: object : LocationProvider {
                    // Use dummy location provider if location source is set to null.
                    // setLocationProvider does not allow null value and there is no method for
                    // restoring the default location provider.
                    override fun registerLocationConsumer(locationConsumer: LocationConsumer) {}
                    override fun unRegisterLocationConsumer(locationConsumer: LocationConsumer) {}
                }
            )
        }
    }

    override fun setMap3dModeEnabled(enabled: Boolean) {
        map3dManager.setMap3dMode(enabled)
    }

    override fun setMapType(type: String, onStyleLoaded: (() -> Unit)?) {
        if (!setMapTypeInProgress) {
            setMapType(
                type = type,
                forceReload = false,
                onStyleLoaded = { onStyleLoaded?.invoke() },
            )
        } else {
            pendingSetMapType = Pair(type, onStyleLoaded)
        }
    }

    internal fun setMapType(
        type: String,
        forceReload: Boolean = false,
        onStyleLoaded: Style.OnStyleLoaded? = null,
    ) {
        if (type == mapType && !forceReload) {
            runCatching { onStyleLoaded?.onStyleLoaded(map.style!!) }
            return
        }

        setMapTypeInProgress = true
        mapType = type

        // Invalidate existing sources and layers associated with overlays, because changing the map
        // style will remove them internally. Trying to use the objects after that may cause a crash.
        // New sources and layers will be created for the overlays when the map style has finished loading.
        val currentOverlays = overlays.toList().onEach { it.invalidateSourceAndLayer() }
        map3dManager.invalidateLayers()

        val styleUri = provider.getStyleUri(type)
        Timber.d("Loading map style, uri = $styleUri, 3d = ${map3dManager.map3dMode}")
        map.loadStyle(
            styleUri
        ) { style ->
            for (overlay in currentOverlays) {
                overlay.createSourceAndLayers(style)
            }
            val layerId = selectedLayerId
            val routeId = selectedRouteId
            if (layerId != null && routeId != null) {
                selectFeature(layerId, routeId)
            }

            // Create Mapbox annotation managers only after the style has finished loading.
            // Otherwise some annotation manager settings like icon overlapping may not work as
            // intended.
            createMapboxAnnotationManagers()

            // Let annotation managers make modifications to their layer properties
            polylineManager.onStyleLoaded(style)

            // Let the 3d mode manager extend the style only after annotation managers have been
            // created. If style is extended before annotation managers are created, they may
            // fail to initialize because of a Mapbox issue.
            // See https://github.com/mapbox/mapbox-maps-android/issues/542
            map3dManager.onStyleLoaded(style)

            // Notify style loaded only after processing current events in the event loop.
            // This is needed as a workaround for Mapbox issues.
            scope.launch {
                onStyleLoaded?.onStyleLoaded(style)
                setMapTypeInProgress = false
                onSetMapTypeCompleted()
            }
        }
    }

    private fun onSetMapTypeCompleted() {
        pendingSetMapType?.let { (type, onStyleLoaded) ->
            pendingSetMapType = null
            setMapType(type, onStyleLoaded)
        }
    }

    @RequiresPermission(anyOf = [ACCESS_FINE_LOCATION, ACCESS_COARSE_LOCATION])
    override fun setMyLocationEnabled(enabled: Boolean) {
        mapView.location.locationPuck = LocationPuck2D(
            topImage = ImageHolder.from(com.stt.android.maps.R.drawable.location_puck),
            bearingImage = ImageHolder.from(com.stt.android.maps.R.drawable.location_bearing)
        )
        mapView.location.enabled = enabled
    }

    override fun setOnCameraMoveListener(listener: GoogleMap.OnCameraMoveListener?) {
        onCameraMoveListener = listener
    }

    override fun setOnCameraIdleListener(listener: GoogleMap.OnCameraIdleListener?) {
        onCameraIdleListener = listener
    }

    override fun setOnMap3dModeChangedWithTiltListener(
        listener: SuuntoMap.OnMap3dModeChangedListener?
    ) {
        map3dManager.onMap3dModeChangedWithTiltListener = listener
    }

    override fun setOnMapClickListener(listener: SuuntoMap.OnMapClickListener?) {
        onMapClickListener?.let { map.removeOnMapClickListener(it) }
        onMapClickListener = null
        listener?.let {
            map.addOnMapClickListener(
                OnMapClickListener { point ->
                    listener.onMapClick(point.toGoogle())
                    false
                }.also { onMapClickListener = it }
            )
        }
    }

    override fun setOnMapLongClickListener(listener: SuuntoMap.OnMapLongClickListener?) {
        onMapLongClickListener?.let { map.removeOnMapLongClickListener(it) }
        onMapLongClickListener = null
        listener?.let {
            map.addOnMapLongClickListener(
                OnMapLongClickListener { point ->
                    listener.onMapLongClick(point.toGoogle())
                    false
                }.apply { onMapLongClickListener = this }
            )
        }
    }

    override fun setOnMapMoveListener(listener: SuuntoMap.OnMapMoveListener?) {
        onMapMoveListener = listener
    }

    override fun setOnCameraMoveStartedListener(listener: GoogleMap.OnCameraMoveStartedListener?) {
        onCameraMoveStartedListener = listener
    }

    override fun setOnMarkerClickListener(listener: SuuntoMap.OnMarkerClickListener?) {
        markerManager.onAnnotationClickListener = listener
    }

    override fun setOnMarkerDragListener(listener: SuuntoMap.OnMarkerDragListener?) {
        markerManager.onAnnotationDragListener = listener
    }

    override fun setPadding(left: Int, top: Int, right: Int, bottom: Int) {
        getUiSettings().setLogoMargins(
            left + defaultLogoMargins.left,
            top + defaultLogoMargins.top,
            right + defaultLogoMargins.right,
            bottom + defaultLogoMargins.bottom
        )
        getUiSettings().setAttributionMargins(
            left + defaultAttributionMargins.left,
            top + defaultAttributionMargins.top,
            right + defaultAttributionMargins.right,
            bottom + defaultAttributionMargins.bottom
        )
        getUiSettings().setCompassMargins(
            left + defaultCompassMargins.left,
            top + defaultCompassMargins.top,
            right + defaultCompassMargins.right,
            bottom + defaultCompassMargins.bottom
        )

        mapPadding = EdgeInsets(
            top.toDouble(),
            left.toDouble(),
            bottom.toDouble(),
            right.toDouble()
        )
    }

    override fun snapshot(callback: SuuntoMap.SnapshotReadyCallback, bitmap: Bitmap?) {
        // Ignore the bitmap parameter. Mapbox does not support providing a preallocated bitmap.
        mapView.snapshot { callback.onSnapshotReady(it) }
    }

    fun addIconToStyle(bitmap: Bitmap, name: String): String {
        return name.also {
            map.getStyle { style ->
                style.addImage(it, bitmap)
            }
        }
    }

    fun onTileOverlayRemoved(overlay: MapboxTileOverlayDelegate) {
        overlays.remove(overlay)
    }

    override fun showScaleBar(options: SuuntoScaleBarOptions) {
        scaleBarPlugin.updateSettings {
            options.refreshInterval?.let { refreshInterval = it }
            options.textColor?.let { textColor = it }
            options.primaryColor?.let { primaryColor = it }
            options.secondaryColor?.let { secondaryColor = it }
            options.marginTop?.let { marginTop = it }
            options.marginLeft?.let { marginLeft = it }
            options.barHeight?.let { height = it }
            options.barMaxWidthRatio?.let { ratio = it }
            options.borderWidth?.let { borderWidth = it }
            options.textSize?.let { textSize = it }
            options.textBarMargin?.let { textBarMargin = it }
            options.metricUnit?.let { isMetricUnits = it }
        }

        scaleBarPlugin.enabled = true
    }

    override fun removeScaleBar() {
        if (<EMAIL> != true) {
            scaleBarPlugin.enabled = false
        }
    }

    override fun setOnScaleListener(listener: SuuntoMap.OnScaleListener?) {
        onScaleListener?.let { map.removeOnScaleListener(it) }
        onScaleListener = null
        listener?.let {
            onScaleListener = (
                object : OnScaleListener {
                    override fun onScaleBegin(detector: StandardScaleGestureDetector) {
                        listener.onScaleBegin()
                    }

                    override fun onScale(detector: StandardScaleGestureDetector) {}

                    override fun onScaleEnd(detector: StandardScaleGestureDetector) {
                        listener.onScaleEnd()
                    }
                }
                ).apply { map.addOnScaleListener(this) }
        }
    }

    override fun <R> batchUpdate(block: () -> R): R {
        for (annotationManager in annotationManagers) {
            annotationManager.beginBatch()
        }
        return try {
            block()
        } finally {
            for (annotationManager in annotationManagers) {
                annotationManager.endBatch()
            }
        }
    }

    override suspend fun selectFeature(layerId: String, latLng: LatLng): SuuntoTopRouteFeature? {
        return runSuspendCatching {
            val features = queryFeatures(
                latLng,
                layerId
            )

            val topRouteFeature = withContext(Default) {
                val feature = features.firstOrNull { it.hasProperty(PROPERTY_KEY_ROUTE_IDS) }
                feature?.let {
                    topRouteIdsAdapter.fromJson(it.getStringProperty(PROPERTY_KEY_ROUTE_IDS))
                        ?.firstOrNull()
                        ?.let { routeId ->
                            topRouteFeatureAdapter.fromJson(
                                it.getStringProperty(
                                    PROPERTY_ROUTE_PREFIX + routeId
                                )
                            )
                        }
                }
            }

            if (topRouteFeature != null) {
                selectedLayerId = layerId
                selectedRouteId = topRouteFeature.route_id

                map.getStyle { style ->
                    style.getLayerAs<LineLayer>(SuuntoLayerType.SELECTED_TOP_ROUTES.getLayerId())
                        ?.filter(
                            has(topRouteFeature.propertyKey)
                        )
                }
            }

            topRouteFeature?.run { SuuntoTopRouteFeature(MapboxTopRouteFeatureDelegate(this)) }
        }.getOrElse { e ->
            Timber.w(e, "Selecting a feature failed.")
            null
        }
    }

    override fun selectFeature(layerId: String, routeId: String) {
        map.getStyle { style ->
            selectedLayerId = layerId
            selectedRouteId = routeId
            style.styleLayers
                .filter { it.id.startsWith(layerId) }
                .forEach { layerInfo ->
                    style.getLayerAs<LineLayer>(layerInfo.id)
                        ?.filter(has(PROPERTY_ROUTE_PREFIX + routeId))
                }
        }
    }

    override suspend fun getVisibleTopRouteFeatures(
        layerId: String
    ): List<SuuntoTopRouteFeature> = withContext(Default) {
        val features = queryVisibleFeatures(layerId)
        val isHandled = HashSet<String>()
        features.asSequence()
            .flatMap { feature ->
                topRouteIdsAdapter.fromJson(
                    feature.getStringProperty(
                        PROPERTY_KEY_ROUTE_IDS
                    )
                )?.mapNotNull { routeId ->
                    if (isHandled.contains(routeId)) {
                        null
                    } else {
                        isHandled.add(routeId)
                        topRouteFeatureAdapter.fromJson(
                            feature.getStringProperty(PROPERTY_ROUTE_PREFIX + routeId)
                        )
                    }
                } ?: emptyList()
            }
            .distinctBy { it.route_id }
            .map { SuuntoTopRouteFeature(MapboxTopRouteFeatureDelegate(it)) }
            .toList()
    }

    override suspend fun getVisibleStartingPointFeature(
        layerId: String,
        latLng: LatLng
    ): SuuntoStartingPointFeature? {
        val feature = queryFeatures(latLng, layerId).firstOrNull() ?: return null
        val point = feature.geometry() as? Point
        val popularity =
            feature.getIntProperty(StartingPointFeature.PROPERTY_KEY_POPULARITY)
        val localMax = feature.getIntProperty(StartingPointFeature.PROPERTY_KEY_LOCAL_MAX)
        return if (point != null && popularity != null && localMax != null) {
            SuuntoStartingPointFeature(
                MapboxStartingPointFeatureDelegate(
                    StartingPointFeature(
                        LatLng(point.latitude(), point.longitude()),
                        popularity = popularity,
                        localMax = localMax
                    )
                )
            )
        } else {
            null
        }
    }

    private fun latLngToScreenBox(latLng: LatLng): ScreenBox {
        val screenLocation = map.pixelForCoordinate(latLng.toMapbox())
        // Increase the area to query features from to help the user to hit a route
        val extraPadding = 90

        return ScreenBox(
            ScreenCoordinate(
                screenLocation.x - extraPadding,
                screenLocation.y - extraPadding
            ),
            ScreenCoordinate(
                screenLocation.x + extraPadding,
                screenLocation.y + extraPadding
            )
        )
    }

    private fun Feature.getIntProperty(propertyKey: String): Int? {
        return if (hasProperty(propertyKey)) {
            val property = getProperty(propertyKey)
            try {
                property.asInt
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }

    private suspend fun queryVisibleFeatures(
        layerId: String
    ): List<Feature> = withContext(Main) {
        runSuspendCatching {
            val padding = map.cameraState.padding
            val left = mapView.left + padding.left
            val top = mapView.top.toFloat() + padding.top
            val right = mapView.right.toFloat() - padding.right
            val bottom = mapView.bottom.toFloat() - padding.bottom

            val screenBox =
                ScreenBox(ScreenCoordinate(left, top), ScreenCoordinate(right, bottom))
            queryFeatures(screenBox, layerId)
        }.getOrElse { e ->
            Timber.w(e, "Query visible features failed.")
            emptyList()
        }
    }

    override suspend fun hasVisibleFeatures(
        layerId: String
    ): Boolean = queryVisibleFeatures(layerId).isNotEmpty()

    private suspend fun queryFeatures(latLng: LatLng, layerId: String): List<Feature> {
        return queryFeatures(latLngToScreenBox(latLng), layerId)
    }

    private suspend fun queryFeatures(screenBox: ScreenBox, layerId: String): List<Feature> {
        return withContext(Main) {
            suspendCancellableCoroutine { continuation ->
                try {
                    map.queryRenderedFeatures(
                        RenderedQueryGeometry(screenBox),
                        RenderedQueryOptions(listOf(layerId), null)
                    ) { expected ->
                        continuation.resumeWith(
                            kotlin.runCatching {
                                if (expected.isValue) {
                                    expected.value?.map {
                                        it.queriedFeature.feature
                                    } ?: emptyList()
                                } else {
                                    if (expected.isError) {
                                        Timber.w(
                                            expected.error,
                                            "ScreenBox query to rendered features failed."
                                        )
                                    }
                                    emptyList()
                                }
                            }
                        )
                    }
                } catch (t: Throwable) {
                    Timber.w(t, "Call to queryFeatures(ScreenBox, String) threw an error.")
                    continuation.cancel(t)
                }
            }
        }
    }

    override fun setCompassEnabled(enabled: Boolean) {
        mapView.compass.enabled = enabled
    }

    override fun getProviderName(): String = MapboxMapsProvider.NAME

    override suspend fun getClickLayerSourceIds(
        latLng: LatLng,
        layerIds: List<String>
    ): Set<String> {
        return withContext(Main) {
            suspendCancellableCoroutine { continuation ->
                try {
                    map.queryRenderedFeatures(
                        RenderedQueryGeometry(map.pixelForCoordinate(latLng.toMapbox())),
                        RenderedQueryOptions(layerIds, null)
                    ) { expected ->
                        continuation.resumeWith(
                            kotlin.runCatching {
                                if (expected.isValue) {
                                    expected.value?.map {
                                        it.queriedFeature.source
                                    }?.toSet() ?: emptySet()
                                } else {
                                    if (expected.isError) {
                                        Timber.w(
                                            expected.error,
                                            "query to rendered features failed."
                                        )
                                    }
                                    emptySet()
                                }
                            }
                        )
                    }
                } catch (t: Throwable) {
                    Timber.w(
                        t,
                        "Call to queryFeatures(RenderedQueryGeometry, RenderedQueryOptions) threw an error."
                    )
                    continuation.cancel(t)
                }
            }
        }
    }

    override fun addRulerLine(options: SuuntoRulerLineOptions): SuuntoPolyline? {
        solidPolyline = polylineManager.addAnnotation(
            SuuntoPolylineOptions(options.points)
                .color(options.solidLineColor)
                .width(options.solidLineWidth)
                .zIndex(options.solidLineZIndex)
        )
        dashPolyline = polylineManager.addAnnotation(
            SuuntoPolylineOptions(options.points)
                .color(options.dashLineColor)
                .zIndex(options.dashLineZIndex)
                .width(options.dashLineWidth.toFloat())
                .rulerLine(true)
        )
        return dashPolyline
    }

    override fun removeRulerLine() {
        solidPolyline?.remove()
        dashPolyline?.remove()
    }

    override fun addColorTrack(descriptor: ColorTrackDescriptor, lineWidth: Double) {
        map.removeStyleLayer(COLOR_TRACK_LAYER)
        map.removeStyleSource(COLOR_TRACK_SOURCE)
        map.addSource(geoJsonSource(COLOR_TRACK_SOURCE) {
            val points = descriptor.points.map { Point.fromLngLat(it.longitude, it.latitude) }
            featureCollection(
                FeatureCollection.fromFeatures(
                    listOf(Feature.fromGeometry(LineString.fromLngLats(points)))
                )
            )
            lineMetrics(true)
        })
        val index = map.styleLayers.indexOfFirst {
            it.id.startsWith(SuuntoLayerType.ANNOTATION.layerIdPrefix)
        }
        val layer = lineLayer(COLOR_TRACK_LAYER, COLOR_TRACK_SOURCE) {
            lineGradient(interpolate {
                linear()
                lineProgress()
                descriptor.colors.forEach {
                    stop(it.fraction) { rgb(it.red, it.green, it.blue) }
                }
            })
            lineCap(LineCap.ROUND)
            lineJoin(LineJoin.ROUND)
            lineWidth(lineWidth)
        }
        if (index != -1) {
            map.addLayerAt(layer, index)
        } else {
            map.addLayer(layer)
        }
    }

    private fun checkCameraIdle() {
        if (cameraIdle || touchingMap || runningAnimatorCount > 0 || cameraIdleJob?.isActive == true) return

        // Send camera idle notification when the required time has elapsed since the last camera
        // movement and the user is not touching the map.
        cameraIdleJob = scope.launch {
            var waitTime: Long
            do {
                if (!isActive) return@launch
                waitTime =
                    lastCameraMoveTimestamp + CAMERA_IDLE_TIMEOUT_MS - SystemClock.elapsedRealtime()
                if (waitTime > 0) {
                    delay(waitTime)
                }
            } while (waitTime > 0 && isActive)

            if (!touchingMap && runningAnimatorCount == 0) {
                setCameraIdle()
                map3dManager.onCameraIdle()
            }
        }
    }

    private fun setCameraIdle() {
        cameraIdle = true
        onCameraIdleListener?.onCameraIdle()
    }

    override fun addTopRoutesFeatures(descriptor: TopRouteFeatureDescriptor) {
        val featureCollection = FeatureCollection.fromFeatures(
            descriptor.points.map { point ->
                Feature.fromGeometry(Point.fromLngLat(point.latLng.longitude, point.latLng.latitude)).apply {
                    addStringProperty("marker_type", point.markerType)
                }
            }
        )

        map.getStyle { style ->
            style.addImage(TOP_ROUTE_CLUSTER_ICON, descriptor.clusterMarkerBitmap)
            style.addImage(TOP_ROUTE_CLUSTER_LARGE_ICON, descriptor.largeClusterMarkerBitmap)

            descriptor.normalMarkerBitmapMap.forEach { (type, bitmap) ->
                style.addImage("${TOP_ROUTE_NORMAL_ICON}_$type", bitmap)
            }

            val existingSource = style.getSourceAs<GeoJsonSource>(TOP_ROUTE_CLUSTER_SOURCE)
            if (existingSource != null) {
                existingSource.featureCollection(featureCollection)
            } else {
                val clusterSource = geoJsonSource(TOP_ROUTE_CLUSTER_SOURCE) {
                    featureCollection(featureCollection)
                    cluster(true)
                    clusterRadius(60)
                    clusterMaxZoom(12)
                }
                map.addSource(clusterSource)
            }

            val clusterLayer = style.getLayerAs<SymbolLayer>(TOP_ROUTE_CLUSTER_LAYER)
            if (clusterLayer == null) {
                val clusterMarkerLayer = SymbolLayer(TOP_ROUTE_CLUSTER_LAYER, TOP_ROUTE_CLUSTER_SOURCE).apply {
                    iconImage(
                        Expression.switchCase(
                            Expression.gt(Expression.get(TOP_ROUTE_CLUSTER_POINT_COUNT), Expression.literal(99)),
                            Expression.literal(TOP_ROUTE_CLUSTER_LARGE_ICON),
                            Expression.literal(TOP_ROUTE_CLUSTER_ICON)
                        )
                    )
                    iconAnchor(IconAnchor.CENTER)
                    textAllowOverlap(true)
                    filter(Expression.has(TOP_ROUTE_CLUSTER_POINT_COUNT))
                    textAnchor(TextAnchor.CENTER)
                    descriptor.options?.let { option ->
                        if (option.showText) {
                            textField(
                                Expression.switchCase(
                                    Expression.gt(Expression.get(TOP_ROUTE_CLUSTER_POINT_COUNT), Expression.literal(99)),
                                    Expression.literal("99+"),
                                    Expression.toString(Expression.get(TOP_ROUTE_CLUSTER_POINT_COUNT))
                                )
                            )
                            option.textSize?.let { textSize(it.toDouble()) }
                            option.textColor?.let { textColor(it) }
                            option.textHaloColor?.let { textHaloColor(it) }
                            option.textHaloWidth?.let { textHaloWidth(it.toDouble()) }
                            option.textOffset?.let { textOffset(it) }
                        }
                    }
                }
                map.addLayer(clusterMarkerLayer)
            }

            descriptor.normalMarkerBitmapMap.keys.forEach { type ->
                val normalLayerId = "${TOP_ROUTE_NORMAL_LAYER}_$type"
                val normalLayer = style.getLayerAs<SymbolLayer>(normalLayerId)
                if (normalLayer == null) {
                    val normalMarkerLayer = SymbolLayer(normalLayerId, TOP_ROUTE_CLUSTER_SOURCE).apply {
                        iconImage(Expression.concat(Expression.literal(TOP_ROUTE_NORMAL_ICON + "_"), Expression.get("marker_type")))
                        iconAnchor(IconAnchor.BOTTOM)
                        iconAllowOverlap(true)
                        filter(
                            Expression.all(
                                Expression.not(Expression.has(TOP_ROUTE_CLUSTER_POINT_COUNT)),
                                Expression.eq(Expression.get("marker_type"), Expression.literal(type))
                            )
                        )
                    }
                    map.addLayer(normalMarkerLayer)
                }
            }
        }
    }

    override fun clearTopRoutesFeatures() {
        map.getStyle { style ->
            val layerIds = style.styleLayers.map { it.id }.filter {
                it.startsWith(TOP_ROUTE_NORMAL_LAYER)
            }
            layerIds.forEach { layerId ->
                map.removeStyleLayer(layerId)
            }
        }
        map.removeStyleLayer(TOP_ROUTE_CLUSTER_LAYER)
        map.removeStyleSource(TOP_ROUTE_CLUSTER_SOURCE)
    }

    override suspend fun hasTopRoutesFeatures(latLng: LatLng): Boolean? {
        val topRoutesClusterFeatures = queryFeatures(latLng, TOP_ROUTE_CLUSTER_LAYER)
        if (topRoutesClusterFeatures.isEmpty()) {
            val topRoutesNormalFeatures = suspendCancellableCoroutine {
                map.getStyle { style ->
                    val layerIds = style.styleLayers.map { it.id }.filter {
                        it.startsWith(TOP_ROUTE_NORMAL_LAYER)
                    }
                    it.resume(layerIds)
                }
            }.flatMap { layerId ->
                queryFeatures(latLng, layerId)
            }
            return if (topRoutesNormalFeatures.isEmpty()) null else false
        }
        return topRoutesClusterFeatures[0].hasProperty(TOP_ROUTE_CLUSTER_POINT_COUNT)
    }

    companion object {
        const val TILE_JSON_VERSION = "2.2.0"
        const val CAMERA_IDLE_TIMEOUT_MS = 100L
        const val TILT_ANIMATION_DURATION_MS = 600L
        const val MAP_2D_TO_3D_PITCH_THRESHOLD = 5f
        const val MAP_3D_TO_2D_PITCH_THRESHOLD = 0f
        private const val EQUATOR_CIRCUMFERENCE = 40075017

        private const val COLOR_TRACK_SOURCE = "app-color-track-source"
        private const val COLOR_TRACK_LAYER = "app-color-track-layer"

        private const val TOP_ROUTE_CLUSTER_SOURCE = "top-route-app-cluster-source"
        private const val TOP_ROUTE_CLUSTER_LAYER = "top-route-app-cluster-layer"
        private const val TOP_ROUTE_NORMAL_LAYER = "top-route-app-normal-layer"
        private const val TOP_ROUTE_CLUSTER_ICON = "top-route-app-cluster-icon"
        private const val TOP_ROUTE_CLUSTER_LARGE_ICON = "top-route-app-cluster-large-icon"
        private const val TOP_ROUTE_NORMAL_ICON = "top-route-app-normal-icon"
        private const val TOP_ROUTE_CLUSTER_POINT_COUNT = "point_count"
    }
}
