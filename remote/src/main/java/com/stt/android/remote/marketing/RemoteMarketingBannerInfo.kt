package com.stt.android.remote.marketing

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class RemoteMarketingBannerInfo(
    @<PERSON><PERSON>(name = "bannerId") val bannerId: Long,
    @<PERSON><PERSON>(name = "displayOrder") val displayOrder: Int = 0,
    @<PERSON><PERSON>(name = "name") val name: String = "",
    @<PERSON><PERSON>(name = "title") val title: String = "",
    @<PERSON><PERSON>(name = "previewUrl") val previewUrl: RemotePreviewUrl,
    @<PERSON><PERSON>(name = "locationId") val location: RemoteLocation? = null,
    @<PERSON><PERSON>(name = "startTime") val startTime: Long,
    @<PERSON><PERSON>(name = "endTime") val endTime: Long,
    @<PERSON><PERSON>(name = "linkType") val linkType: RemoteLinkType? = null,
    @<PERSON><PERSON>(name = "link") val link: String? = null,
) {
    @JsonClass(generateAdapter = true)
    data class RemotePreviewUrl(
        @Json(name = "m") val phone: String,
    )

    enum class RemoteLocation {
        @Json(name = "HomeView")
        HOME_VIEW,

        @<PERSON><PERSON>(name = "SuuntoPlusStoreView")
        SUUNTO_PLUS_STORE_VIEW,
    }
}
