package com.stt.android.domain

import com.suunto.algorithms.data.Energy
import com.suunto.algorithms.data.Energy.Companion.cal
import java.time.Clock
import java.time.LocalTime
import java.time.temporal.ChronoUnit

object EnergyUtil {
    /**
     * Scale the given [bmr] (basal metabolic rate) to the current time of day. Returns the number of kilocalories
     * burned so far today.
     *
     * For example, at 3 o'clock in the afternoon, the returned value is 15/24=62.5% of the total BMR.
     */
    fun scaledBmrForToday(bmr: Energy, clock: Clock): Energy {
        val secondOfDay = LocalTime.now(clock).toSecondOfDay()
        return (bmr.inCal * secondOfDay / ChronoUnit.DAYS.duration.seconds).cal
    }
}
